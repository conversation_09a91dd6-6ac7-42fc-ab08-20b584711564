// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import type {Theme, ThemeKey} from 'mattermost-redux/selectors/entities/preferences';

const Preferences = {
    APP_BAR: 'app_bar',
    CATEGORY_CHANNEL_OPEN_TIME: 'channel_open_time',
    CATEGORY_CHANNEL_APPROXIMATE_VIEW_TIME: 'channel_approximate_view_time',
    CATEGORY_DIRECT_CHANNEL_SHOW: 'direct_channel_show',
    CATEGORY_GROUP_CHANNEL_SHOW: 'group_channel_show',
    CATEGORY_FLAGGED_POST: 'flagged_post',
    CATEGORY_AUTO_RESET_MANUAL_STATUS: 'auto_reset_manual_status',
    CATEGORY_NOTIFICATIONS: 'notifications',
    COLLAPSED_REPLY_THREADS: 'collapsed_reply_threads',
    COLLAPSED_REPLY_THREADS_OFF: 'off',
    COLLAPSED_REPLY_THREADS_ON: 'on',
    COLLAPSED_REPLY_THREADS_FALLBACK_DEFAULT: 'off',
    COMMENTS: 'comments',
    COMMENTS_ANY: 'any',
    COMMENTS_ROOT: 'root',
    COMMENTS_NEVER: 'never',
    EMAIL: 'email',
    EMAIL_INTERVAL: 'email_interval',
    INTERVAL_FIFTEEN_MINUTES: 15 * 60,
    INTERVAL_HOUR: 60 * 60,
    INTERVAL_IMMEDIATE: 30,

    // "immediate" is a 30 second interval
    INTERVAL_NEVER: 0,
    INTERVAL_NOT_SET: -1,
    CATEGORY_DISPLAY_SETTINGS: 'display_settings',
    NAME_NAME_FORMAT: 'name_format',
    DISPLAY_PREFER_NICKNAME: 'nickname_full_name',
    DISPLAY_PREFER_FULL_NAME: 'full_name',
    DISPLAY_PREFER_USERNAME: 'username',
    MENTION_KEYS: 'mention_keys',
    USE_MILITARY_TIME: 'use_military_time',

    CATEGORY_CUSTOM_STATUS: 'custom_status',
    NAME_CUSTOM_STATUS_TUTORIAL_STATE: 'custom_status_tutorial_state',
    NAME_RECENT_CUSTOM_STATUSES: 'recent_custom_statuses',
    CUSTOM_STATUS_MODAL_VIEWED: 'custom_status_modal_viewed',

    CATEGORY_SIDEBAR_SETTINGS: 'sidebar_settings',
    CHANNEL_SIDEBAR_ORGANIZATION: 'channel_sidebar_organization',
    LIMIT_VISIBLE_DMS_GMS: 'limit_visible_dms_gms',
    SHOW_UNREAD_SECTION: 'show_unread_section',
    CATEGORY_ADVANCED_SETTINGS: 'advanced_settings',
    ADVANCED_FILTER_JOIN_LEAVE: 'join_leave',
    ADVANCED_CODE_BLOCK_ON_CTRL_ENTER: 'code_block_ctrl_enter',
    ADVANCED_SEND_ON_CTRL_ENTER: 'send_on_ctrl_enter',
    ADVANCED_SYNC_DRAFTS: 'sync_drafts',
    CATEGORY_WHATS_NEW_MODAL: 'whats_new_modal',
    HAS_SEEN_SIDEBAR_WHATS_NEW_MODAL: 'has_seen_sidebar_whats_new_modal',

    CATEGORY_PERFORMANCE_DEBUGGING: 'performance_debugging',
    NAME_DISABLE_CLIENT_PLUGINS: 'disable_client_plugins',
    NAME_DISABLE_TELEMETRY: 'disable_telemetry',
    NAME_DISABLE_TYPING_MESSAGES: 'disable_typing_messages',

    UNREAD_SCROLL_POSITION: 'unread_scroll_position',
    UNREAD_SCROLL_POSITION_START_FROM_LEFT: 'start_from_left_off',
    UNREAD_SCROLL_POSITION_START_FROM_NEWEST: 'start_from_newest',

    CATEGORY_UPGRADE_CLOUD: 'upgrade_cloud',
    SYSTEM_CONSOLE_LIMIT_REACHED: 'system_console_limit_reached',

    NEW_CHANNEL_WITH_BOARD_TOUR_SHOWED: 'channel_with_board_tip_showed',

    CATEGORY_ONBOARDING: 'category_onboarding',

    CATEGORY_DRAFTS: 'drafts',
    DRAFTS_TOUR_TIP_SHOWED: 'drafts_tour_tip_showed',

    CATEGORY_REPORTING: 'reporting',

    HIDE_BATCH_EXPORT_CONFIRM_MODAL: 'hide_batch_export_confirm_modal',
    HIDE_MYSQL_STATS_NOTIFICATION: 'hide_mysql_stats_notifcation',

    CATEGORY_OVERAGE_USERS_BANNER: 'overage_users_banner',

    CATEGORY_THEME: 'theme',
    THEMES: {
        quartz: {
            type: 'الوضع الفاتح',
            awayIndicator: '#e6bb27',
            buttonBg: '#24A1DE',
            buttonColor: '#ffffff',
            centerChannelBg: '#ffffff',
            centerChannelColor: '#0d0d0d',
            codeTheme: 'github',
            dndIndicator: '#dc362e',
            errorTextColor: '#fd5960',
            linkColor: '#1c58d9',
            mentionBg: '#24A1DE',
            mentionBj: '#24A1DE',
            mentionColor: '#ffffff',
            mentionHighlightBg: '#ffffff',
            mentionHighlightLink: '#24A1DE',
            newMessageSeparator: '#24A1DE',
            onlineIndicator: '#3db887',
            sidebarBg: '#ffffff',
            sidebarHeaderBg: '#ffffff',
            sidebarHeaderTextColor: '#0d0d0d',
            sidebarTeamBarBg: '#cccccc',
            sidebarText: '#0d0d0d',
            sidebarTextActiveBorder: '#24A1DE',
            sidebarTextActiveColor: '#0d0d0d',
            sidebarTextHoverBg: '#f3f3f3',
            sidebarUnreadText: '#24A1DE',
        },

        denim: {
            type: 'الوضع الليلي',
            sidebarBg: '#202228',
            sidebarText: '#ffffff',
            sidebarUnreadText: '#ffffff',
            sidebarTextHoverBg: '#25262a',
            sidebarTextActiveBorder: '#24A1DE',
            sidebarTextActiveColor: '#ffffff',
            sidebarHeaderBg: '#24272d',
            sidebarHeaderTextColor: '#ffffff',
            sidebarTeamBarBg: '#292c33',
            onlineIndicator: '#3db887',
            awayIndicator: '#f5ab00',
            dndIndicator: '#d24b4e',
            mentionBg: '#24A1DE',
            mentionBj: '#24A1DE',
            mentionColor: '#ffffff',
            centerChannelBg: '#292c33',
            centerChannelColor: '#e3e4e8',
            newMessageSeparator: '#1adbdb',
            linkColor: '#1c58d9',
            buttonBg: '#24A1DE',
            buttonColor: '#ffffff',
            errorTextColor: '#da6c6e',
            mentionHighlightBg: '#0d6e6e',
            mentionHighlightLink: '#24A1DE',
            codeTheme: 'monokai',
        },
    } as unknown as Record<ThemeKey, Theme>,
    RECENT_EMOJIS: 'recent_emojis',
};

export default Preferences;
