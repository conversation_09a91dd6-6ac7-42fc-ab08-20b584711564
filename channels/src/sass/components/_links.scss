@use "utils/variables";

@use 'sass:color';

a {
    color: var(--sofa-color);
    cursor: pointer;
    text-decoration: none;
    word-break: break-word;

    &:hover,
    &:focus {
        // color: variables.$primary-color--hover;
        outline: 0;
    }
}

button.style--link {
    padding: 0;
    border: none;
    background: none;
    color: var(--link-color);
    cursor: pointer;
    line-height: normal;
    word-break: break-word;

    &:hover,
    &:focus {
        text-decoration: underline;
    }
}

.text-danger {
    color: color.adjust(variables.$red, $saturation: -20%);

    &:hover,
    &:focus {
        color: color.adjust(variables.$red, $saturation: -20%);
    }
}
