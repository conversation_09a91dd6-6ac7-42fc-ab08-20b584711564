@use "utils/mixins";
@use "utils/variables";

.help-text {
    display: block;
    margin: 10px 0 0;
    color: var(--center-channel-color-rgb);

    ul,
    ol {
        padding-left: 23px;
    }

    a {
        opacity: 0.5;
    }

    .help-link {
        margin-right: 5px;
    }

    .btn {
        font-size: 13px;
    }

    &.remove-filename {
        position: relative;
        top: -2px;
        margin-bottom: 5px;
    }
}

.form-control {
    border-radius: var(--radius-xs);

    html[dir="ltr"] & {
        text-align: left;
    }
    html[dir="rtl"] & {
        text-align: right;
    }

    &:focus {
        box-shadow: none;
    }

    &.no-padding {
        padding: 0;
        line-height: 32px;
    }

    &.no-resize {
        resize: none;
    }

    &[disabled],
    [readonly] {
        @include mixins.alpha-property(background, variables.$white, 0.1);

        color: inherit;
        cursor: auto;
    }
}

fieldset {
    &[disabled] {
        .form-control {
            @include mixins.alpha-property(background, variables.$white, 0.1);

            color: inherit;
            cursor: auto;
        }
    }
}

.app__body {
    fieldset[disabled] {
        color: var(--center-channel-color);
    }
}

.admin-textarea {
    resize: none;
}

input {
    &[type='radio'],
    &[type='checkbox'] {
        margin-top: 3px;
        font-size: 14px;
        accent-color: var(--sofa-color);
    }
}

input::-webkit-file-upload-button {
    display: none;
}

::-webkit-input-placeholder { /* Chrome/Opera/Safari */
    color: inherit;
    opacity: 0.5;
}

::-moz-placeholder { /* Firefox 19+ */
    color: inherit;
    opacity: 0.5;
}

:-ms-input-placeholder { /* IE 10+ */
    color: inherit;
    opacity: 0.5;
}

:-moz-placeholder { /* Firefox 18- */
    color: inherit;
    opacity: 0.5;
}

.input-group-addon {
    color: var(--center-channel-color);
}

// This class is used to make the input field look like a dropdown to be used in menus
.inputWithMenu {
    cursor: pointer;

    input.form-control {
        width: 100px;
        cursor: pointer;

        &:read-only {
            background-color: variables.$white;
        }
    }
}
