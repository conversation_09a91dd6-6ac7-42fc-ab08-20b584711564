@use "utils/functions";
@use "utils/variables";

@use 'sass:color';

#ldap_groups {
    overflow: visible;
}

.groups-list {
    padding: 2rem 2rem 2rem 2rem;
    transition: all 0.4s ease-in-out 0s;

    &.groups-list-less-padding {
        padding: 0;
    }

    .groups-list--global-actions {
        position: relative;
        display: flex;
        padding: 8px 20px;
        background: variables.$white;

        .group-list-search {
            position: relative;
            display: flex;
            max-width: 280px;
            height: 30px;
            flex-grow: 1;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 15px;
            margin-top: 2px;
            stroke: variables.$dark-gray;

            input {
                width: 100%;
                border: 0;
                margin-right: 15px;
                margin-left: 25px;
                font-size: 0.95em;

                &:dir(rtl) {
                    margin-right: 25px;
                    margin-left: 15px;
                }
            }

            input:focus {
                outline: none;
            }

            .group-filter-action {
                padding: 8px 10px 0 0;

                &:dir(rtl) {
                    padding: 8px 0 0 10px;
                }
                &.hidden {
                    visibility: hidden;
                }
            }
        }

        .group-list-link-unlink {
            display: flex;
            flex-grow: 1;
            justify-content: flex-end;
        }

        .group-search-filters {
            position: absolute;
            top: 40px;
            width: 303px;
            padding: 20px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            background-color: white;

            .search-groups-btn {
                right: 20px;
                bottom: 20px;
                float: right;
            }

            .cancel-filters {
                float: right;
            }

            .filter-row {
                height: 30px;
                vertical-align: middle;
            }

            .filter-check {
                display: inline-block;
                width: 18px;
                height: 18px;
                border: 2px solid variables.$dark-gray;
                border-radius: 3px;
                margin-right: 7px;
                vertical-align: middle;

                &.checked {
                    border: 0;

                    svg {
                        background: variables.$white;
                        fill: variables.$primary-color;
                    }
                }
            }
        }
    }

    .groups-list--header {
        display: flex;
        flex-direction: row;
        padding: 0.5em 0;
        border-bottom: solid 1px rgba(0, 0, 0, 0.1);
        font-size: 1.1em;
        font-weight: bold;

        .group-name {
            margin-left: 16px;
        }

        .group-description {
            display: block;
            width: 150px;
            color: variables.$black;
            opacity: 1;

            &.adjusted {
                width: 100px;
            }
        }

        .group-actions {
            width: 130px;
        }
    }

    .groups-list--body {
        background: variables.$bg--gray;
        cursor: pointer;

        .groups-list-loading {
            padding: 40px;
            text-align: center;
        }

        .groups-list-empty {
            margin-top: 1rem;
            background: functions.alpha-color(variables.$white, 0.5);
            color: variables.$gray;
            font-size: 1.6rem;
            text-align: center;

            span {
                display: inline-block;
                width: 100%;
                height: 100%;
                padding-top: 20px;
                padding-bottom: 20px;
            }
        }
    }

    .groups-list--footer {
        display: flex;
        height: auto;
        align-items: center;
        justify-content: flex-end;
        padding: 5px;
        border-top: solid 1px rgba(0, 0, 0, 0.1);
        background: variables.$white;

        .btn-tertiary {
            color: variables.$dark-gray;

            &.disabled {
                opacity: 0.5;
            }
        }

        .counter {
            margin-bottom: 2px;
            color: variables.$dark-gray;
            font-size: 1.1em;
        }
    }

    .group-check {
        display: flex;
        min-width: 18px;
        min-height: 18px;
        align-items: center;
        justify-content: space-around;
        border: 2px solid variables.$dark-gray;
        border-radius: 3px;
        margin: 5px 5px 4px 14px;

        &.checked {
            border: 0;

            svg {
                background: variables.$white;
                fill: variables.$primary-color;
            }
        }
    }

    .group-name {
        display: flex;
        flex: 1.8;
        flex-grow: 1;
        align-items: center;
    }

    .group-content {
        display: flex;
        flex: 1;
        padding-left: 20px;
    }

    .group-description {
        display: flex;
        overflow: hidden;
        width: 150px;
        height: 100%;
        align-items: center;
        opacity: 0.75;

        &.adjusted {
            width: 120px;
        }

        &.roles {
            overflow: visible;
        }

        i {
            margin-right: 5px;
        }

        >a {
            padding: 3px 5px;
            color: variables.$black;
            text-decoration: none;

            &:hover {
                border-radius: 3px;
                background-color: variables.$light-gray;
            }

            &.warning {
                color: variables.$red;
            }
        }
    }

    .group-description-link {
        >a {
            &:hover {
                background-color: transparent;
                text-decoration: underline;
            }
        }
    }

    .group-actions {
        width: 130px;
        padding-right: 20px;
        text-align: right;
    }

    .group {
        &.checked {
            background-color: color.adjust(variables.$primary-color, $lightness: 40%);

            .group-description {
                opacity: 1;
            }
        }

        &:nth-child(even) {
            background: functions.alpha-color(variables.$white, 0.5);
        }

        &.checked:nth-child(even) {
            background-color: color.adjust(variables.$primary-color, $lightness: 40%);
        }

        .group-row {
            .group-name {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }

    .group-row {
        display: flex;
        height: 34px;
        flex-direction: row;
        align-items: center;
        border: 2px solid variables.$transparent;

        &:hover {
            border-left: 2px solid variables.$primary-color;
            background-color: color.adjust(variables.$primary-color, $lightness: 45%);

            .group-description {
                opacity: 1;
            }
        }

        &.selected {
            animation-duration: 3s;
            animation-name: rowhighlight;
        }

        &.group-row-large {
            height: 80px;
        }
    }

    .channel-icon {
        opacity: 0.8;
    }
}

#groupDisplayName {
    &.group-profile {
        padding-bottom: 0;
    }
}

#groupMention {
    .group-profile {
        padding-top: 0;
    }

    .group-at-mention-input {
        padding-left: 26px;
    }
}

.group-profile {
    padding: 20px;
}

.group-users {
    .group-users--header {
        padding: 10px 20px;
        background: #333;
        color: variables.$white;

        a {
            color: variables.$white;
            text-decoration: underline;
        }
    }

    .group-users--body {
        position: relative;
        margin: 15px 15px 0 15px;

        .group-users-loading {
            position: absolute;
            display: none;
            width: 100%;
            height: 100%;
            background: variables.$light-gray;
            text-align: center;

            &.active {
                display: flex;
                align-items: center;
                justify-content: center;

                .fa-spinner {
                    max-width: 10%;
                }
            }
        }

        .group-users-empty {
            padding: 20px;
            background: functions.alpha-color(variables.$white, 0.5);
            color: variables.$gray;
            font-size: 1.5em;
            text-align: center;
        }

        .group-users-row {
            &:nth-child(even) {
                background: functions.alpha-color(variables.$white, 0.5);
            }

            display: flex;
            align-items: center;
            padding: 10px 15px;

            .profile-picture {
                width: 32px;
                height: 32px;
                border-radius: 100%;
                margin: 5px 10px;
            }

            .user-data {
                margin-left: 10px;

                .name-row {
                    display: flex;
                }

                .display-name,
                .email {
                    color: variables.$gray;
                }
            }

            .email-label {
                margin-right: 5px;
            }
        }
    }

    .group-users--footer {
        display: flex;
        height: auto;
        align-items: center;
        justify-content: flex-end;
        padding: 5px;
        margin: 0 15px 15px 15px;
        background: variables.$white;

        &.empty {
            height: 0;
            padding: 0;
            border-top: 0;
        }

        .btn-tertiary {
            color: variables.$dark-gray;

            &.disabled {
                opacity: 0.5;
            }
        }

        .counter {
            margin-bottom: 2px;
            color: variables.$dark-gray;
            font-size: 1.1em;
        }
    }
}

.channel-moderation {
    .channel-moderation--body {
        padding: 20px;

        .channel-moderation--table {
            width: 100%;

            th {
                &:first-child {
                    width: 100%;
                    padding-left: 0;
                }

                padding: 6px 20px;
                border-bottom: solid 1px functions.alpha-color(#ccc, 0.75);
            }

            tr {
                &:not(:last-child) {
                    border-bottom: solid 1px functions.alpha-color(#ccc, 0.75);
                }

                &:last-child {
                    td {
                        padding-bottom: 0;
                    }
                }
            }

            td {
                padding: 20px 0;

                &:first-child {
                    padding-left: 0;
                }

                &:not(:first-child) {
                    text-align: center;
                    vertical-align: middle;
                }
            }

            .checkbox {
                display: inline-block;
                width: 18px;
                height: 18px;
                padding: 0;
                border: 2px solid variables.$dark-gray;
                border-radius: 3px;
                background: none;

                > * {
                    pointer-events: none;
                }

                &.disabled {
                    border-color: variables.$light-gray;
                }

                &.checked {
                    border: 0;

                    svg {
                        fill: #24A1DE;
                    }
                }
            }
        }
    }
}

.group-teams-and-channels {
    .group-teams-and-channels-loading {
        padding: 40px;
        text-align: center;
    }

    .group-teams-and-channels-empty {
        padding: 20px;
        background: functions.alpha-color(variables.$white, 0.5);
        color: variables.$gray;
        font-size: 1.5em;
        text-align: center;
    }

    .group-teams-and-channels--header {
        font-size: 1.1em;
        font-weight: bold;
    }

    .group-teams-and-channels--body {
        padding: 20px;
        .col-sm-10{
            &:dir(rtl){  float: right !important;}
        }
        .group-teams-and-channels-row.has-children {
            .type {
                margin-left: 20px;
            }
        }

        .group-teams-and-channels-row {
            &:nth-child(even) {
                background: functions.alpha-color(variables.$white, 0.5);
            }

            .team-icon,
            .channel-icon {
                width: 16px;
                height: 16px;
                margin: 0 7px 0 4px;
                color: inherit;
                opacity: 0.7;
            }

            .arrow-icon {
                .fa {
                    padding: 0 4px;
                    cursor: pointer;
                }
            }

            .channel-icon {
                margin: 1px 2px 0 20px;
            }

            .team-icon-private {
                font-size: 0.35em;
            }

            .actions {
                button {
                    padding: 5px;
                }
            }

            .type {
                flex: 1;
                margin-left: -29px;
                color: #454448;
                opacity: 50%;
            }

            .name-no-arrow {
                margin-left: 16px;
            }

            .name-content {
                display: flex;
                flex: 1;

                .name {
                    flex: 1;
                }

                .arrow-icon {
                    margin-left: 10px;

                    .fa-caret-right {
                        padding: 5px;
                        cursor: pointer;
                    }

                    .open {
                        transform: rotate(90deg);
                        transform-origin: 55% 50%;
                        transition: transform 300ms;
                    }
                }
            }

            .remove {
                height: 30px;
            }
        }

        .teams-list {
            .teams-list--header {
                display: flex;
                justify-content: space-between;
                border-bottom: 1px solid variables.$dark-gray;
                font-size: 1.1em;
                font-weight: bold;
            }
        }
    }
}

.group-settings {
    .group-settings--body {
        padding: 0 20px 20px 20px;
    }
}

.group-profile-add-menu {
    position: relative;
    margin-top: -36px;
    margin-left: 10px;

    .fa-caret-down {
        margin-left: 5px;
    }

    .add-team-or-channel-menu {
        position: absolute;
        top: 33px;
        width: 100%;
        padding: 5px 8px;
        border: 1px solid variables.$gray;
        border-radius: 2px;
        background: variables.$white;

        a {
            display: block;
            padding: 7px;
            color: variables.$dark-gray;
        }
    }
}

.group-user-profile-picture {
    width: 32px;
    height: 32px;
    border-radius: 100%;
    margin: 5px 10px;
}

.group-users-to-remove-modal-description {
    padding: 16px;
    background-color: #f5f5f5;
}

.group-description.group-user-removal-column {
    height: 40px;
}

#team_groups .group-row .group-description,
#channel_groups .group-row .group-description {
    width: 170px;
}

.section-separator {
    position: relative;
    z-index: 0;
    height: 4rem;
    margin: 0;
    text-align: center;

    hr {
        position: relative;
        z-index: 5;
        top: 2rem;
        border-color: #333;
        margin: 0;
        opacity: 0.12;
    }
}

.email-group-row {
    overflow: hidden;
    width: 90%;
    text-overflow: ellipsis;
}

.group-users--header-padded {
    padding-left: 24px;
}

.icon-over-input {
    position: absolute;
    padding-top: 9px;
    padding-left: 12px;
    opacity: 0.8;
}

.AdminChannelDetails_archiveContainer {
    position: relative;
    left: -22px;
    display: block;
    width: 920px;
    padding-top: 20px;
    padding-left: 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    margin-top: 20px;
}

.AdminChannelDetails_archiveContainer .ArchiveButton {
    padding: 9px 16px;
}

.AdminChannelDetails_archiveContainer .channel-icon__archive,
.AdminChannelDetails_archiveContainer .channel-icon__unarchive {
    width: 16px;
    height: 16px;
}

$archived-color: #166de0;
$unarchived-color: #f74343;

.ArchiveButton___archived {
    border-color: rgba($archived-color, 1);
    color: rgba($archived-color, 1);

    svg {
        fill: rgba($archived-color, 1);
    }
}

.ArchiveButton___unarchived {
    border-color: rgba($unarchived-color, 1);
    color: rgba($unarchived-color, 1);

    svg {
        fill: rgba($unarchived-color, 1);
    }
}

.ArchiveButton {
    border-width: 1px;
    border-style: solid;
    border-radius: 4px;
    background-color: transparent;
}

.ArchiveButton___unarchived:hover {
    background-color: rgba($unarchived-color, 0.06);
    color: rgba($unarchived-color, 1);
}

.ArchiveButton___archived:hover {
    background-color: rgba($archived-color, 0.06);
    color: rgba($archived-color, 1);
}

.ArchiveButton:focus,
.ArchiveButton:focus:active,
.ArchiveButton:active {
    outline: none;
}

.ArchiveButton___archived:focus {
    color: rgba($archived-color, 1);
}

.ArchiveButton___unarchived:focus {
    color: rgba($unarchived-color, 1);
}

.ArchiveButton___archived:focus:active {
    background-color: rgba($archived-color, 0.12);
}

.ArchiveButton___unarchived:focus:active {
    background-color: rgba($unarchived-color, 0.12);
}

.ArchiveButton:active {
    box-shadow: none;
}
