.channel-view {
    .badge {
        position: relative;
        display: flex;
        min-width: 18px;
        height: 18px;
        align-items: center;
        justify-content: center;
        padding: 0 5px;
        border-radius: 100px;
        font-size: 11px;
        line-height: 18px;
    }

    .team-sidebar,
    .sidebar--left,
    #SidebarContainer {
        .active .badge,
        .badge {
            background: var(--mention-bg);
            color: var(--mention-color);

            &.urgent {
                background-color: var(--dnd-indicator);
                color: #fff;
            }
        }
    }

    .team-sidebar,
    #SidebarContainer {
        .scheduledPostwidth{ width: 25px !important;}
        .badge {
            padding: 0px;
            gap: 10px;
            width: 19px;
            height: 19px;
            background: #24A1DE ; 
            border-radius: 4px;
            flex: none;
            order: 0;
            flex-grow: 0;
        

            &.badge-max-number {
                padding: 0 4px 0 5px;
            }
        }
    }

    .team-sidebar {
        .badge {
            position: absolute;
            z-index: 5;
            top: -3px;
            right: -3px;
            display: inline-block;
            flex-shrink: 0;
            box-shadow: 0 0 0 3px var(--sidebar-teambar-bg);
        }
    }

    #SidebarContainer {
        .badge {
            display: inline-block;
            flex-shrink: 0;
            margin: 4px ;
        }
    }

    .navbar-toggle {
        .badge {
            position: absolute;
            top: 10px;
            left: 8px;
        }
    }
}
