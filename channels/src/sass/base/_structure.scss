@use "utils/mixins";
@use "utils/variables";
.eTyVFy {
  background: var(--sofa-color) !important;
}
html,
body {
  height: 100%;
}
.font--open_sans.enable-animations.console__body.announcement-bar--fixed {
  .user-profile-popover {
    background: white !important;
    hr {
      border-color: rgba(0, 0, 0, 0.15) !important;
    }
    .btn.btn-icon {
      color: black;
    }
  }
  .react-select .react-select__menu{
    background: #fff;
  }
  .systemUsersFilterPopoverContainer {
    color: var(--sys-center-channel-color) !important;
    background-color: var(--sys-center-channel-bg) !important;
    #header {
      color: var(--sys-center-channel-color) !important;
    }
    .asyncTeamSelectInput__controlContainer {
      color: var(--sys-center-channel-color) !important;
      .DropDown__control {
        background-color: transparent !important;
      }
    }
    .asyncTeamSelectInput__option.focused > div {
      color: var(--sys-center-channel-color) !important;
    }
    .DropdownInput__option.selected > div {
      color: var(--sys-center-channel-color) !important;
    }
    .DropDown__menu {
      .asyncTeamSelectInput__option:hover {
        background-color: var(--sys-denim-button-bg);
      }
      .DropDown__option:hover {
        background-color: var(--sys-denim-button-bg);
      }
    }
    .DropDown__menu {
      color: var(--sys-center-channel-color) !important;
    }
    .DropdownInput__controlContainer {
      .DropDown__control {
        background-color: transparent !important;
      }
    }
    .DropDown__single-value {
      color: var(--sys-center-channel-color) !important;
    }
  }
  .MuiPaper-root{
    li{ span{ color: var(--sys-center-channel-color) !important;}}
    color: var(--sys-center-channel-color) !important;
    background-color: var(--sys-center-channel-bg) !important;
  }
}
body {
  position: relative;
  width: 100%;
  background: variables.$bg--gray;

  &.sticky {
    .container-fluid {
      overflow: auto;
      -webkit-overflow-scrolling: touch;
    }
  }

  &.admin-onboarding {
    background-image: url("images/Vector.svg");
    background-repeat: no-repeat;
    background-size: cover;
  }
}

*:focus {
  outline: none;
}

.sticky {
  background: variables.$white;

  > .channel-view {
    overflow: auto;
    height: 100vh;
    padding: 0 15px;
  }

  .inner-wrap {
    width: 100%;

    > .row {
      &.content {
        min-height: calc(100vh - 89px);
        background-position: center;
        background-repeat: no-repeat;
        background-size: cover;
        background-image: url("components/login/imagelogin/Vector.svg");
        @media (max-width: 768px) {
          min-height: calc(100vh - 187px);
        }
      }
    }
  }
}

.help-page {
  overflow: hidden;
  padding: 3em 0;
}

.inner-wrap {
  height: 100%;

  &.sticky {
    overflow: auto;
  }

  > .row {
    &.main {
      position: relative;
      height: 100%;
    }
  }
}

.container-fluid {
  @include mixins.pie-clearfix;

  position: relative;
  height: 100%;
}

.channel-view {
  @include mixins.clearfix;

  position: relative;
  height: 100%;
}

/* stylelint-disable -- grid-template indentation */

body.app__body {
  background-color: var(--sidebar-header-bg);
}

body.admin-onboarding #root {
  &.channel-view {
    display: flex;
    background-color: transparent;
  }
}

.app__body {
  #root.channel-view {
    &:has(.backstage-body) {
      background-color: variables.$bg--gray;
    }
  }
}

#root,
body.app__body #root {
  display: grid;
  overflow: hidden;
  min-height: 100%;

  --columns: min-content minmax(385px, 1fr) min-content;
  grid-template:
    "announcement announcement announcement" min-content
    "admin-announcement admin-announcement admin-announcement" min-content
    "header header header" min-content
    "team-sidebar main app-sidebar"
    "footer footer footer" min-content / var(--columns);

  > :only-child {
    grid-area: main;
    width: 100%;
  }

  &.console__root {
    background-color: inherit;
    padding-bottom: 0;
    padding-right: 0;
    grid-template:
      "announcement announcement" min-content
      "admin-announcement admin-announcement" min-content
      "header header" min-content
      "lhs center"
      "footer footer" min-content;
    grid-template-columns: auto 1fr; /* lhs takes its content width, center takes remaining space */
  }

  &.container-fluid {
    background: none;
    display: block;
  }

  .announcement-bar {
    grid-area: announcement;
  }

  .announcement-bar.admin-announcement {
    grid-area: admin-announcement;
  }
  #global-header {
    background-color: var(--sidebar-header-bg);
    border-radius: 11px;
    font-family: "GraphikArabic";
    font-weight: normal;
    font-style: normal;
    max-height: 60px;
    margin-top: 1px;
    margin-bottom: 2px;
    grid-area: header;
    border-top: var(--border-default);
    grid-area: header;
  }

  .team-sidebar {
    grid-area: team-sidebar;
  }

  .main-wrapper {
    position: relative;
    display: grid;
    overflow: hidden;
    border: var(--border-light);
    margin: 0 4px 4px 4px;
    border-radius: var(--radius-l);
    grid-area: main;
    grid-template: "lhs center rhs";
    grid-template-columns: min-content minmax(385px, 1fr) min-content;
    @media screen and (max-width: 768px) {
    }

    &:has(.backstage-body) {
      background: inherit;
      padding: 0;
      margin: 0;
      border-radius: 0;
      grid-template:
        "header" min-content
        "main";
    }

    &:has(#playbooks-backstageRoot) {
      margin: 0;
      border-radius: 0;
    }
  }

  &:has(.app-bar) {
    .main-wrapper {
      margin-right: 0;
    }
  }

  &:has(.team-sidebar) {
    .main-wrapper {
      &:dir(ltr) {
        margin-left: 0;
      }
      &:dir(rtl) {
        margin-right: 0;
      }
    }
  }

  #SidebarContainer {
    grid-area: lhs;
  }
  .sidbaercard {
    background-size: cover;
    background-position: top;
    font-family: "GraphikArabic";
    font-weight: 400;
    font-style: normal;

    @media screen and (max-width: 768px) {
      display: none;
    }
  }
  #channel_view,
  .product-wrapper {
    grid-area: center;
    overflow: visible;
  }

  .product-wrapper {
    overflow: auto;
  }

  .sidebar--right--width-holder {
    grid-area: rhs;
    transform: none;
  }

  #sidebar-right {
    top: 0;
    &:lang(ar) {
      left: 0;
      border-radius: 6px 0 0 6px;
    }

    // قواعد خاصة باللغة الإنجليزية
    &:lang(en) {
      right: 0;
    }

    @media screen and (min-width: 768px) {
      position: absolute;
    }
  }

  .app-bar {
    grid-area: app-sidebar;
  }
  .app-bar__top .app-bar__icon span:not(.pulsating_dot) {
    background-color: transparent !important;
  }
  &:not(.console__root) {
    @media screen and (max-width: 768px) {
      padding-bottom: 0;
      padding-right: 0;
      z-index: 17;
      grid-template:
        "announcement" min-content
        "admin-announcement" min-content
        "header" min-content
        "main" auto
        "footer" min-content / auto;
      .team-sidebar,
      .app-bar {
        grid-area: main;
      }

      #SidebarContainer.move--right {
        position: relative;
      }
      .sidbaercard.move--right {
        position: relative;
      }
      .main-wrapper {
        margin: 0;
        grid-template: "main";
        padding: 0;
        border-radius: 0;

        #channel_view,
        #SidebarContainer,
        .product-wrapper,
        #sidebar-right {
          grid-area: main;
          border-radius: 0;
        }

        .channel-view {
          border-radius: 0;
          border: none;
          box-shadow: none;
        }
      }
    }
  }

  @media screen and (min-width: 768px) and (max-width: 1200px) {
    &.rhs-open-expanded {
      .sidebar--right--width-holder {
        display: none;
      }

      #sidebar-right {
        position: absolute;
        width: 100%;
        grid-area: center;
        transition: width 0.25s ease-in-out;
        border-radius: var(--radius-l);
      }

      --columns: min-content 1fr min-content;
    }

    #sidebar-right {
      transition: none;
    }
  }
}

/* stylelint-enable -- grid-template indentation */

#channel_view.channel-view {
  overflow: hidden;
  border-radius: var(--radius-l);
  border-left: var(--border-light);
  background: var(--center-channel-bg);
  box-shadow: var(--elevation-1);
}

.rhs-open #channel_view.channel-view,
.rhs-open-expanded #channel_view.channel.view {
  @media screen and (min-width: 1200px) {
    &:lang(ar) {
      padding-left: 20px;
      margin-left: -20px;
    }
    &:lang(en) {
      padding-right: 20px;
      margin-right: -20px;
    }
  }
}

img {
  max-width: 100%;
}
