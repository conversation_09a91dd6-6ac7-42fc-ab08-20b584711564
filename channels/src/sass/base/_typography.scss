@use "utils/mixins";
@use "utils/variables";

b,
strong {
    font-weight: 400;
    font-family: 'GraphikArabic';
}

a {
    color: variables.$primary-color;
    cursor: pointer;
    text-decoration: none;
    word-break: break-word;

    &:focus,
    &:hover {
        color: var(--sofa-color);
    }
}

.color--link {
    color: variables.$link-color;
    cursor: pointer;

    &:hover,
    &:focus {
        text-decoration: underline;
    }
}

body {
    @include mixins.font-smoothing;

    font-family: 'GraphikArabic', sans-serif;
}

h1,
h2,
h3 {
    font-family: 'GraphikArabic', sans-serif;

    html:lang(ar) & {
        font-family: 'GraphikArabic', sans-serif;
    }
}

h1 {
    font-weight: 600;
}

.is-empty:empty {
    display: none;
}

label {
    &.has-error {
        color: variables.$red;
        font-weight: normal;
    }
}

.small {
    font-size: 12px;
}

.light {
    opacity: 0.73;
}

.word-break--all {
    word-break: break-all;
}

.whitespace--nowrap {
    white-space: nowrap;
}

.whitespace--normal {
    white-space: normal;
}

.overflow--ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
}

ul,
ol {
   
    margin-top: 3px;
    margin-bottom: 0.11em;
    padding-left: 22px;

}

.fa {
    &.fa-1x {
        font-size: 1.6rem;
    }
}

.font-weight--normal {
    font-weight: normal;
}

.font-weight--semibold {
    font-weight: 600;
}

.cursor--pointer {
    cursor: pointer;
}

// Metropolis font definitions
@font-face {
    font-family: 'Metropolis';
    font-style: normal;
    font-weight: 600;
    font-display: swap;
    src: url('../fonts/Metropolis-SemiBold.woff') format('woff');
}

@font-face {
    font-family: 'Metropolis';
    font-style: italic;
    font-weight: 600;
    font-display: swap;
    src: url('../fonts/Metropolis-SemiBoldItalic.woff') format('woff');
}

@font-face {
    font-family: 'Metropolis';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url('../fonts/Metropolis-Regular.woff') format('woff');
}

@font-face {
    font-family: 'Metropolis';
    font-style: italic;
    font-weight: 400;
    src: url('../fonts/Metropolis-RegularItalic.woff') format('woff');
}

@font-face {
    font-family: 'Metropolis';
    font-style: normal;
    font-weight: 300;
    font-display: swap;
    src: url('../fonts/Metropolis-Light.woff') format('woff');
}

@font-face {
    font-family: 'Metropolis';
    font-style: italic;
    font-display: swap;
    font-weight: 300;
    src: url('../fonts/Metropolis-LightItalic.woff') format('woff');
}

/* open-sans-300 - vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic */
@font-face {
    font-family: 'GraphikArabic';
    font-style: normal;
    font-weight: 300;
    src:
        local(''),
        url('../fonts/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-300.woff2') format('woff2'), /* Chrome 26+, Opera 23+, Firefox 39+ */
        url('../fonts/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-300.woff') format('woff'); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
        font-display: swap;
    }

/* open-sans-300italic - vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic */
@font-face {
    font-family: 'GraphikArabic';
    font-style: italic;
    font-weight: 300;
    src:
        local(''),
        url('../fonts/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-300italic.woff2') format('woff2'), /* Chrome 26+, Opera 23+, Firefox 39+ */
        url('../fonts/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-300italic.woff') format('woff'); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
        font-display: swap;
    }

/* open-sans-regular - vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic */
@font-face {
    font-family: 'GraphikArabic';
    font-style: normal;
    font-weight: 400;
    src:
        local(''),
        url('../fonts/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-regular.woff2') format('woff2'), /* Chrome 26+, Opera 23+, Firefox 39+ */
        url('../fonts/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-regular.woff') format('woff'); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
        font-display: swap;
    }

/* open-sans-italic - vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic */
@font-face {
    font-family: 'GraphikArabic';
    font-style: italic;
    font-weight: 400;
    src:
        local(''),
        url('../fonts/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-italic.woff2') format('woff2'), /* Chrome 26+, Opera 23+, Firefox 39+ */
        url('../fonts/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-italic.woff') format('woff'); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
        font-display: swap;
    }

/* open-sans-600italic - vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic */
@font-face {
    font-family: 'GraphikArabic';
    font-style: italic;
    font-weight: 600;
    src:
        local(''),
        url('../fonts/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-600italic.woff2') format('woff2'), /* Chrome 26+, Opera 23+, Firefox 39+ */
        url('../fonts/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-600italic.woff') format('woff'); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
        font-display: swap;
    }

/* open-sans-600 - vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic */
@font-face {
    font-family: 'GraphikArabic';
    font-style: normal;
    font-weight: 600;
    src:
        local(''),
        url('../fonts/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-600.woff2') format('woff2'), /* Chrome 26+, Opera 23+, Firefox 39+ */
        url('../fonts/open-sans-v18-vietnamese_latin-ext_latin_greek-ext_greek_cyrillic-ext_cyrillic-600.woff') format('woff'); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
        font-display: swap;
    }

.app__body.font--open_sans {
    font-family: 'GraphikArabic', sans-serif;

    html:lang(ar) & {
        font-family: 'GraphikArabic', sans-serif;
    }
}

.app__body {
    h1,
    h2,
    h3 {
        font-family: 'GraphikArabic', sans-serif;
        
        html:lang(ar) & {
            font-family: 'GraphikArabic', sans-serif;
        }
    }
}

.color--link--adminack {
    padding-left: 20px;
    color: variables.$white;
    cursor: pointer;
    text-decoration: underline;

    &:hover,
    &:focus {
        text-decoration: underline;
    }
}

// Import GraphikArabic font files
@font-face {
    font-family: 'GraphikArabic';
    font-style: normal;
    font-weight: 100;
    font-display: swap;
    src: url('../fonts/GraphikArabic-Thin.woff2') format('woff2'),
         url('../fonts/GraphikArabic-Thin.woff') format('woff'),
         url('../fonts/GraphikArabic-Thin.ttf') format('truetype'),
         url('../fonts/GraphikArabic-Thin.eot');
}

@font-face {
    font-family: 'GraphikArabic';
    font-style: normal;
    font-weight: 200;
    font-display: swap;
    src: url('../fonts/GraphikArabic-Extralight.woff2') format('woff2'),
         url('../fonts/GraphikArabic-Extralight.woff') format('woff'),
         url('../fonts/GraphikArabic-Extralight.ttf') format('truetype'),
         url('../fonts/GraphikArabic-Extralight.eot');
}

@font-face {
    font-family: 'GraphikArabic';
    font-style: normal;
    font-weight: 300;
    font-display: swap;
    src: url('../fonts/GraphikArabic-Light.woff2') format('woff2'),
         url('../fonts/GraphikArabic-Light.woff') format('woff'),
         url('../fonts/GraphikArabic-Light.ttf') format('truetype'),
         url('../fonts/GraphikArabic-Light.eot');
}

@font-face {
    font-family: 'GraphikArabic';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url('../fonts/GraphikArabic-Regular.woff2') format('woff2'),
         url('../fonts/GraphikArabic-Regular.woff') format('woff'),
         url('../fonts/GraphikArabic-Regular.ttf') format('truetype'),
         url('../fonts/GraphikArabic-Regular.eot');
}

@font-face {
    font-family: 'GraphikArabic';
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: url('../fonts/GraphikArabic-Medium.woff2') format('woff2'),
         url('../fonts/GraphikArabic-Medium.woff') format('woff'),
         url('../fonts/GraphikArabic-Medium.ttf') format('truetype'),
         url('../fonts/GraphikArabic-Medium.eot');
}

@font-face {
    font-family: 'GraphikArabic';
    font-style: normal;
    font-weight: 600;
    font-display: swap;
    src: url('../fonts/GraphikArabic-Semibold.woff2') format('woff2'),
         url('../fonts/GraphikArabic-Semibold.woff') format('woff'),
         url('../fonts/GraphikArabic-Semibold.ttf') format('truetype'),
         url('../fonts/GraphikArabic-Semibold.eot');
}

@font-face {
    font-family: 'GraphikArabic';
    font-style: normal;
    font-weight: 700;
    font-display: swap;
    src: url('../fonts/GraphikArabic-Bold.woff2') format('woff2'),
         url('../fonts/GraphikArabic-Bold.woff') format('woff'),
         url('../fonts/GraphikArabic-Bold.ttf') format('truetype'),
         url('../fonts/GraphikArabic-Bold.eot');
}

// @font-face {
//     font-family: 'GraphikArabic';
//     font-style: normal;
//     font-weight: 800;
//     src: url('../fonts/GraphikArabic-Super.woff2') format('woff2'),
//          url('../fonts/GraphikArabic-Super.woff') format('woff'),
//          url('../fonts/GraphikArabic-Super.ttf') format('truetype'),
//          url('../fonts/GraphikArabic-Super.eot');
// }

// @font-face {
//     font-family: 'GraphikArabic';
//     font-style: normal;
//     font-weight: 900;
//     src: url('../fonts/GraphikArabic-Black.woff2') format('woff2'),
//          url('../fonts/GraphikArabic-Black.woff') format('woff'),
//          url('../fonts/GraphikArabic-Black.ttf') format('truetype'),
//          url('../fonts/GraphikArabic-Black.eot');
// }
