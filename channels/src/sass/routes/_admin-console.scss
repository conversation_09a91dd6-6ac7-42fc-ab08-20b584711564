@use "utils/functions";
@use "utils/mixins";
@use "utils/variables";

@use 'sass:color';
.col-sm-offset-4 {
  &:dir(rtl) {
    margin-right: 33.33333333% !important;
    margin-left: 0% !important;
  }
}
.admin-console__wrapper {
  overflow: auto;
}

.admin-console {
  overflow: auto;
  height: 100%;
  color: black;
  grid-area: center;

  > div {
    width: 100%;
    height: 100%;
  }

  .wrapper--fixed {
    display: flex;
    height: 100%;
    flex-direction: column;

    // Fix for Safari on iOS MM-24361
    @supports (-webkit-touch-callout: none) {
      height: -webkit-fill-available;
    }
  }

  .admin-console__wrapper {
    position: relative;
    height: 100%;
    flex: 1 1 auto;
    padding: 20px;
  .DropDown__menu{
     color:black;
   }
    > .wrapper--admin {
      display: flex;
      overflow: auto;
      height: 100%;
      flex-direction: column;
    }

    .admin-console__filters-rows {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      padding-block-end: 12px;
      padding-block-start: 24px;
      padding-inline-end: 24px;
      padding-inline-start: 24px;
      row-gap: 12px;
    }

    .admin-console__container {
      background-color: var(--sys-center-channel-bg);
      box-shadow: 0 2px 3px 0 functions.alpha-color(variables.$black, 0.08);
    }
  }

  // .admin-console__content {
  //     max-width: 920px;
  // }

  .admin-console__checkbox-list {
    padding: 0;
    margin: 0;
    list-style: none;

    li > .alert {
      margin-top: 4px;
      margin-bottom: 10px;
    }
    .radio input[type="radio"],
    .radio-inline input[type="radio"],
    .checkbox input[type="checkbox"],
    .checkbox-inline input[type="checkbox"] {
      &:dir(rtl) {
        margin-right: -20px !important;
        margin-left: 0px !important;
      }
    }
    .radio-inline,
    .checkbox-inline {
      &:dir(rtl) {
        padding-right: 20px;
        padding-left: 0px !important;
      }
    }
  }

  mark {
    border-radius: 5%;
    background-color: variables.$highlight-color;
  }

  .btn {
    &.btn-default {
      background: functions.alpha-color(variables.$black, 0.7);
      color: variables.$white;

      &:hover,
      &:focus,
      &:active {
        background: functions.alpha-color(variables.$black, 0.8);
        color: variables.$white;
      }
    }

    &.btn-spacing--right {
      margin-right: 10px;
    }

    .fa {
      margin-right: 7px;

      &.margin-left {
        margin-right: 0;
        margin-left: 7px;
      }
    }
  }

  .table {
    margin-bottom: 0;
    th{
        &:dir(rtl){
        text-align: right !important;}
    }
  }

  .color-picker__popover {
    position: absolute;
    z-index: 5;
    top: 40px;
    right: 0;
  }

  .color-input {
    width: 232px;
  }

  .dropdown-menu {
    background: var(--sys-center-channel-bg) !important;

    > li > button:hover {
      background: rgba(var(--sys-center-channel-color-rgb), 0.1);
    }

    .divider {
      opacity: 1;
    }
  }

  .filtered-user-list {
    height: calc(100vh - 175px);

    .announcement-bar--fixed & {
      height: calc(100vh - 205px);
    }
  }

  .Select-value-label {
    white-space: nowrap;
  }

  .inner-wrap {
    position: absolute;
    width: 100%;
  }

  h3 {
    padding-bottom: 0.5em;
    margin: 1em 0;
    font-weight: 600;
  }

  h4 {
    margin-bottom: 2em;
    font-weight: 600;
  }

  .form-control {
    border: 1px solid variables.$light-gray;
    background-color: variables.$white;

    &:focus:not(.Input) {
      border-color: var(--sofa-color);
      box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075),
        0 0 8px var(--sofa-color);
      outline: 0;
    }

    &.disabled {
      background-color: variables.$bg--gray;
    }

    .placeholder-text {
      color: variables.$gray;
    }

    &.Input {
      border: none;
    }
  }

  .filter-control {
    .fa {
      font-size: 12px;
    }
  }

  .log__panel {
    overflow: scroll;
    width: 100%;
    height: calc(100vh - 280px);
    padding: 10px;
    border: variables.$border-gray;
    margin-top: 14px;
    background-color: white;
  }

  &.admin {
    overflow: auto;
    height: 100%;
    min-height: 600px;
    padding: 0 40px 20px;
    background-color: #f1f1f1;
  }

  .form-horizontal {
    height: 100%;

    .control-label {
      font-weight: 600;

      &:dir(rtl) {
        padding-left: 0;
        text-align: right;
      }
      &:dir(ltr) {
        padding-right: 0;
        text-align: left;
      }
    }

    .has-error {
      .control-label {
        font-weight: normal;
      }
    }

    .help-text {
      white-space: pre-line;
    }

    .form-group {
      margin-bottom: 25px;

      &.half {
        margin-bottom: 14px;
      }
    }

    .file__upload {
      display: inline-block;
      width: fit-content;
      margin: 0 10px 10px 0;

      input {
        display: none;
      }
    }

    .alert {
      position: relative;
      top: 1px;
      display: inline-block;
      padding: 5px 7px;
      margin: 1em 0 0;

      .fa {
        margin-right: 5px;
      }

      &.alert-transparent {
        width: 100%;
        padding: 12px 14px;
        border: variables.$border-gray;
        margin: 0;
        background: variables.$white;
      }

      hr {
        border-color: #ddd;
        margin: 0.8em 0;
      }

      div {
        &:last-child {
          hr {
            display: none;
          }
        }
      }
    }
  }

  .banner {
    padding: 0.8em 1.5rem;
    border: variables.$border-gray;
    margin: 4px 0 2em;
    background: variables.$white;
    font-size: 0.95em;
    font-weight: 600;

    &.banner--url {
      padding: 1.2em;
    }

    code {
      background: variables.$bg--gray;
    }

    p {
      &:last-child {
        margin: 0 0 2px;
      }
    }

    .banner__url {
      padding: 0.7em 1em;
      border-radius: 2px;
      background: functions.alpha-color(variables.$black, 0.07);
      word-break: break-all;
    }

    .banner__heading {
      margin-bottom: 0.5em;
      font-size: 1.5em;
    }

    .banner__icon {
      padding: 5px 10px 5px 5px;
    }

    &.warning {
      border-color: #faebcc;
      background: #fcf8e3;
      color: #8a6d3b;
    }
  }

  .popover {
    width: 100%;
    border-radius: 3px;
    font-size: 0.95em;
  }

  .panel {
    border: none;
    background-color: transparent;
  }

  .panel-default {
    > .panel-heading {
      padding: 10px 0;
      background-color: transparent;
    }

    .panel-body {
      padding: 30px 0 10px;
    }
  }

  .panel-group {
    margin-bottom: 50px;
  }

  .panel-title {
    font-size: 24px;
    line-height: 1.5;

    a {
      @include mixins.clearfix;

      display: block;
      text-decoration: none;

      &.collapsed {
        .fa-minus {
          display: none;
        }

        .fa-plus {
          display: inline-block;
        }
      }

      .fa {
        margin-top: 8px;
        color: #aaa;
        float: right;
        font-size: 18px;
      }

      .fa-plus {
        display: none;
      }
    }
  }

  .more-modal__list {
    .filtered-user-list {
      .filter-controls {
        padding-bottom: 1em;
      }
    }

    .filter-row {
      margin: 10px 0;
    }
  }

  .member-list-holder {
    overflow: visible;
    padding: 5px 0;
    background: variables.$white;

    .more-modal__list {
      overflow: visible;
    }

    .more-modal__row {
      &:last-child {
        border: none;
      }
    }
  }

  .member-count {
    margin-top: 8px;
    opacity: 0.7;
  }

  .admin-console__header {
    z-index: 100;
    display: flex;
    height: 64px;
    flex: 0 0 64px;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 12px 20px;
    border-bottom: 1px solid functions.alpha-color(variables.$black, 0.1);
    background: white;
    font-family: 'GraphikArabic', sans-serif;
    font-size: 22px;
    font-weight: 600;
    letter-spacing: -0.01em;
    line-height: 32px;

    html:lang(ar) & {
      font-family: "GraphikArabic", sans-serif;
    }

    button,
    select {
      font-family: "GraphikArabic", sans-serif;
      letter-spacing: normal;

      html:lang(ar) & {
        font-family: "GraphikArabic", sans-serif;
      }
    }

    select {
      font-weight: normal;
    }

    &.with-back {
      padding: 0;

      > div {
        display: flex;
        align-items: center;
      }

      .back {
        display: flex;
        width: 64px;
        height: 64px;
        align-items: center;
        justify-content: center;
        border-right: 1px solid rgba(var(--center-channel-color-rgb), 0.12);
        margin-right: 20px;
        font-size: 3.2rem;
        text-decoration: none;

        &::before {
          position: relative;
          top: -1px;
        }

        &:hover {
          background-color: rgba(var(--center-channel-color-rgb), 0.04);
        }
      }
    }
  }

  .admin-console-save {
    z-index: 100;
    display: flex;
    width: 100%;
    height: 60px;
    -webkit-flex: 0 0 60px;
    flex: 0 0 60px;
    align-items: center;
    padding: 0 10px;
    border-top: 1px solid functions.alpha-color(variables.$black, 0.1);
    background: white;

    .save-button {
      min-width: 100px;
      height: 34px;
      margin: 0 10px;
      background: variables.$light-gray;
      color: functions.alpha-color(variables.$black, 0.5);
      opacity: 1;

      &.btn-primary {
        background: variables.$primary-color;
        color: variables.$white;
      }
    }

    .cancel-button {
      display: flex;
      min-width: 100px;
      height: 34px;
      align-items: center;
      justify-content: center;
      border-radius: var(--radius-s);
      margin: 0 10px 0 0;
      text-decoration: none;
      transition: all 0.15s ease;

      &:hover {
        background-color: variables.$primary-color;
        color: variables.$white;
      }
    }

    .error-message {
      overflow: hidden;

      .control-label {
        padding: 5px 0 0 0;

        @include mixins.text-clamp(2, 25);
      }

      div:nth-child(2) > .control-label {
        padding: 0;
      }
    }

    .reset-defaults-btn {
      right: 0;
      padding-right: 10px;
      padding-left: 10px;
    }
  }

  .status-icon-unknown {
    color: gray;
  }

  .status-icon-success {
    color: #69c169;
  }

  .status-icon-warning {
    color: #eac262;
  }

  .status-icon-error {
    color: #ea6262;
  }

  .suggestion--selected {
    background-color: #e2e2e2;
  }

  .trial {
    margin-top: 15px;
  }

  .trial-error {
    margin-top: 10px;
    color: variables.$dark-gray;
    font-weight: 400;
  }

  .trial-legal-terms,
  .upgrade-legal-terms {
    margin-top: 10px;
    margin-bottom: 0;
    color: variables.$dark-gray;
    font-size: 12px;
    font-weight: 400;
  }

  .logs-banner {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
  }
}

.job-table__table {
  overflow: auto;
  width: 100%;
  min-height: 100px;
  max-height: 240px;
  padding: 5px;
  border: variables.$border-gray;
  margin: 20px 0 10px;
  background-color: variables.$white;
}

.admin-console__disabled-text {
  margin: 10px 0 0 15px;
  color: #777;
}

.admin-sidebar {
  z-index: 10;
  display: flex;
  width: 220px;
  flex-direction: column;
  background: #fff;
  grid-area: lhs;
  border-right: 1px solid #dee0e0;

  &:dir(rtl) {
    border-right: 0;
    border-left: 1px solid #dee0e0;
  }

  .filter-container {
    position: relative;

    .search__icon {
      position: absolute;
      top: 16px;
      
      width: 16px;
      height: 16px;
      fill: rgba(var(--sys-sidebar-text-rgb), 0.75);
      stroke: #999;
      &:dir(rtl) {
        right: 21px;
      }
      &:dir(ltr) {
        left: 21px;
      }
    }

    .filter {
      width: 204px;
      
      border: 1px solid #666;
      border-radius: 6px;
      margin: 8px;
      background: rgba(255, 255, 255, 0.05);
      color: #333333;
      font-size: 12px;
      outline: none;
      &:dir(rtl) {
        padding: 5px 36px 5px 4px;
      } &:dir(ltr) {
        padding: 5px 4px 5px 36px;
      }
      &:focus,
      &.active {
        border: 2px solid var(--sofa-color);
        margin: 7px;
      }
    }

    .input-clear {
      .input-clear-x {
        position: absolute;
       
        font-weight: bold;
        &:dir(rtl) {
          left: 16px;
          color: variables.$black;
        } 
        &:dir(ltr) {
          right: 16px;
          color: variables.$white;
        } 
      }
    }
  }

  .Menu {
    .dropdown-menu {
      overflow: auto;
      background: var(--sys-center-channel-bg);
      color: black;

      &:dir(rtl) {
        left: unset;
        right: -5px;
      }

      .MenuGroup.menu-divider {
        background: rgba(var(--sys-center-channel-color-rgb), 0.16);
      }
    }
  }

  mark {
    border-radius: 5%;
    background-color: variables.$highlight-color;
  }

  body.announcement-bar--fixed & {
    top: variables.$announcement-bar-height + variables.$backstage-bar-height;
    padding-bottom: variables.$announcement-bar-height;
  }

  .dropdown-menu {
    overflow: auto;
    min-width: 200px;
    max-width: 200px;
    max-height: 80vh;
  }

  .team__header {
    background: transparent;
  }

  .nav-pills__container {
    @include mixins.font-smoothing(initial);

    position: relative;
    height: calc(100% - 68px);
    padding-bottom: 20px;
    margin-top: 1px;

    ul {
      padding-right: 0;
      padding-bottom: 20px;
      margin-top: 1px;

      &.nav-stacked.task-list-shown {
        padding-bottom: 72px;
      }
    }
  }

  .sidebar-category {
    margin-top: 0;

    .category-title {
      padding: 10px 12px;
      background: functions.alpha-color(variables.$white, 0.15);
      color: black;
      line-height: 15.4px;
      text-transform: uppercase;
      font-size: 16px;
      font-weight: 600;

      .category-icon {
        top: 6px;
        overflow: hidden;
        width: 16px;
        margin-right: 6px;
        text-align: center;
        vertical-align: bottom;

        &:dir(rtl) {
          margin-right: 0;
          margin-left: 6px;
        }
        &.fa-users {
          font-size: 13px;
        }

        > svg {
          vertical-align: bottom;
        }
      }
    }

    .sections {
      padding: 5px 0;
    }
  }

  .sidebar-section {
    list-style-type: none;

    > .sidebar-section-title {
      position: relative;
    }

    .nav__sub-menu {
      margin-bottom: 7px;

      &:empty {
        display: none;
      }
    }
  }

  .sections {
    &.sections--settings {
      .sidebar-section-title {
        text-transform: uppercase;

        &:hover,
        &:focus {
          color: functions.alpha-color(variables.$white, 0.5);
        }
      }
    }
  }

  .sidebar-section-title {
    padding: 6px 35px 6px 12px;
  }

  .sidebar-section-tag {
    position: relative;
    top: -1px;
    height: 16px;
    padding: 0 4px 0 4px;
    border-radius: 4px;
    margin-left: 8px;
    background-color: rgba(255, 255, 255, 0.75);
    color: rgba(61, 60, 64, 1);
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
  }

  .sidebar-section-indicator {
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;

    i {
      color: functions.alpha-color(variables.$white, 0.5);
      font-size: 14px;
      line-height: 14px;
    }
  }

  .sidebar-subsection-title {
    padding: 6px 35px 6px 30px;

    &--active {
      &::after {
        top: 4px;
      }
    }
  }

  .sidebar-section-title,
  .sidebar-subsection-title {
    position: relative;
    display: block;
    color: rgba(0, 18, 16, 0.5);
    font-size: 16px;

    &:focus {
      text-decoration: none;
    }

    &:hover {
      color: var(--sofa-color);
      text-decoration: none;

      i {
        color: color.adjust(variables.$primary-color, $lightness: 10%);
      }
    }

    &--active {
      background: #f5f7fa;
      color: black;

      // i {
      //     color: variables.$white;
      // }

      &:hover,
      &:focus {
        // background: functions.alpha-color(variables.$white, 0.1);
        color: black;

        // i {
        //     color: variables.$white;
        // }
      }

      &::after {
        position: absolute;
        top: 2px;
        right: -1px;
        display: inline-block;
        color: whitesmoke;
        content: "\f0d9";
        font: normal normal normal 26px/1 FontAwesome;
        -moz-osx-font-smoothing: grayscale;
        -webkit-font-smoothing: antialiased;
        text-rendering: auto;
      }
    }
  }

  .menu-icon--right {
    position: absolute;
    top: 8px;
    right: 12px;
    width: 20px;
    height: 20px;
    font-size: 18px;
    font-weight: 600;
    line-height: 20px;
    text-align: center;

    .fa {
      position: relative;
      right: -2px;
      color: variables.$white;
      font-size: 13px;
    }

    &.menu__close {
      top: 3px;
      right: 10px;
      cursor: pointer;
    }
  }
}

.email-connection-test {
  margin-top: -15px;
}

.s3-connection-test {
  margin-top: -15px;
}

.recycle-db {
  margin-top: 50px !important;
}

.cluster-status {
  width: 24px;
  height: 24px;
}

.config-hash {
  overflow: hidden;
  width: 130px;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.system-users__filter-row {
  position: sticky;
  left: 0;
  display: flex;
  padding-block-end: 12px;
  padding-block-start: 24px;
  padding-inline-end: 24px;
  padding-inline-start: 24px;

  .system-users__filter {
    flex: 1 1 140px;
  }

  .system-users__team-filter {
    flex: 1 1 auto;
  }

  > label {
    display: flex;
    flex: 1 1 auto;
    align-items: center;

    span {
      padding: 0 4px 0 8px;
    }
  }

  label {
    font-weight: normal;
  }
}

.manage-teams {
  .manage-teams__user {
    display: flex;
    align-items: center;
  }

  .manage-teams__teams {
    border-top: variables.$border-gray;
    margin: 1em 0 32px;
    margin-top: 1em;

    .btn-tertiary {
      &.danger {
        color: #c55151;
      }
    }

    .manage-row__empty {
      padding: 9px 0;
    }
  }

  .member-row--padded {
    padding-left: 20px;

    strong {
      margin-right: 10px;
    }
  }

  .member-row-lone-padding {
    padding-top: 10px;
  }

  .manage-row--inner {
    padding: 15px 0 4px;

    & + div {
      border-top: variables.$border-gray;
    }
  }

  .manage-teams__info {
    overflow: hidden;
    flex: 1;
    white-space: nowrap;
&:dir(rtl){
  margin-right: 10px;
}
&:dir(ltr){
  margin-left: 10px;
}
    .manage-teams__name {
      overflow: hidden;
      font-weight: bold;
      text-overflow: ellipsis;
    }

    .manage-teams__email {
      overflow: hidden;
      opacity: 0.5;
      text-overflow: ellipsis;
    }
  }

  .manage-teams__system-admin {
    padding-right: 10px;
    margin-left: 10px;
    opacity: 0.5;
  }

  .manage-teams__team {
    display: flex;
    align-items: center;
    padding: 7px 10px;
    border-bottom: variables.$border-gray;

    .btn {
      font-size: 0.9em;
    }

    .dropdown {
      padding: 6px 0;
    }
  }

  .manage-teams__team-name {
    overflow: hidden;
    flex: 1;
    text-overflow: ellipsis;
  }

  .manage-teams__team-actions {
    margin-left: 10px;

    // Override default react-bootstrap style
    .dropdown-toggle {
      box-shadow: none;
    }
  }
}

.discard-changes-modal .modal-dialog {
  .btn-default,
  .btn-default:hover,
  .btn-default:active,
  .btn-default:focus {
    border: none;
    background-color: transparent;
    color: variables.$primary-color;
  }
}

.admin-setting-user__dropdown {
  position: relative;

  &::before {
    position: absolute;
    top: 15px;
    right: 11px;
    width: 0;
    height: 0;
    border-width: 5px 5px 2.5px;
    border-style: solid;
    border-color: #999 transparent transparent;
    content: "";
    pointer-events: none;
  }
}

.admin-setting-user__fullname {
  opacity: 0.5;
}

.password__group-addon {
  min-width: 150px;
  text-align: left;
}

.password__group-addon-space {
  margin-bottom: 20px;
}

.password-settings__preview-heading {
  display: block;
  margin-bottom: 5px;
  font-weight: 600;
}

#error-tooltip {
  .tooltip-inner {
    max-width: none;
  }
}

.admin-console .admin-console__content.admin-logs-content {
  display: flex;
  max-width: none;
  height: 100%;
  flex-direction: column;

  // We keep the banner the same width and only expand the content box.
  .banner {
    max-width: 920px;
  }

  > .btn {
    width: fit-content;
  }

  .LogTable {
    height: 100%;

    .DataGrid {
      display: flex;
      height: 100%;
      flex-direction: column;

      .DataGrid_rows {
        overflow: auto;
        height: 100%;
        min-height: 100% !important;
      }
    }
  }
}

div[disabled] {
  opacity: 0.7;
  pointer-events: none;
}
