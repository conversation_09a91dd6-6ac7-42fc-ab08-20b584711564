@use "utils/functions";
@use "utils/mixins";
@use "utils/variables";

@use 'sass:color';

body {
  &.announcement-bar--fixed {
    .signup-header {
      top: 42px;
    }
  }
}

.signup-page-container {
  display: flex;
  height: 100vh;
  flex-direction: column;
}

.signup-header {
  position: fixed;
  z-index: 5;
  top: 0;
  left: 0;
  width: 100%;
  padding: 0 1em 0.2em;
  line-height: 33px;

  .fa {
    margin-right: 4px;
  }
}

.signup-team__container {
  position: relative;
  display: flex;
  overflow: hidden;
  max-width: fit-content;
  background-color: rgb(249, 251, 251);
  flex: 1;
  flex-direction: column;
  padding: 0px 3px 0 6px;
  margin: 0 auto;
  justify-content: center;
  border-radius: 24px;
  align-items: center;
  .medium-center {
    width: 70%;
    margin-right: auto;
    margin-left: auto;
  }

  .terms-of-service__markdown {
    min-width: 100vw;
    flex: 1;
    padding-top: 3em;
    padding-bottom: 3em;
    background-color: #fff;
    overflow-y: auto;
  }

  &.branded {
    display: flex;
    max-width: 900px;
    flex-direction: row;

    .signup__markdown {
      flex: 1.3 0 0;
      padding-right: 80px;

      ul + p,
      ol + p {
        margin-top: 0.6em;
      }

      p + ul,
      p + ol {
        margin-top: 0.6em;
      }

      img {
        max-width: 450px;
      }

      p {
        width: 100%;
        margin: 0;
        color: color.adjust(variables.$black, $lightness: 50%);
        line-height: 1.6em;
        white-space: pre-wrap;
        word-break: break-word;
      }
    }

    .signup__content {
      flex: 1 0 0;
    }
  }

  &.padding--less {
    padding-top: 70px;
  }

  &.terms-of-service__container {
    max-width: 100%;
  }

  .terms-of-service__footer {
    padding-top: 0.5em;
    padding-bottom: 0.5em;

    .terms-of-service__button-group {
      padding-top: 1em;
      padding-bottom: 1em;
    }

    .terms-of-service__server-error {
      padding-bottom: 1em;
    }
  }

  &.max-width--full {
    max-width: 100%;
  }

  .form-control:focus {
    box-shadow: inset 0 1px 1px #24A1DE,
    0 0 8px #24A1DE;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p {
    line-height: 1.3;
  }

  h1 {
    font-weight: 600;
  }

  h2 {
    margin-bottom: 0.8em;
    font-size: functions.em(30px);
    font-weight: 600;
    letter-spacing: -0.5px;
  }

  h3 {
    margin: 0 0 1.3em;
    font-size: 1.5em;
    font-weight: 600;
  }

  h4 {
    margin-bottom: 1em;
    font-size: functions.em(20px);
    font-weight: 600;
  }

  h5 {
    font-size: functions.em(16px);
  }

  hr {
    margin: 2em 0;
  }

  p {
    margin-bottom: 1em;
    color: #555;
    line-height: 1.5;

    .black,
    &.black {
      color: #000;
    }
  }

  .input-group {
    &.input-group--limit {
      width: 100%;
      table-layout: fixed;

      .tooltip-inner {
        word-wrap: break-word;
      }

      .form-control {
        display: table-cell;
        width: 100%;
        text-align: left;
      }

      .input-group-addon {
        display: table-cell;
        overflow: hidden;
        width: 50%;
        color: inherit;
        text-align: left;
        text-overflow: ellipsis;
      }
    }
  }

  .inner__content {
    margin: 30px 0 20px;
  }

  .block--gray {
    display: inline-block;
    padding: 0.85em 1.2em;
    border-radius: 3px;
    background: #f2f2f2;
    font-weight: 600;
  }

  form {
    margin-bottom: 0.8em;
  }

  .signup-team-confirm__container {
    padding: 100px 0;
  }

  .signup-team-logo {
    display: none;
    width: 210px;
    margin: 0 0 2em;
  }

  .signup-team-login {
    padding-bottom: 10px;
    font-weight: 600;
  }

  .signup-team__name {
    margin: 0.5em 0 0;
    font-size: 2.2em;
    font-weight: 600;
  }

  .signup-team__subdomain {
    margin: 0.2em 0 1.2em;
    font-size: 1.5em;
    font-weight: 300;
    text-transform: uppercase;
  }

  .form-control {
    height: functions.em(38px);
  }

  .or__container {
    height: 1px;
    margin: 2em 0;
    margin: 2.5em 0 2.5em -1rem;
    background: #ddd;
    text-align: left;

    span {
      position: relative;
      top: -10px;
      display: inline-block;
      width: 40px;
      background: #fff;
      font-size: 1.14286em;
      font-weight: 600;
      line-height: 20px;
      text-align: center;
    }
  }

  ul {
    padding-left: 18px;
    margin-bottom: 0;
  }

  .has-error {
    &.no-padding {
      padding: 0;
    }

    .control-label {
      display: block;
      width: 100%;
      padding: 0.7em 1em;
      border-radius: 3px;
      margin: 1em 0 0;
      background: #f2f2f2;
      color: #999;
      font-size: 14px;

      &::before {
        @extend %font-awesome;

        margin-right: 4px;
        color: #aaa;
        content: "\f071";
      }
    }
  }

  .reset-form {
    position: relative;
    border-radius: 3px;
    font-size: 0.95em;

    p {
      color: inherit;
    }
  }

  // Modifier Styles
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    &.sub-heading {
      margin-bottom: 0;
      font-weight: 400;
    }

    &.color--light {
      font-weight: 300;
    }
  }

  .color--light {
    color: #777;
  }
}
.class-of-color {
  color: #00121080;
}

.custom-margin {
  margin-bottom: 35px;
}

.signup-team__container .signup__content .next-button button#teamNameNextButton { 
   float: inline-end !important;
   width: 50%;
   .fa {
    margin-right: 4px;
    html[dir="ltr"] & {
      transform: rotate(-90deg);
      padding: 6px;
    }
  }

  
}

.signup-team__container .signup__content .babutton button#teamNameNextButton {
  width: 52%;
  margin-top: 21px !important;
  background-color: white;
  color: black !important;
  border: 1px solid rgb(230, 226, 226);
  margin-right: -28px;
}

.babutton {
  .signup-header {
    position: static !important;
    z-index: 5;
   
    left: 0;
    width: auto ;
    padding: 0 1em 0.2em;
    line-height: 33px;
  
    .fa {
      margin-right: 4px;
      html[dir="rtl"] & {
        transform: rotate(-180deg);
        padding: 6px;
      }
    }
  }
}

.select-team__container {
  max-width: 800px;
}

.signup-team-all {
  max-height: calc(75vh - (108px + 89px));
  margin: 0 0 20px;

  .signup-team-dir {
    @include mixins.pie-clearfix;
    margin-top: 5px;
    margin-left: 6px;
    position: relative;
    border-top: 1px solid #ddd;
    border-radius: 12px;
    flex: none;
    order: 0;
    align-self: stretch;
    flex-grow: 0;
    background: #fff;
    border: 1px solid rgba(0, 18, 16, 0.1);
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    &:hover {
      background: variables.$white;
      transition: background-color 0.15s ease;
    }

    .tooltip {
      margin: 10px 0 0 14px;
    }

    .icon {
      height: 20px;
    
      cursor: pointer;
      float: left;
      opacity: 0.45;
      html[dir="rtl"] & {
        margin: 0 15px 0 -8px;
      }
      html[dir="ltr"] & {
        margin: 0 -8px 0 15px;
      }
      svg {
        width: 20px;
        height: 20px;
      }
    }

    .fa-lock {
      position: relative;
      top: 1px;
      font-size: 18px;
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 5px 7px;
      background: rgba(0, 152, 126, 0.1);
      border-radius: 100px;
      flex: none;
      flex-grow: 0;
      color: #24A1DE;
      margin: 5px;
    }

    a {
      display: flex;
      height: 3.5em;
      flex: 1 1 auto;
      align-items: center;
      justify-content: space-between;
      padding: 0 20px 0 16px;
      color: inherit;
      font-size: 1.1em;
      line-height: 3.6em;
      text-decoration: none;

      &.disabled {
        color: variables.$dark-gray;
        cursor: default;
      }
    }
  }

  .signup-team-dir-err {
    padding: 5px 15px;
    border-top: 1px solid #d5d5d5;
    background: #fafafa;
    color: inherit;

    &:first-child {
      border: none;
    }
  }

  .signup-team-dir__name {
    overflow: hidden;
    width: calc(100% - 50px);
    float: left;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .signup-team__icon {
    position: relative;
    color: variables.$dark-gray;
    font-size: 1.5em;

    &.fa-spin {
      top: 16px;
      right: -2px;
      font-size: 0.9em;
    }

    &.fa-info-circle {
      top: 11px;
      padding-left: 0.5em;
      margin-right: 0.3em;
      float: left;
      font-size: 1.5em;
      line-height: 1.5em;
    }
  }
  .teamicon {
    box-sizing: border-box;
    height: 36px;
    background: #24A1DE;
    border-radius: 6px;
    flex: none;
    order: 0;
    color: white;
    text-align: center;
    justify-content: center;
    align-items: center;
    display: flex;
    padding: 5px;
    margin: 4px;
  }
}

.authorize-box {
  width: 500px;
  height: 280px;
  border: 1px solid variables.$black;
  margin: 100px auto;
}

.authorize-inner {
  padding: 20px;
}

.authorize-btn {
  margin-right: 6px;
}

.verify_panel {
  max-width: 380px;
  margin: 60px auto auto;
}
