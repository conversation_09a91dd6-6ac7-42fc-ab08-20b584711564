{"about.buildnumber": "شمارهٔ بیلد:", "about.cloudEdition": "ابری", "about.copyright": "تحت پوشش حق تآلیف {currentYear} - ۲۰۱۵ .شرکت Mattermost. تمامی حقوق محفوظ است", "about.database": "پایگاه داده:", "about.date": "تاریخ ساخت:", "about.dbversion": "نسخه ساختار پایگاه داده:", "about.enterpriseEditionLearn": "در اینجا در مورد نسخه تجاری بیشتر بدانید ", "about.enterpriseEditionSst": "پیام‌رسانی بسیار معتمد برای سازمان", "about.enterpriseEditionSt": "ارتباطات مدرن از پشت دیوار آتش شما.", "about.enterpriseEditione1": "نسخه تجاری", "about.hash": "هش ساخت:", "about.hashee": "هش ساخت نسخه تجاری:", "about.licensed": "مجوز داده شده به:", "about.notice": "Mattermost یک نرم افزار متن باز توسه یافته است . جهت بررسی نسخه ها در <linkServer>server</linkServer> و نسخه دسکتاپ در <linkDesktop>desktop</linkDesktop> و اپلیکشن موبایل در <linkMobile>mobile</linkMobile> اطلاع رسانی می شود.", "about.privacy": "سیاست حفظ حریم خصوصی", "about.teamEditionLearn": "از اینجا عضو جامعه مترمست شوید ", "about.teamEditionSt": "تمام ارتباطات تیم شما از یکجا، قابل جستجوی سریع و قابل دسترس از همه جا.", "about.teamEditiont0": "نسخه تیمی", "about.teamEditiont1": "نسخه تجاری", "about.title": "درباره {appTitle}", "about.tos": "شرایط و قوانین استفاده از سرویس", "about.version": "نسخه مترمست:", "access_history.title": "تاریخچه دسترسی", "accessibility.button.Info": "اطلاعات", "accessibility.button.Search": "جستجو", "accessibility.button.attachment": "فایل ضمیمه", "accessibility.button.bold": "توپُر", "accessibility.button.bulleted_list": "فهرست نشانه‌دار", "accessibility.button.code": "<PERSON><PERSON>", "accessibility.button.dialog": "{dialogName} مکالمه", "accessibility.button.formatting": "قالب‌بندی", "accessibility.button.heading": "سربرگ", "accessibility.button.italic": "کج", "accessibility.button.link": "پیوند", "accessibility.button.numbered_list": "فهرست‌های نشان‌دار", "accessibility.button.preview": "پیش‌نمایش", "accessibility.button.quote": "نقل‌قول", "accessibility.button.strike": "<PERSON><PERSON> زدن", "accessibility.sections.centerContent": "لیست پیام های موقعیت اصلی", "accessibility.sections.centerFooter": "پیام ورودی موقعیت تکمیل کننده", "accessibility.sections.channelHeader": "موقعیت هدر کانال", "accessibility.sections.lhsHeader": "موقعیت منوی تیم", "accessibility.sections.lhsList": "موقعیت منوی کنار کانال", "accessibility.sections.lhsNavigator": "منطقه هدایتگری کانال", "accessibility.sections.rhs": "{regionTitle} موقعیت تکمیل کننده", "accessibility.sections.rhsContent": "جزئیات پیام موقعیت تکمیل کننده", "accessibility.sidebar.types.mention": "اشاره", "accessibility.sidebar.types.mentions": "اشاره ها", "accessibility.sidebar.types.private": "کانال خصوصی", "accessibility.sidebar.types.public": "کانال عمومی", "accessibility.sidebar.types.unread": "خوانده نشده", "activity_log.activeSessions": "نشست های فعال", "activity_log.browser": "مرورگر: {browser}", "activity_log.firstTime": "اولین زمان فعال بودن: تاریخ: {date} و زمان: {time}", "activity_log.lastActivity": "آخرین فعالیت: تاریخ: {date} و زمان: {time}", "activity_log.logout": "خروج از حساب", "activity_log.moreInfo": "اطلاعات بیشتر", "activity_log.os": "سیستم عامل: {os}", "activity_log.sessionId": "شناسه نشست: {id}", "activity_log.sessionsDescription": "نشست ها زمانی که شما از یک مرورگر یا دستگاه جدید وارد حساب کاربری خود می شوید ایجاد می شود. این نشست ها به مترمست کمک می کند تا بدون نیاز به ورود مجدد در یک دوره مشخص که توسط مدیر سیستم مشخص می شود در حساب کاربری خود وارد بمانید. در صورتی که مایل هستید این نشست زودتر خاتمه یابد از دکمه \"خروج از حساب\" کمک بگیرید.", "activity_log_modal.android": "اندروید", "activity_log_modal.androidNativeApp": "اپکلیشن بومی اندروید", "activity_log_modal.androidNativeClassicApp": "اپلیکیشن بومی کلاسیک اندروید", "activity_log_modal.desktop": "اپلیکشن بومی دسکتاپ", "activity_log_modal.iphoneNativeApp": "اپلیکشن بومی آیفون", "activity_log_modal.iphoneNativeClassicApp": "اپلیکیشن بومی کلاسیک آیفون", "add_command.autocomplete": "تکمیل شونده خودکار", "add_command.autocomplete.help": "نمایش دستور / اسلش شما در لیست کامل شونده خودکار زمانی که یک کاربر / را در بخش ورودی سیستم تایپ می کند.", "add_command.autocompleteDescription": "توضیحات تکمیل شونده خودکار", "add_command.autocompleteDescription.help": "(اختیاری) دستور اسلش خود را برای لیست کامل شونده خودکار شرح دهید.", "add_command.autocompleteDescription.placeholder": "برای مثال: \"نتایج جستجو را برای سوابق بیمار برمی گرداند\"", "add_command.autocompleteHint": "راهنما(Hint) تکمیل شونده خودکار", "add_command.autocompleteHint.help": "(اختیاری) آرگومان های مرتبط با دستور اسلش خود را مشخص کنید.", "add_command.autocompleteHint.placeholder": "مثال: [نام بیمار]", "add_command.cancel": "انصراف", "add_command.description": "توضیحات", "add_command.description.help": "دستور اسلش خود را شرح دهید.", "add_command.displayName": "عنوان", "add_command.displayName.help": "عنوانی تا 64 کاراکتر برای صفحه تنظیمات دستور اسلش مشخص کنید.", "add_command.doneHelp": "دستور اسلش شما تنظیم شده است. توکن زیر در محموله خروجی ارسال خواهد شد. لطفاً از آن برای تأیید درخواست از سوی تیم Mattermost خود استفاده کنید (جزئیات در <link>Slash Commands</link>).", "add_command.iconUrl": "نماد پاسخ", "add_command.iconUrl.help": "(اختیاری) URL یک فایل png. یا .jpg را وارد کنید تا هنگام ارسال پاسخ به این دستور اسلش به عنوان نماد استفاده شود. اندازه فایل باید حداقل 128 پیکسل در 128 پیکسل باشد. اگر خالی بماند، از عکس نمایه شما استفاده می شود.", "add_command.iconUrl.placeholder": "https://www.example.com/myicon.png", "add_command.method": "روش درخواست", "add_command.method.get": "دریافت (GET)", "add_command.method.help": "نوع درخواست را مشخص کنید، POST یا GET، که به نقطه پایانی که Mattermost برای رسیدن به برنامه شما ضربه می‌زند، ارسال می‌شود.", "add_command.method.post": "ارسال (POST)", "add_command.save": "ذخیره", "add_command.saving": "در حال ذخیره سازی ...", "add_command.token": "**توکن**: {token}", "add_command.trigger": "کلمه تریگر دستور", "add_command.trigger.help": "کلمه ماشه ای را مشخص کنید که یک دستور داخلی نباشد، حاوی فاصله نباشد و با کاراکتر اسلش شروع نشود.", "add_command.trigger.helpExamples": "مثال: مشت<PERSON><PERSON>، کارمند، بیمار، آب و هوا", "add_command.trigger.helpReserved": "رزرو شده: {link}", "add_command.trigger.helpReservedLinkText": "دستورات اسلش داخلی را ببینید", "add_command.trigger.placeholder": "ماشه فرمان به عنوان مثال \"سلام\" بدون احتساب اسلش", "add_command.triggerInvalidLength": "یک کلمه محرک باید بین {min} و {max} کاراکتر باشد", "add_command.triggerInvalidSlash": "یک کلمه ماشه نمی تواند با یک / شروع شود", "add_command.triggerInvalidSpace": "کلمه ماشه نباید دارای فاصله باشد", "add_command.triggerRequired": "یک کلمه ماشه مورد نیاز است", "add_command.url": "آدرس URL درخواستی", "add_command.url.help": "URL بازگشت به تماس را برای دریافت درخواست رویداد HTTP POST یا GET در هنگام اجرای دستور اسلش مشخص کنید.", "add_command.url.placeholder": "باید با http:// یا https:// شروع شود", "add_command.urlRequired": "URL درخواست مورد نیاز است", "add_command.username": "پاسخ نام کاربری", "add_command.username.help": "(اختیاری) نامی را برای ارسال پاسخ برای این دستور اسلش مشخص کنید. نام کاربری می تواند حداکثر 22 کاراکتر باشد و شامل حروف کوچک، اعداد و نمادهای \"-\"، \"_\" و \".\" باشد. اگر خالی بماند، از نام کاربری Mattermost شما استفاده می شود.", "add_command.username.placeholder": "نام کاربری", "add_emoji.cancel": "انصراف", "add_emoji.customNameTaken": "این نام در حال حاضر توسط یک شکلک سفارشی استفاده شده است. لطفا نام دیگری انتخاب کنید", "add_emoji.failedToAdd": "هنگام افزودن ایموجی سفارشی مشکلی وجود دارد.", "add_emoji.header": "اضافه کردن", "add_emoji.image": "تصویر", "add_emoji.image.button": "انتخاب کنید", "add_emoji.image.help": "یک فایل .gif، .png، یا jpg. برای ایموجی خود مشخص کنید. فایل می تواند تا 512 کیلوبایت باشد. ابعاد به طور خودکار به 128 پیکسل در 128 پیکسل تغییر می کنند و در عین حال نسبت تصویر را حفظ می کنند.", "add_emoji.imageRequired": "یک تصویر برای ایموجی لازم است", "add_emoji.imageTooLarge": "شکلک ایجاد نمی شود. اندازه تصویر باید کمتر از 512 کیلوبایت باشد.", "add_emoji.name": "نام", "add_emoji.name.help": "نام ایموجی تا 64 کاراکتر را مشخص کنید. می تواند شامل حروف کوچک، اعداد و نمادهای '-'، '+' و '_' باشد.", "add_emoji.nameInvalid": "نام ایموجی فقط می تواند شامل حروف کوچک، اعداد و نمادهای «-» و «_» باشد.", "add_emoji.nameRequired": "یک نام برای ایموجی لازم است", "add_emoji.nameTaken": "این نام در حال حاضر توسط یک شکلک سیستم استفاده می شود. لطفا نام دیگری انتخاب کنید", "add_emoji.preview": "پیش نمایش", "add_emoji.preview.sentence": "این یک جمله با {تصویر} در آن است.", "add_emoji.save": "ذخیره", "add_emoji.saving": "در حال ذخیره سازی...", "add_groups_to_channel.title": "افزودن گروه های جدید به کانال {channelName}", "add_groups_to_team.title": "افزودن گروه های جدید به تیم {teamName}", "add_incoming_webhook.cancel": "انصراف", "add_incoming_webhook.channel": "کانال", "add_incoming_webhook.channel.help": "این کانال عمومی یا خصوصی، محل پیش فرض دریافت بارهای وب هوک است. هنگام راه اندازی وب هوک، باید به کانال خصوصی تعلق داشته باشید.", "add_incoming_webhook.channelLocked": "این کانال را قفل کنید", "add_incoming_webhook.channelLocked.help": "در صورت تنظیم، وب هوک ورودی فقط می تواند در کانال انتخابی پست کند.", "add_incoming_webhook.channelRequired": "یک کانال معتبر مورد نیاز است", "add_incoming_webhook.description": "شرح", "add_incoming_webhook.description.help": "وب هوک ورودی خود را شرح دهید.", "add_incoming_webhook.displayName": "عنوان", "add_incoming_webhook.displayName.help": "عنوانی حداکثر تا 64 کاراکتر برای صفحه تنظیمات webhook تعیین کنید.", "add_incoming_webhook.doneHelp": "وب هوک ورودی شما راه اندازی شده است. لطفاً داده‌ها را به نشانی اینترنتی زیر ارسال کنید (جزئیات در <link>Incoming Webhooks</link>).", "add_incoming_webhook.icon_url": "عکس پروفایل", "add_incoming_webhook.icon_url.help": "هنگام پست کردن، نشانی اینترنتی یک فایل png. یا .jpg را برای تصویر نمایه این ادغام وارد کنید. اندازه فایل باید حداقل 128 پیکسل در 128 پیکسل باشد. اگر خالی بماند، از عکس نمایه مشخص شده توسط سازنده وب هوک استفاده می شود.", "add_incoming_webhook.save": "صرفه جویی", "add_incoming_webhook.saving": "صرفه جویی در...", "add_incoming_webhook.url": "**URL**: {url}", "add_incoming_webhook.username": "نام کاربری", "add_incoming_webhook.username.help": "نام کاربری که این ادغام به عنوان پست خواهد شد را مشخص کنید. نام کاربری باید با حروف کوچک، حداکثر 22 کاراکتر باشد و می تواند حاوی اعداد و نمادهای \"-\"، \"_\" و \".\" . اگر خالی بماند، از نامی که سازنده وب هوک تعیین کرده است استفاده می شود.", "add_oauth_app.callbackUrls.help": "URI هایی را مشخص کنید که این سرویس پس از پذیرش یا رد مجوز استفاده از برنامه شما، کاربران را به آنها هدایت می کند و کدهای مجوز یا نشانه های دسترسی را مدیریت می کند. باید با http:// یا https:// شروع شود.", "add_oauth_app.callbackUrlsRequired": "یک یا چند URL بازگشت به تماس مورد نیاز است", "add_oauth_app.clientId": "**شناسه مشتری**: {id}", "add_oauth_app.clientSecret": "**راز مشتری**: {مخفی}", "add_oauth_app.description.help": "برنا<PERSON>ه OAuth 2.0 خود را شرح دهید.", "add_oauth_app.descriptionRequired": "توضیحات برای برنامه OAuth 2.0 مورد نیاز است.", "add_oauth_app.doneHelp": "برنامه OAuth 2.0 شما راه اندازی شده است. لطفاً هنگام درخواست مجوز برای برنامه خود از شناسه مشتری و راز مشتری زیر استفاده کنید (جزئیات در <link>oAuth 2.0 Applications</link>).", "add_oauth_app.doneUrlHelp": "در اینجا URLهای تغییر مسیر مجاز شما هستند.", "add_oauth_app.header": "اضافه کردن", "add_oauth_app.homepage.help": "این نشانی اینترنتی صفحه اصلی برنامه OAuth 2.0 است. بسته به پیکربندی سرور خود، از HTTP یا HTTPS در URL خود استفاده کنید.", "add_oauth_app.homepageRequired": "صفحه اصلی برای برنامه OAuth 2.0 مورد نیاز است.", "add_oauth_app.icon.help": "(اختیاری) URL تصویر را برای برنامه OAuth 2.0 خود مشخص کنید. از HTTP یا HTTPS در URL استفاده کنید.", "add_oauth_app.name.help": "نام نمایشی برنامه OAuth 2.0 خود را مشخص کنید. می توانید تا 64 کاراکتر استفاده کنید.", "add_oauth_app.nameRequired": "نام برای برنامه OAuth 2.0 مورد نیاز است.", "add_oauth_app.trusted.help": "اگر درست باشد، برنامه OAuth 2.0 توسط سرور Mattermost مورد اعتماد تلقی می شود و نیازی به پذیرش مجوز از کاربر ندارد. اگر نادرست باشد، پنجره ای باز می شود که از کاربر می خواهد مجوز را بپذیرد یا رد کند.", "add_oauth_app.url": "**URL(های)**: {url}", "add_outgoing_webhook.callbackUrls": "URL های برگشت به تماس (یکی در هر خط)", "add_outgoing_webhook.callbackUrls.help": "آدرس اینترنتی که پیام ها به آن ارسال می شوند را مشخص کنید. اگر URL خصوصی است، آن را به عنوان {link} اضافه کنید.", "add_outgoing_webhook.callbackUrls.helpLinkText": "اتصال داخلی قابل اعتماد", "add_outgoing_webhook.callbackUrlsRequired": "یک یا چند URL بازگشت به تماس مورد نیاز است", "add_outgoing_webhook.cancel": "انصراف", "add_outgoing_webhook.channel": "کانال", "add_outgoing_webhook.channel.help": "اگر حداق<PERSON> یک کلمه ماشه را مشخص کنید این فیلد اختیاری است. کانال عمومی را که بار را به وب هوک تحویل می دهد، مشخص کنید.", "add_outgoing_webhook.contentType.help1": "نوع محتوای ارسال درخواست را مشخص کنید.", "add_outgoing_webhook.contentType.help2": "برای اینکه سرور پارامترها را در قالب URL در بدنه درخواست رمزگذاری کند، application/x-www-form-urlencoded را انتخاب کنید.", "add_outgoing_webhook.contentType.help3": "برای اینکه سرور بدنه درخواست را به صورت JSON فرمت کند، application/json را انتخاب کنید.", "add_outgoing_webhook.content_Type": "نوع محتوا", "add_outgoing_webhook.description": "شرح", "add_outgoing_webhook.description.help": "وب هوک خروجی خود را شرح دهید.", "add_outgoing_webhook.displayName": "عنوان", "add_outgoing_webhook.displayName.help": "عنوانی را برای صفحه تنظیمات webhook مشخص کنید. عنوان می تواند حداکثر 64 کاراکتر داشته باشد.", "add_outgoing_webhook.doneHelp": "وب هوک خروجی شما راه اندازی شده است. توکن زیر در محموله خروجی ارسال خواهد شد. لطفاً از آن برای تأیید درخواست از سوی تیم Mattermost خود استفاده کنید (جزئیات در <link>Outgoing Webhooks</link>).", "add_outgoing_webhook.icon_url": "عکس پروفایل", "add_outgoing_webhook.icon_url.help": "URL یک فایل .png یا jpg. را برای این ادغام وارد کنید تا در هنگام پست کردن به عنوان تصویر نمایه استفاده شود. اندازه فایل باید حداقل 128 پیکسل در 128 پیکسل باشد. اگر خالی بماند، از عکس نمایه مشخص شده توسط سازنده وب هوک استفاده می شود.", "add_outgoing_webhook.save": "صرفه جویی", "add_outgoing_webhook.saving": "صرفه جویی در...", "add_outgoing_webhook.token": "**ژتون**: {token}", "add_outgoing_webhook.token.message": "مطمئن شوید که این حساب ربات را به تیم‌ها و کانال‌هایی که می‌خواهید با آنها تعامل داشته باشد اضافه کنید. برای اطلاعات بیشتر به <link>مستندات</link> مراجعه کنید.", "add_outgoing_webhook.triggerWords": "کلمات محرک (یکی در هر خط)", "add_outgoing_webhook.triggerWords.help": "کلمات محرکی را که درخواست HTTP POST را به برنامه شما ارسال می کنند، مشخص کنید. اگر فقط کانال را انتخاب کنید، این اختیاری است. ماشه می تواند برای کانال، وب هوک خروجی یا هر دو باشد، اما وقتی آن را برای هر دو تنظیم می کنید، پیام باید با هر دو مقدار مطابقت داشته باشد.", "add_outgoing_webhook.triggerWordsOrChannelRequired": "یک کانال معتبر یا فهرستی از کلمات محرک لازم است", "add_outgoing_webhook.triggerWordsTriggerWhen": "ماشه وقتی", "add_outgoing_webhook.triggerWordsTriggerWhen.help": "مشخص کنید چه زمانی باید وب هوک خروجی فعال شود.", "add_outgoing_webhook.triggerWordsTriggerWhenFullWord": "کلمه اول دقیقاً با یک کلمه ماشه مطابقت دارد", "add_outgoing_webhook.triggerWordsTriggerWhenStartsWith": "کلمه اول با یک کلمه ماشه شروع می شود", "add_outgoing_webhook.username": "نام کاربری", "add_outgoing_webhook.username.help": "نام کاربری که این ادغام به عنوان پست خواهد شد را مشخص کنید. نام کاربری می تواند حداکثر 22 کاراکتر باشد و شامل حروف کوچک، اعداد و نمادهای \"-\"، \"_\" و \".\" باشد. اگر خالی بماند، از نامی که سازنده وب هوک تعیین کرده است استفاده می شود.", "add_teams_to_scheme.confirmation.accept": "بله، تیم حرکت کنید", "add_teams_to_scheme.confirmation.message": "این تیم قبلاً در یک طرح تیمی دیگر انتخاب شده است، آیا مطمئن هستید که می خواهید آن را به این طرح تیمی منتقل کنید؟", "add_teams_to_scheme.confirmation.title": "طرح لغو تیم تغییر می کند؟", "add_user_to_channel_modal.add": "اضافه کردن", "add_user_to_channel_modal.cancel": "انصراف", "add_user_to_channel_modal.help": "برای پیدا کردن کانال تایپ کنید از ↑↓ برای مرور، ↵ برای انتخاب، ESC برای رد کردن استفاده کنید.", "add_user_to_channel_modal.membershipExistsError": "{name} قبلاً عضو آن کانال است", "add_user_to_channel_modal.title": "{name} را به یک کانال اضافه کنید", "add_users_to_role.title": "افزودن کاربران به {roleName}", "add_users_to_team.title": "اعضای جدید را به تیم {teamName} اضافه کنید", "adldap_upsell_banner.confirm.continue": "ادامه", "adldap_upsell_banner.confirm.learn_more": "مطالعه بیشتر", "adldap_upsell_banner.sales_btn": "تماس با بخش فروش برای استفاده", "admin.advance.cluster": "در دسترس بودن بالا", "admin.advance.metrics": "نظارت بر عملکرد", "admin.announcement_banner_feature_discovery.copy": "بنرهای اعلامیه ایجاد کنید تا همه اعضا از اطلاعات مهم مطلع شوند.", "admin.announcement_banner_feature_discovery.title": "بنرهای اعلامیه سفارشی را با Mattermost Professional ایجاد کنید", "admin.audits.reload": "بارگیری مجدد گزارش های فعالیت کاربر", "admin.authentication.email": "احراز هویت ایمیل", "admin.authentication.gitlab": "GitLab", "admin.authentication.guest_access": "دسترسی مهمان", "admin.authentication.ldap": "AD/LDAP", "admin.authentication.mfa": "احراز هویت چند عاملی", "admin.authentication.oauth": "OAuth 2.0", "admin.authentication.openid": "اتصال OpenID", "admin.authentication.saml": "SAML 2.0", "admin.authentication.signup": "ثبت نام", "admin.banner.heading": "توجه داشته باشید:", "admin.billing.company_info.add": "اطلاعات شرکت را اضافه کنید", "admin.billing.company_info.address": "نشانی", "admin.billing.company_info.address_2": "آدرس 2", "admin.billing.company_info.billingAddress": "آدرس قبض", "admin.billing.company_info.city": "شهر", "admin.billing.company_info.companyAddress": "آدرس شرکت", "admin.billing.company_info.companyName": "نام شرکت", "admin.billing.company_info.employees": "{employees} کارمندان", "admin.billing.company_info.numEmployees": "تعداد کارمندان (اختیاری)", "admin.billing.company_info.title": "اطلاعات شرکت", "admin.billing.company_info.zipcode": "کد پستی / کد پستی", "admin.billing.company_info_display.companyDetails": "جزئیات شرکت", "admin.billing.company_info_display.noCompanyInfo": "در حال حاضر هیچ اطلاعات شرکت در پرونده وجود ندارد.", "admin.billing.company_info_display.provideDetails": "نام و آدرس شرکت خود را وارد کنید", "admin.billing.company_info_edit.cancel": "انصراف", "admin.billing.company_info_edit.companyDetails": "جزئیات شرکت", "admin.billing.company_info_edit.company_address": "آدرس شرکت", "admin.billing.company_info_edit.sameAsBillingAddress": "مشابه آدرس صورت حساب", "admin.billing.company_info_edit.save": "ذخیره اطلاعات", "admin.billing.company_info_edit.title": "ویرایش اطلاعات شرکت", "admin.billing.deleteWorkspace.failureModal.buttonText": "تلاش دوباره", "admin.billing.deleteWorkspace.resultModal.ContactSupport": "تماس با پشتیبانی", "admin.billing.history.allPaymentsShowHere": "همه پرداخت‌های ماهانه شما در اینجا نشان داده می‌شود", "admin.billing.history.date": "تاریخ", "admin.billing.history.description": "شرح", "admin.billing.history.noBillingHistory": "در آینده، این جایی است که سابقه صورتحساب شما نشان داده خواهد شد.", "admin.billing.history.pageInfo": "{startRecord} - {endRecord} از {totalRecords}", "admin.billing.history.paid": "پرداخت شده", "admin.billing.history.paymentFailed": "پرداخت ناموفق", "admin.billing.history.pending": "در انتظار", "admin.billing.history.seeHowBillingWorks": "ب<PERSON><PERSON><PERSON><PERSON>د صورتحساب چگونه کار می کند", "admin.billing.history.status": "وضعیت", "admin.billing.history.title": "تاریخچه صورتحساب", "admin.billing.history.total": "جمع", "admin.billing.history.transactions": "معاملات", "admin.billing.subscription.cancelSubscriptionSection.contactUs": "تماس با ما", "admin.billing.subscription.cancelSubscriptionSection.title": "لغو اشتراکتان", "admin.billing.subscription.cloudMonthlyBadge": "ماهیانه", "admin.billing.subscription.cloudTrial.subscribeButton": "اکنون مشترک شوید", "admin.billing.subscription.cloudYearlyBadge": "سالیانه", "admin.billing.subscription.freeTrial.description": "دوره آزمایشی رایگان شما {daysLeftOnTrial} روز دیگر منقضی می‌شود. برای ادامه پس از پایان دوره آزمایشی، اطلاعات پرداخت خود را اضافه کنید.", "admin.billing.subscription.freeTrial.lastDay.description": "دوره آزمایشی رایگان شما به پایان رسیده است. برای ادامه بهره مندی از مزایای Cloud Professional، اطلاعات پرداخت را اضافه کنید.", "admin.billing.subscription.freeTrial.lastDay.title": "آزمایشی رایگان شما امروز به پایان می رسد", "admin.billing.subscription.freeTrial.lessThan3Days.description": "دوره آزمایشی رایگان شما در {daysLeftOnTrial, number} {daysLeftOnTrial, plural, یک {day} دیگر {days}} پایان خواهد یافت. برای ادامه بهره مندی از مزایای Cloud Professional، اطلاعات پرداخت را اضافه کنید.", "admin.billing.subscription.freeTrial.title": "شما در حال حاضر در یک دوره آزمایشی رایگان هستید", "admin.billing.subscription.invoice.next": "فاکتور بعدی", "admin.billing.subscription.planDetails.currentPlan": "(نقشه جاری)", "admin.billing.subscription.planDetails.features.advanceTeamPermission": "مجوزهای تیم پیشرفته", "admin.billing.subscription.planDetails.features.autoComplianceExports": "صادرات انطباق خودکار", "admin.billing.subscription.planDetails.features.customRetentionPolicies": "خط مشی های نگهداری داده های سفارشی", "admin.billing.subscription.planDetails.features.groupAndOneToOneMessaging": "پیام رسانی گروهی و یک به یک، اشتراک گذاری فایل و جستجو", "admin.billing.subscription.planDetails.features.guestAccounts": "حساب های مهمان", "admin.billing.subscription.planDetails.features.incidentCollaboration": "همکاری در حادثه", "admin.billing.subscription.planDetails.features.mfa": "احراز هویت چند عاملی (MFA)", "admin.billing.subscription.planDetails.features.mfaEnforcement": "اجرای وزارت امور خارجه", "admin.billing.subscription.planDetails.features.multilanguage": "ترجمه های چند زبانه", "admin.billing.subscription.planDetails.features.multiplatformSso": "Gitlab، Google و O365 single sign-on", "admin.billing.subscription.planDetails.features.openid": "OpenID", "admin.billing.subscription.planDetails.features.premiumSupport": "پشتیبانی Premium (ارتقای اختیاری)", "admin.billing.subscription.planDetails.features.readOnlyChannels": "کانال های اعلامی فقط خواندنی", "admin.billing.subscription.planDetails.features.sharedChannels": "کانال های مشترک (به زودی)", "admin.billing.subscription.planDetails.features.unlimitedUsers": "کاربران نامحدود", "admin.billing.subscription.planDetails.features.unlimittedUsersAndMessagingHistory": "کاربران نامحدود و تاریخچه پیام", "admin.billing.subscription.planDetails.flatFeePerMonth": "/month (کاربران نامحدود) ", "admin.billing.subscription.planDetails.howBillingWorks": "ب<PERSON><PERSON><PERSON><PERSON>د صورتحساب چگونه کار می کند", "admin.billing.subscription.planDetails.perUserPerMonth": "/کاربر/ماه. ", "admin.billing.subscription.planDetails.productName.cloudEnterprise": "Cloud Enterprise", "admin.billing.subscription.planDetails.productName.cloudFree": "ابر رایگان", "admin.billing.subscription.planDetails.productName.cloudProfessional": "ابر حرفه ای", "admin.billing.subscription.planDetails.productName.unknown": "محصول ناشناخته", "admin.billing.subscription.planDetails.subheader": "جزئیات طرح", "admin.billing.subscription.planDetails.userCount": "{userCount} کاربر", "admin.billing.subscription.privateCloudCard.cloudEnterprise.description": "در Mattermost، ما با شما و تیم شما کار می کنیم تا نیازهای شما را در سراسر محصول برآورده کنیم. اگر به دنبال تخفیف سالیانه هستید، لطفا با تیم فروش ما تماس بگیرید.", "admin.billing.subscription.privateCloudCard.cloudEnterprise.title": "به دنبال تخفیف سالیانه هستید؟ ", "admin.billing.subscription.privateCloudCard.cloudProfessional.description": "ویژگی های امنیتی پیشرفته و انطباق با پشتیبانی ممتاز. برای جزئیات بیشتر به {pricingLink} مراجعه کنید.", "admin.billing.subscription.privateCloudCard.cloudProfessional.title": "به Cloud Enterprise ارتقا دهید", "admin.billing.subscription.privateCloudCard.contactSales": "تماس با بخش فروش", "admin.billing.subscription.privateCloudCard.contactSalesy": "تماس با بخش فروش", "admin.billing.subscription.privateCloudCard.contactSupport": "با پشتیبانی تماس بگیرید", "admin.billing.subscription.privateCloudCard.freeTrial.description": "ما دوست داریم با مشتریان خود و نیازهای آنها کار کنیم. برای سوالات مربوط به اشتراک، صورتحساب یا دوره آزمایشی با بخش فروش تماس بگیرید.", "admin.billing.subscription.privateCloudCard.freeTrial.title": "در مورد محاکمه شما سوال دارید؟", "admin.billing.subscription.privateCloudCard.upgradeNow": "اکنون ارتقا دهید", "admin.billing.subscription.proratedPayment.title": "شما اکنون مشترک {selectedProductName} هستید", "admin.billing.subscription.stateprovince": "ایالت/استان", "admin.billing.subscription.title": "اشتراک، ابونمان", "admin.billing.subscription.updatePaymentInfo": "به‌روزرسانی اطلاعات پرداخت", "admin.billing.subscriptions.billing_summary.lastInvoice.monthlyFlatFee": "هزینه ثابت ماهانه", "admin.billing.subscriptions.billing_summary.lastInvoice.paid": "پرداخت‌شده", "admin.billing.subscriptions.billing_summary.lastInvoice.partialCharges": "هزینه‌های جزئی", "admin.billing.subscriptions.billing_summary.lastInvoice.seeBillingHistory": "مشاهده تاریخچه پرداخت", "admin.billing.subscriptions.billing_summary.lastInvoice.taxes": "مالیات", "admin.billing.subscriptions.billing_summary.lastInvoice.title": "آخرین صورت‌حساب", "admin.billing.subscriptions.billing_summary.lastInvoice.total": "جمع", "admin.billing.subscriptions.billing_summary.lastInvoice.viewInvoice": "نمایش فاکتور", "admin.billing.subscriptions.billing_summary.noBillingHistory.description": "در آینده، این جایی است که آخرین خلاصه صورتحساب شما نشان داده خواهد شد.", "admin.billing.subscriptions.billing_summary.noBillingHistory.link": "ب<PERSON><PERSON><PERSON><PERSON>د صورتحساب چگونه کار می کند", "admin.billing.subscriptions.billing_summary.noBillingHistory.title": "هنوز سابقه صورتحساب وجود ندارد", "admin.bleve.bulkIndexingTitle": "نمایه سازی انبوه:", "admin.bleve.createJob.help": "همه کاربران، کانال‌ها و پست‌های پایگاه داده از قدیمی‌ترین تا جدیدترین فهرست‌بندی می‌شوند. Bleve در طول نمایه سازی در دسترس است اما نتایج جستجو ممکن است تا زمانی که کار نمایه سازی کامل نشود ناقص باشد.", "admin.bleve.createJob.title": "اکنون فهرست کنید", "admin.bleve.enableAutocompleteDescription": "هنگامی که درست باشد، Bleve برای تمام درخواست‌های تکمیل خودکار در کاربران و کانال‌هایی که از آخرین فهرست استفاده می‌کنند استفاده می‌شود. نتایج تکمیل خودکار ممکن است تا زمانی که فهرست انبوهی از پایگاه داده کاربران و کانال‌های موجود تکمیل نشود، ناقص باشد. هنگامی که نادرست است، از تکمیل خودکار پایگاه داده استفاده می شود.", "admin.bleve.enableAutocompleteTitle": "Bleve را برای پرس و جوهای تکمیل خودکار فعال کنید:", "admin.bleve.enableIndexingDescription": "زمانی که درست باشد، فهرست بندی پست های جدید به طور خودکار انجام می شود. تا زمانی که \"Enable Bleve for Search Query\" فعال نشود، پرس و جوهای جستجو از جستجوی پایگاه داده استفاده می کنند. {documentationLink}", "admin.bleve.enableIndexingTitle": "فعال کردن Bleve Indexing:", "admin.bleve.enableSearchingDescription": "هنگامی که درست باشد، Bleve برای تمام جستارهای جستجو با استفاده از آخرین فهرست استفاده می شود. نتایج جستجو ممکن است ناقص باشد تا زمانی که فهرست انبوه پایگاه داده پست موجود به پایان برسد. هنگامی که نادرست است، از جستجوی پایگاه داده استفاده می شود.", "admin.bleve.enableSearchingTitle": "B<PERSON>e را برای عبارت های جستجو فعال کنید:", "admin.bleve.indexDirDescription": "مسیر دایرکتوری مورد استفاده برای ذخیره فهرست های bleve.", "admin.bleve.indexDirTitle": "پوشه فهرست:", "admin.bleve.percentComplete": "{percent}٪ تکمیل‌شده", "admin.bleve.purgeIndexesButton": "شاخص پاکسازی", "admin.bleve.purgeIndexesButton.error": "ایندکس ها پاک نشد: {error}", "admin.bleve.purgeIndexesButton.label": "شاخص های پاکسازی:", "admin.bleve.purgeIndexesButton.success": "ایندکس ها با موفقیت پاک شدند.", "admin.bleve.purgeIndexesHelpText": "پاکسازی، محتوای پوشه فهرست‌های Bleve را به طور کامل حذف می کند. نتایج جستجو ممکن است تا زمانی که فهرست انبوهی از پایگاه داده موجود بازسازی نشود، ناقص باشد.", "admin.bleve.title": "Bleve", "admin.channel_list.archived": "بایگانی شد", "admin.channel_list.group_sync": "همگام سازی گروهی", "admin.channel_list.manual_invites": "دعوتنامه های دستی", "admin.channel_list.private": "خصوصی", "admin.channel_list.public": "عمومی", "admin.channel_settings.channel_detail.archive_confirm.button": "ذخیره و آرشیو کانال", "admin.channel_settings.channel_detail.archive_confirm.message": "ذخیره کانال را از تیم بایگانی می کند و محتوای آن را برای همه کاربران غیر قابل دسترس می کند. آیا مطمئن هستید که می خواهید این کانال را ذخیره و بایگانی کنید؟", "admin.channel_settings.channel_detail.archive_confirm.title": "ذخیره و آرشیو کانال", "admin.channel_settings.channel_detail.channelOrganizationsMessage": "با سازمان های مورد اعتماد به اشتراک گذاشته شده است", "admin.channel_settings.channel_detail.channel_configuration": "پیکربندی کانال", "admin.channel_settings.channel_detail.groupsDescription": "گروه هایی را برای اضافه شدن به این کانال انتخاب کنید.", "admin.channel_settings.channel_detail.groupsTitle": "گروه ها", "admin.channel_settings.channel_detail.manageDescription": "بین دعوت از اعضا به صورت دستی یا همگام سازی اعضا به صورت خودکار از گروه ها را انتخاب کنید.", "admin.channel_settings.channel_detail.manageTitle": "مدیریت کانال", "admin.channel_settings.channel_detail.membersDescription": "لیستی از کاربرانی که در حال حاضر در کانال هستند", "admin.channel_settings.channel_detail.membersTitle": "اعضا", "admin.channel_settings.channel_detail.profileDescription": "خلاصه کانال به همراه نام کانال.", "admin.channel_settings.channel_detail.profileTitle": "پروفایل کانال", "admin.channel_settings.channel_detail.syncedGroupsDescription": "اعضای کانال را بر اساس عضویت در گروه اضافه و حذف کنید.", "admin.channel_settings.channel_detail.syncedGroupsTitle": "گروه های همگام سازی شده", "admin.channel_settings.channel_details.add_group": "اضافه کردن گروه", "admin.channel_settings.channel_details.archiveChannel": "کانال آرشیو", "admin.channel_settings.channel_details.isDefaultDescr": "این کانال پیش فرض را نمی توان به یک کانال خصوصی تبدیل کرد.", "admin.channel_settings.channel_details.isPublic": "کانال عمومی یا کانال خصوصی", "admin.channel_settings.channel_details.isPublicDescr": "اگر \"عموم<PERSON>\" کانال قابل کشف است و هر کاربری می تواند بپیوندد، یا اگر دعوت نامه های \"خصوصی\" مورد نیاز است. تغییر جهت تبدیل کانال های عمومی به خصوصی. وقتی همگام سازی گروهی فعال است، کانال های خصوصی را نمی توان به عمومی تبدیل کرد.", "admin.channel_settings.channel_details.syncGroupMembers": "همگام سازی اعضای گروه", "admin.channel_settings.channel_details.syncGroupMembersDescr": "در صورت فعال بودن، افزودن و حذف کاربران از گروه ها، آنها را از این کانال اضافه یا حذف می کند. تنها راه دعوت اعضا به این کانال، افزودن گروه هایی است که به آنها تعلق دارند. <link>بیشتر بیاموزید</link>", "admin.channel_settings.channel_details.unarchiveChannel": "لغو آرشیو کانال", "admin.channel_settings.channel_list.managementHeader": "مدی<PERSON><PERSON>ت", "admin.channel_settings.channel_list.nameHeader": "نام", "admin.channel_settings.channel_list.no_channels_found": "کانالی پیدا نشد", "admin.channel_settings.channel_list.search_channels_errored": "مشکلی پیش آمد. دوباره امتحان کنید", "admin.channel_settings.channel_list.teamHeader": "تیم", "admin.channel_settings.channel_moderation.channelMentions": "ذکر کانال", "admin.channel_settings.channel_moderation.channelMentions.disabledBoth": "ذکر کانال برای اعضا و مهمانان در [{scheme_name}](../permissions/{scheme_link}) غیرفعال شده است.", "admin.channel_settings.channel_moderation.channelMentions.disabledBothDueToCreatePosts": "مهمانان و اعضا نمی توانند از منشن کانال بدون امکان ایجاد پست استفاده کنند.", "admin.channel_settings.channel_moderation.channelMentions.disabledGuest": "ذکر کانال برای مهمانان در [{scheme_name}](../permissions/{scheme_link}) غیرفعال شده است.", "admin.channel_settings.channel_moderation.channelMentions.disabledGuestsDueToCreatePosts": "مهمانان نمی توانند از ذکر کانال بدون امکان ایجاد پست استفاده کنند.", "admin.channel_settings.channel_moderation.channelMentions.disabledMember": "ذکر کانال برای اعضا در [{scheme_name}](../permissions/{scheme_link}) غیرفعال شده است.", "admin.channel_settings.channel_moderation.channelMentions.disabledMemberDueToCreatePosts": "اعضا نمی توانند از ذکر کانال بدون امکان ایجاد پست استفاده کنند.", "admin.channel_settings.channel_moderation.channelMentionsDesc": "امکان استفاده اعضا و مهمانان از @all، @here و @channel.", "admin.channel_settings.channel_moderation.channelMentionsDescMembers": "امکان استفاده اعضا از @all، @here و @channel.", "admin.channel_settings.channel_moderation.createPosts": "ایجاد پست ها", "admin.channel_settings.channel_moderation.createPosts.disabledBoth": "ایجاد پست برای اعضا و مهمانان در [{scheme_name}](../permissions/{scheme_link}) غیرفعال شده است.", "admin.channel_settings.channel_moderation.createPosts.disabledGuest": "ایجاد پست برای مهمانان در [{scheme_name}](../permissions/{scheme_link}) غیرفعال شده است.", "admin.channel_settings.channel_moderation.createPosts.disabledMember": "ایجاد پست برای اعضا در [{scheme_name}](../permissions/{scheme_link}) غیرفعال شده است.", "admin.channel_settings.channel_moderation.createPostsDesc": "امکان ایجاد پست در کانال برای اعضا و مهمانان.", "admin.channel_settings.channel_moderation.createPostsDescMembers": "امکان ایجاد پست در کانال برای اعضا.", "admin.channel_settings.channel_moderation.guests": "میه<PERSON>انان", "admin.channel_settings.channel_moderation.manageMembers": "مدیریت اعضا", "admin.channel_settings.channel_moderation.manageMembers.disabledBoth": "مدیریت اعضا برای اعضا و مهمانان در [{scheme_name}](../permissions/{scheme_link}) غیرفعال هستند.", "admin.channel_settings.channel_moderation.manageMembers.disabledGuest": "مدیریت اعضا برای مهمانان در [{scheme_name}](../permissions/{scheme_link}) غیرفعال شده است.", "admin.channel_settings.channel_moderation.manageMembers.disabledMember": "مدیریت اعضا برای اعضا در [{scheme_name}](../permissions/{scheme_link}) غیرفعال شده است.", "admin.channel_settings.channel_moderation.manageMembersDesc": "امکان افزودن و حذف افراد توسط اعضا.", "admin.channel_settings.channel_moderation.members": "اعضا", "admin.channel_settings.channel_moderation.permissions": "مجوزها", "admin.channel_settings.channel_moderation.postReactions": "پست واکنش ها", "admin.channel_settings.channel_moderation.postReactions.disabledBoth": "واکنش‌های پست برای اعضا و مهمانان در [{scheme_name}](../permissions/{scheme_link}) غیرفعال شده است.", "admin.channel_settings.channel_moderation.postReactions.disabledGuest": "واکنش‌های پست برای مهمانان در [{scheme_name}](../permissions/{scheme_link}) غیرفعال شده است.", "admin.channel_settings.channel_moderation.postReactions.disabledMember": "واکنش‌های پست برای اعضا در [{scheme_name}](../permissions/{scheme_link}) غیرفعال شده است.", "admin.channel_settings.channel_moderation.postReactionsDesc": "امکان ارسال واکنش برای اعضا و مهمانان.", "admin.channel_settings.channel_moderation.postReactionsDescMembers": "امکان ارسال واکنش برای اعضا.", "admin.channel_settings.channel_moderation.subtitle": "اقدامات موجود برای اعضای کانال و مهمانان را مدیریت کنید.", "admin.channel_settings.channel_moderation.subtitleMembers": "اقدامات موجود برای اعضای کانال را مدیریت کنید.", "admin.channel_settings.channel_moderation.title": "مدیریت کانال", "admin.channel_settings.channel_row.configure": "ویرایش کنید", "admin.channel_settings.description": "تنظیمات کانال را مدیریت کنید", "admin.channel_settings.groupsPageTitle": "کانال های {siteName}", "admin.channel_settings.title": "کانال ها", "admin.cluster.ClusterName": "نام خوشه:", "admin.cluster.ClusterNameDesc": "خوشه برای پیوستن به نام. فقط گره هایی با نام خوشه یکسان به هم می پیوندند. این برای پشتیبانی از استقرار سبز-آبی یا استیجینگ با اشاره به همان پایگاه داده است.", "admin.cluster.ClusterNameEx": "به عنوان مثال: \"تولید\" یا \"صحنه سازی\"", "admin.cluster.EnableExperimentalGossipEncryption": "رمزگذاری آزمایشی Gossip را فعال کنید:", "admin.cluster.EnableExperimentalGossipEncryptionDesc": "وقتی درست باشد، تمام ارتباطات از طریق پروتکل شایعات رمزگذاری خواهد شد.", "admin.cluster.EnableGossipCompression": "فعال‌کردن فشرده‌سازی Gossip:", "admin.cluster.GossipPort": "بندر شایعات:", "admin.cluster.GossipPortDesc": "پورت مورد استفاده برای پروتکل gossip. هر دو UDP و TCP باید روی این پورت مجاز باشند.", "admin.cluster.GossipPortEx": "به عنوان مثال: \"8074\"", "admin.cluster.OverrideHostname": "لغو نام میزبان:", "admin.cluster.OverrideHostnameDesc": "مقدار پیش‌فرض «<blank>» سعی می‌کند نام میزبان را از سیستم عامل دریافت کند یا از آدرس IP استفاده کند. با این ویژگی می توانید نام میزبان این سرور را لغو کنید. لغو نام میزبان توصیه نمی شود مگر اینکه نیاز باشد. این ویژگی همچنین می تواند در صورت نیاز روی یک آدرس IP خاص تنظیم شود.", "admin.cluster.OverrideHostnameEx": "به عنوان مثال: \"app-server-01\"", "admin.cluster.UseIPAddress": "استفاده از آدرس IP:", "admin.cluster.UseIPAddressDesc": "وقتی درست باشد، خوشه سعی می کند از طریق آدرس IP در مقابل استفاده از نام میزبان ارتباط برقرار کند.", "admin.cluster.enableDescription": "وقتی درست باشد، Mattermost در حالت دسترسی بالا اجرا می شود. لطفاً برای کسب اطلاعات بیشتر در مورد پیکربندی High Availability برای Mattermost، به <link>Documentation</link> مراجعه کنید.", "admin.cluster.enableTitle": "حالت دسترسی بالا را فعال کنید:", "admin.cluster.loadedFrom": "این فایل پیکربندی از Node ID {clusterId} بارگیری شده است. اگر از طریق یک متعادل کننده بار به کنسول سیستم دسترسی دارید و با مشکلاتی مواجه هستید، لطفاً راهنمای عیب‌یابی را در <link>اسناد</link>  ما ببینید.", "admin.cluster.noteDescription": "تغییر خصوصیات در این بخش نیاز به راه اندازی مجدد سرور قبل از اعمال دارد.", "admin.cluster.should_not_change": "هشدار: این تنظیمات ممکن است با سرورهای دیگر در خوشه همگام سازی نشوند. ارتباط بین گره‌ای با دسترسی بالا شروع نمی‌شود مگر اینکه config.json را تغییر دهید تا در همه سرورها یکسان باشد و Mattermost را مجدداً راه‌اندازی کنید. لطفاً <linkCluster>مستندات</linkCluster>  را در مورد نحوه افزودن یا حذف یک سرور از خوشه ببینید. اگر از طریق یک متعادل کننده بار به کنسول سیستم دسترسی دارید و مشکلاتی را تجربه می کنید، لطفاً راهنمای عیب یابی را در <linkCluster>مستندات</linkCluster> ما ببینید .", "admin.cluster.status_table.config_hash": "فایل پیکربندی MD5", "admin.cluster.status_table.hostname": "نام میزبان", "admin.cluster.status_table.reload": " بارگیری مجدد وضعیت خوشه", "admin.cluster.status_table.status": "وضعیت", "admin.cluster.status_table.url": "آدرس شایعات", "admin.cluster.status_table.version": "نسخه", "admin.cluster.unknown": "ناشناس", "admin.compliance.complianceMonitoring": "نظارت بر انطباق", "admin.compliance.directoryDescription": "دایرکتوری که گزارش های انطباق در آن نوشته می شود. اگر خالی باشد، روی ./data/ تنظیم می شود.", "admin.compliance.directoryExample": "به عنوان مثال: \"./data/\"", "admin.compliance.directoryTitle": "فهرست راهنمای گزارش انطباق:", "admin.compliance.enableDailyDesc": "هنگامی که درست باشد، Mattermost یک گزارش مطابقت روزانه ایجاد می کند.", "admin.compliance.enableDailyTitle": "فعال کردن گزارش روزانه:", "admin.compliance.enableDesc": "هنگامی که درست است، Mattermost اجازه می دهد تا گزارش انطباق را از برگه **انطباق و حسابرسی** ارسال کنید. برای کسب اطلاعات بیشتر به <link>documentation</link> مراجعه کنید.", "admin.compliance.enableTitle": "فعال کردن گزارش انطباق:", "admin.compliance.newComplianceExportBanner": "این ویژگی با ویژگی جدید [Compliance Export] ({siteURL}/admin_console/compliance/export) جایگزین شده است و در نسخه بعدی حذف خواهد شد. ما مهاجرت به سیستم جدید را توصیه می کنیم.", "admin.complianceExport.createJob.help": "یک کار Export Compliance را فوراً آغاز می کند.", "admin.complianceExport.createJob.title": "اکنون کار Export Compliance را اجرا کنید", "admin.complianceExport.exportFormat.actiance": "Actiance XML", "admin.complianceExport.exportFormat.csv": "CSV", "admin.complianceExport.exportFormat.globalrelay": "رله جهانی EML", "admin.complianceExport.exportFormat.title": "فرمت صادرات:", "admin.complianceExport.exportJobStartTime.description": "زمان شروع کار صادرات انطباق برنامه ریزی شده روزانه را تنظیم کنید. زمانی را انتخاب کنید که افراد کمتری از سیستم شما استفاده کنند. باید یک مهر زمانی 24 ساعته به شکل HH:MM باشد.", "admin.complianceExport.exportJobStartTime.example": "به عنوان مثال: \"02:00\"", "admin.complianceExport.exportJobStartTime.title": "زمان صدور مطابقت:", "admin.complianceExport.globalRelayCustomerType.a10.description": "A10/نوع 10", "admin.complianceExport.globalRelayCustomerType.a9.description": "A9/نوع 9", "admin.complianceExport.globalRelayCustomerType.description": "نوع حساب مشتری Global Relay که سازمان شما دارد.", "admin.complianceExport.globalRelayCustomerType.title": "حساب مشتری رله جهانی:", "admin.complianceExport.globalRelayEmailAddress.description": "آدرس ایمیل سرور Global Relay شما برای صادرات انطباق دریافتی نظارت می کند.", "admin.complianceExport.globalRelayEmailAddress.example": "به عنوان مثال: \"<EMAIL>\"", "admin.complianceExport.globalRelayEmailAddress.title": "آدرس ایمیل Global Relay:", "admin.complianceExport.globalRelaySMTPPassword.description": "رمز عبوری که برای احراز هویت در برابر سرور SMTP GlobalRelay استفاده می شود.", "admin.complianceExport.globalRelaySMTPPassword.example": "به عنوان مثال: \"globalRelayPassword\"", "admin.complianceExport.globalRelaySMTPPassword.title": "<PERSON><PERSON>ز عبور SMTP:", "admin.complianceExport.globalRelaySMTPUsername.description": "نام کاربری که برای احراز هویت در برابر سرور SMTP GlobalRelay استفاده می شود.", "admin.complianceExport.globalRelaySMTPUsername.example": "به عنوان مثال: \"globalRelayUser\"", "admin.complianceExport.globalRelaySMTPUsername.title": "نام کاربری SMTP:", "admin.complianceExport.messagesExportedCount": "{count} پیام صادر شد.", "admin.complianceExport.title": "مطابقت صادرات", "admin.complianceExport.warningCount": "با {count} اخطار مواجه شد، برای جزئیات به warning.txt مراجعه کنید", "admin.complianceExport.warningCount.globalrelay": "با {count} اخطار مواجه شد، برای جزئیات به گزارش مراجعه کنید", "admin.complianceMonitoring.userActivityLogsTitle": "گزارش های فعالیت کاربر", "admin.compliance_export_feature_discovery.copy": "گزارش‌های انطباق روزانه را اجرا کنید و آنها را به فرمت‌های مختلف قابل مصرف توسط ابزارهای ادغام شخص ثالث مانند Smarsh (Actiance) صادر کنید.", "admin.compliance_export_feature_discovery.title": "صادرات مطابق با Mattermost Enterprise را اجرا کنید", "admin.compliance_reports.desc": "اسم شغل:", "admin.compliance_reports.desc_placeholder": "به عنوان مثال. \"حسابرسی 445 برای منابع انسانی\"", "admin.compliance_reports.emails": "ایمیل ها:", "admin.compliance_reports.emails_placeholder": "به عنوان مثال. \"<EMAIL>، <EMAIL>\"", "admin.compliance_reports.from": "از جانب:", "admin.compliance_reports.from_placeholder": "به عنوان مثال. \"2016-03-11\"", "admin.compliance_reports.keywords": "کلید واژه ها:", "admin.compliance_reports.keywords_placeholder": "به عنوان مثال. \"سهام کوتاه\"", "admin.compliance_reports.reload": "بارگیری مجدد گزارش های انطباق کامل", "admin.compliance_reports.run": "اجرای گزارش انطباق", "admin.compliance_reports.title": "گزارش های انطباق", "admin.compliance_reports.to": "به:", "admin.compliance_reports.to_placeholder": "به عنوان مثال. \"2016-03-15\"", "admin.compliance_table.desc": "شرح", "admin.compliance_table.download": "د<PERSON><PERSON><PERSON>د", "admin.compliance_table.failed": "ناموفق", "admin.compliance_table.files": "فایل ها", "admin.compliance_table.params": "پارامترها", "admin.compliance_table.pending": "در انتظار", "admin.compliance_table.records": "سوابق", "admin.compliance_table.status": "وضعیت", "admin.compliance_table.success": "مو<PERSON><PERSON><PERSON>ت", "admin.compliance_table.timestamp": "مهر زمان", "admin.compliance_table.type": "تای<PERSON> کنید", "admin.compliance_table.userId": "درخواست شده توسط", "admin.connectionSecurityNone": "هیچ یک", "admin.connectionSecurityNoneDescription": "Mattermost از طریق یک اتصال ناامن متصل می شود.", "admin.connectionSecurityStart": "STARTTLS", "admin.connectionSecurityStartDescription": "اتصال ناامن موجود را می گیرد و سعی می کند آن را با استفاده از TLS به یک اتصال ایمن ارتقا دهد.", "admin.connectionSecurityTitle": "امنیت اتصال:", "admin.connectionSecurityTls": "TLS", "admin.connectionSecurityTlsDescription": "ارتباط بین Mattermost و سرور شما را رمزگذاری می کند.", "admin.custom_terms_of_service_feature_discovery.copy": "شرایط خدمات خود را ایجاد کنید که کاربران جدید باید قبل از دسترسی به نمونه Mattermost خود در دسک تاپ، وب یا تلفن همراه بپذیرند.", "admin.custom_terms_of_service_feature_discovery.title": "شرایط خدمات سفارشی را با Mattermost Enterprise ایجاد کنید", "admin.customization.androidAppDownloadLinkDesc": "یک لینک برای دانلود برنامه اندروید اضافه کنید. از کاربرانی که از طریق مرورگر وب تلفن همراه به سایت دسترسی پیدا می کنند، صفحه ای نمایش داده می شود که به آنها امکان دانلود برنامه را می دهد. برای جلوگیری از ظاهر شدن صفحه، این قسمت را خالی بگذارید.", "admin.customization.androidAppDownloadLinkTitle": "لینک دانلود اپلیکیشن اندروید:", "admin.customization.announcement.allowBannerDismissalDesc": "در صورت صحت، کاربران می توانند بنر را تا به روز رسانی بعدی آن رد کنند. در صورت نادرست بودن، بنر برای همیشه قابل مشاهده است تا زمانی که توسط مدیر سیستم خاموش شود.", "admin.customization.announcement.allowBannerDismissalTitle": "اجازه حذف بنر:", "admin.customization.announcement.bannerColorTitle": "رنگ بنر:", "admin.customization.announcement.bannerTextColorTitle": "رنگ متن بنر:", "admin.customization.announcement.bannerTextDesc": "متنی که در بنر اطلاعیه ظاهر می شود.", "admin.customization.announcement.bannerTextTitle": "متن بنر:", "admin.customization.announcement.enableBannerDesc": "یک بنر اعلامیه را در همه تیم ها فعال کنید.", "admin.customization.announcement.enableBannerTitle": "فعال کردن بنر اعلان:", "admin.customization.appDownloadLinkDesc": "پیوندی به صفحه دانلود برنامه های Mattermost اضافه کنید. هنگامی که پیوندی وجود دارد، گزینه ای برای \"دانلود برنامه های Mattermost\" در منوی محصول اضافه می شود تا کاربران بتوانند صفحه دانلود را پیدا کنند. این قسمت را خالی بگذارید تا گزینه از منوی محصول مخفی شود.", "admin.customization.appDownloadLinkTitle": "لینک صفحه دانلود اپلیکیشن Mattermost:", "admin.customization.customUrlSchemes": "طرح های URL سفارشی:", "admin.customization.customUrlSchemesDesc": "به متن پیام اجازه می‌دهد در صورتی که با هر یک از طرح‌های URL جدا شده با کاما فهرست شده شروع شود پیوند داده شود. به‌طور پیش‌فرض، طرح‌های زیر پیوندهایی ایجاد می‌کنند: «http»، «https»، «ftp»، «tel» و «mailto».", "admin.customization.customUrlSchemesPlaceholder": "به عنوان مثال: \"git,smtp\"", "admin.customization.enableCustomEmojiDesc": "کاربران را قادر به ایجاد شکلک های سفارشی برای استفاده در پیام ها کنید. وقتی فعال باشد، می‌توان به تنظیمات ایموجی سفارشی در کانال‌ها از طریق انتخابگر شکلک دسترسی داشت.", "admin.customization.enableCustomEmojiTitle": "فعال کردن ایموجی سفارشی:", "admin.customization.enableEmojiPickerDesc": "انتخابگر شکلک به کاربران اجازه می دهد تا شکلک هایی را برای افزودن به عنوان واکنش یا استفاده در پیام ها انتخاب کنند. فعال کردن انتخابگر شکلک با تعداد زیادی ایموجی سفارشی ممکن است عملکرد را کاهش دهد.", "admin.customization.enableEmojiPickerTitle": "فعال کردن Em<PERSON>ji <PERSON>er:", "admin.customization.enableGifPickerDesc": "به کاربران اجازه دهید از طریق یکپارچه سازی Gfycat، GIF ها را از انتخابگر شکلک انتخاب کنند.", "admin.customization.enableGifPickerTitle": "فعال کردن GIF Picker:", "admin.customization.enableInlineLatexDesc": "رندر کد لاتکس درون خطی را فعال کنید. اگر نادرست باشد، لاتکس را فقط می توان در یک بلوک کد با استفاده از برجسته کردن نحو ارائه کرد. لطفاً <link>اسن<PERSON></link>  ما را برای جزئیات در مورد قالب بندی متن بررسی کنید.", "admin.customization.enableInlineLatexTitle": "فعال کردن Inline Latex Rendering:", "admin.customization.enableLatexDesc": "رندر لاتکس را در بلوک های کد فعال کنید. اگر نادرست باشد، کد لاتکس فقط هایلایت می شود.", "admin.customization.enableLatexTitle": "رندر لاتکس را فعال کنید:", "admin.customization.enableLinkPreviewsDesc": "در صورت موجود بودن، پیش نمایشی از محتوای وب سایت، پیوندهای تصویری و پیوندهای YouTube را در زیر پیام نمایش دهید. سرور باید به اینترنت متصل باشد و از طریق فایروال (در صورت وجود) به وب سایت هایی که پیش نمایش از آنها انتظار می رود دسترسی داشته باشد. کاربران می توانند این پیش نمایش ها را از Settings > Display > Website Link Previews غیرفعال کنند.", "admin.customization.enableLinkPreviewsTitle": "فعال کردن پیش نمایش لینک وب سایت:", "admin.customization.enablePermalinkPreviewsDesc": "وقتی فعال باشد، پیوندهای پیام‌های Mattermost یک پیش‌نمایش برای کاربرانی که به پیام اصلی دسترسی دارند ایجاد می‌کند. لطفاً <link>اسناد</link>  ما را برای جزئیات بررسی کنید.", "admin.customization.enablePermalinkPreviewsTitle": "فعال کردن پیش‌نمایش پیوند پیام:", "admin.customization.enableSVGsDesc": "پیش نمایش ها را برای پیوست های فایل SVG فعال کنید و به آنها اجازه دهید در پیام ها ظاهر شوند.", "admin.customization.enableSVGsTitle": "SVG ها را فعال کنید:", "admin.customization.iosAppDownloadLinkDesc": "پیوندی برای دانلود اپلیکیشن iOS اضافه کنید. از کاربرانی که از طریق مرورگر وب تلفن همراه به سایت دسترسی پیدا می کنند، صفحه ای نمایش داده می شود که به آنها امکان دانلود برنامه را می دهد. برای جلوگیری از ظاهر شدن صفحه، این قسمت را خالی بگذارید.", "admin.customization.iosAppDownloadLinkTitle": "لینک دانلود اپلیکیشن iOS:", "admin.customization.restrictLinkPreviewsDesc": "پیش‌نمایش پیوند و پیش‌نمایش پیوند تصویر برای فهرست دامنه‌های جدا شده با کاما نشان داده نخواهد شد.", "admin.customization.restrictLinkPreviewsExample": "به عنوان مثال: \"internal.mycompany.com، images.example.com\"", "admin.customization.restrictLinkPreviewsTitle": "پیش نمایش پیوندهای وب سایت را از این دامنه ها غیرفعال کنید:", "admin.data_grid.empty": "موردی یافت نشد", "admin.data_grid.loading": "بارگذاری", "admin.data_grid.paginatorCount": "{startCount, number} - {endCount, number} از {total, number}", "admin.data_retention.channel_team_counts": "{team_count} {team_count, plural, one {team} other {teams}}, {channel_count} {channel_count, plural, one {channel} {channels}}", "admin.data_retention.channel_team_counts_empty": "N/A", "admin.data_retention.createJob.instructions": "زمان روزانه برای بررسی خط‌مشی‌ها و اجرای کار حذف:", "admin.data_retention.createJob.title": "اکنون کار حذف را اجرا کنید", "admin.data_retention.customPolicies.addPolicy": "سیاست اضافه کنید", "admin.data_retention.customPolicies.subTitle": "سفارشی کنید که تیم‌ها و کانال‌های خاص چه مدت پیام‌ها را نگه می‌دارند.", "admin.data_retention.customPolicies.title": "سیاست های نگهداری سفارشی", "admin.data_retention.customPoliciesTable.appliedTo": "اعمال شده به", "admin.data_retention.customPoliciesTable.channelMessages": "پیام های کانال", "admin.data_retention.customPoliciesTable.description": "شرح", "admin.data_retention.customTitle": "سیاست حفظ سفارشی", "admin.data_retention.custom_policy.cancel": "لغو کنید", "admin.data_retention.custom_policy.channel_selector.addChannels": "کانال ها را اضافه کنید", "admin.data_retention.custom_policy.channel_selector.subTitle": "کانال هایی را اضافه کنید که از این خط مشی حفظ پیروی می کنند.", "admin.data_retention.custom_policy.channel_selector.title": "کانال های اختصاص داده شده", "admin.data_retention.custom_policy.form.durationInput.error": "خطا در تجزیه حفظ پیام.", "admin.data_retention.custom_policy.form.input": "نام خط مشی", "admin.data_retention.custom_policy.form.input.error": "نام خط مشی نمی تواند خالی باشد.", "admin.data_retention.custom_policy.form.subTitle": "به خط مشی خود یک نام بدهید و تنظیمات حفظ را پیکربندی کنید.", "admin.data_retention.custom_policy.form.teamsError": "شما باید یک تیم یا یک کانال به خط مشی اضافه کنید.", "admin.data_retention.custom_policy.form.title": "نام و حفظ", "admin.data_retention.custom_policy.save": "صرفه جویی", "admin.data_retention.custom_policy.serverError": "اشکال در فرم بالا وجود دارد", "admin.data_retention.custom_policy.team_selector.addTeams": "تیم ها را اضافه کنید", "admin.data_retention.custom_policy.team_selector.subTitle": "تیم هایی را اضافه کنید که از این خط مشی حفظ پیروی کنند.", "admin.data_retention.custom_policy.team_selector.title": "تیم های تعیین شده", "admin.data_retention.custom_policy.teams.remove": "برداشتن", "admin.data_retention.form.channelAndDirectMessageRetention": "حفظ کانال و پیام مستقیم", "admin.data_retention.form.days": "روزها", "admin.data_retention.form.fileRetention": "نگهداری فایل", "admin.data_retention.form.keepForever": "برای همیشه نگه دارید", "admin.data_retention.form.text": "برای همه تیم ها و کانال ها اعمال می شود، اما برای سیاست های حفظ سفارشی اعمال نمی شود.", "admin.data_retention.form.years": "سال ها", "admin.data_retention.globalPoliciesTable.channelMessages": "پیام های کانال", "admin.data_retention.globalPoliciesTable.delete": "<PERSON><PERSON><PERSON>", "admin.data_retention.globalPoliciesTable.description": "شرح", "admin.data_retention.globalPoliciesTable.edit": "ویرایش کنید", "admin.data_retention.globalPoliciesTable.files": "فایل ها", "admin.data_retention.globalPolicy.subTitle": "پیام ها و فایل ها را برای مدت زمان مشخصی نگه دارید.", "admin.data_retention.globalPolicy.title": "سیاست حفظ جهانی", "admin.data_retention.globalPolicyTitle": "سیاست حفظ جهانی", "admin.data_retention.global_policy.form.numberError": "با<PERSON>د عددی بزرگتر یا مساوی 1 اضافه کنید.", "admin.data_retention.jobCreation.subTitle": "گزارش روزانه پیام ها و فایل ها بر اساس خط مشی های تعریف شده در بالا حذف می شوند.", "admin.data_retention.jobCreation.title": "گزارش سیاست", "admin.data_retention.jobTimeAM": "{time} صب<PERSON> (UTC)", "admin.data_retention.jobTimePM": "{time} بعد از ظهر (UTC)", "admin.data_retention.retention_days": "{count} {count, plural, one {day} other {days}}", "admin.data_retention.retention_years": "{count} {count, plural, one {year} دیگر {years}}", "admin.data_retention.settings.title": "سیاست های حفظ داده ها", "admin.data_retention.title": "سیاست حفظ داده ها", "admin.data_retention_feature_discovery.copy": "داده های خود را فقط تا زمانی که نیاز دارید نگه دارید. برای حذف خودکار داده های یکبار مصرف، کارهای نگهداری داده ها را برای کانال ها و تیم های انتخابی ایجاد کنید.", "admin.data_retention_feature_discovery.title": "برنامه های نگهداری داده ها را با Mattermost Enterprise ایجاد کنید", "admin.database.migrations_table.name": "نام", "admin.database.migrations_table.version": "نسخه", "admin.database.title": "پایگاه داده", "admin.developer.title": "تنظیمات برنامه نویس", "admin.elasticsearch.bulkIndexingTitle": "نمایه سازی انبوه:", "admin.elasticsearch.caExample": "مثال: \"./elasticsearch/ca.pem\"", "admin.elasticsearch.caTitle": "آدرس CA:", "admin.elasticsearch.clientCertExample": "مثال:‭ \"./elasticsearch/client-cert.pem\"", "admin.elasticsearch.clientKeyExample": "مثال: ‭\"./elasticsearch/client-key.pem\"", "admin.elasticsearch.connectionUrlDescription": "آدرس سرور Elasticsearch. {documentationLink}", "admin.elasticsearch.connectionUrlExample": "به عنوان مثال: \"https://elasticsearch.example.org:9200\"", "admin.elasticsearch.connectionUrlTitle": "آدرس اتصال سرور:", "admin.elasticsearch.createJob.help": "همه کاربران، کانال‌ها و پست‌های پایگاه داده از قدیمی‌ترین تا جدیدترین فهرست‌بندی می‌شوند. Elasticsearch در طول نمایه سازی در دسترس است اما نتایج جستجو ممکن است تا زمانی که کار نمایه سازی کامل نشود ناقص باشد.", "admin.elasticsearch.createJob.title": "اکنون فهرست کنید", "admin.elasticsearch.elasticsearch_test_button": "تست اتصال", "admin.elasticsearch.enableAutocompleteDescription": "به اتصال موفقیت آمیز به سرور Elasticsearch نیاز دارد. در صورت درست بودن، Elasticsearch برای تمام جستارهای تکمیل خودکار در کاربران و کانال هایی که از آخرین فهرست استفاده می کنند استفاده می شود. نتایج تکمیل خودکار ممکن است تا زمانی که فهرست انبوهی از پایگاه داده کاربران و کانال‌های موجود تکمیل نشود، ناقص باشد. هنگامی که نادرست است، از تکمیل خودکار پایگاه داده استفاده می شود.", "admin.elasticsearch.enableAutocompleteTitle": "Elasticsearch را برای جستجوهای تکمیل خودکار فعال کنید:", "admin.elasticsearch.enableIndexingDescription": "زمانی که درست باشد، فهرست بندی پست های جدید به طور خودکار انجام می شود. تا زمانی که \"Enable Elasticsearch for Search Query\" فعال نشود، عبارت های جستجو از جستجوی پایگاه داده استفاده خواهند کرد. {documentationLink}", "admin.elasticsearch.enableIndexingTitle": "فعال کردن Elasticsearch Indexing:", "admin.elasticsearch.enableSearchingDescription": "به اتصال موفقیت آمیز به سرور Elasticsearch نیاز دارد. زمانی که درست باشد، Elasticsearch برای همه عبارت‌های جستجو با استفاده از آخرین فهرست استفاده می‌شود. نتایج جستجو ممکن است ناقص باشد تا زمانی که فهرست انبوه پایگاه داده پست موجود به پایان برسد. هنگامی که نادرست است، از جستجوی پایگاه داده استفاده می شود.", "admin.elasticsearch.enableSearchingTitle": "فعال کردن Elasticsearch برای عبارت های جستجو:", "admin.elasticsearch.password": "به عنوان مثال: \"رمز عبور شما\"", "admin.elasticsearch.passwordDescription": "(اختیاری) رمز عبور برای احراز هویت در سرور Elasticsearch.", "admin.elasticsearch.passwordTitle": "رمز سرور:", "admin.elasticsearch.percentComplete": "{percent}٪ تکمیل‌شده", "admin.elasticsearch.purgeIndexesButton": "شاخص های پاکسازی", "admin.elasticsearch.purgeIndexesButton.error": "ایندکس ها پاک نشد: {error}", "admin.elasticsearch.purgeIndexesButton.label": "شاخص های پاکسازی:", "admin.elasticsearch.purgeIndexesButton.success": "ایندکس ها با موفقیت پاک شدند.", "admin.elasticsearch.purgeIndexesHelpText": "پاکسازی، نمایه های سرور Elasticsearch را به طور کامل حذف می کند. نتایج جستجو ممکن است تا زمانی که فهرست انبوهی از پایگاه داده موجود بازسازی نشود، ناقص باشد.", "admin.elasticsearch.skipTLSVerificationDescription": "در صورت صحت، Mattermost نیازی به امضای گواهی Elasticsearch توسط یک مرجع معتبر گواهینامه ندارد.", "admin.elasticsearch.skipTLSVerificationTitle": "رد شدن از تأیید TLS:", "admin.elasticsearch.sniffDescription": "وقتی درست است، sniffing به طور خودکار همه گره های داده در خوشه شما را پیدا کرده و به آنها متصل می شود.", "admin.elasticsearch.sniffTitle": "فعال کردن Cluster Sniffing:", "admin.elasticsearch.testConfigSuccess": "تست موفقیت آمیز پیکربندی ذخیره شد.", "admin.elasticsearch.testHelpText": "آزمایش می کند که آیا سرور Mattermost می تواند به سرور Elasticsearch مشخص شده متصل شود یا خیر. آزمایش اتصال تنها در صورتی پیکربندی را ذخیره می کند که آزمایش موفقیت آمیز باشد. برای پیغام های خطای دقیق تر به فایل گزارش مراجعه کنید.", "admin.elasticsearch.title": "Elasticsearch", "admin.elasticsearch.usernameDescription": "(اختیاری) نام کاربری برای احراز هویت در سرور Elasticsearch.", "admin.elasticsearch.usernameExample": "به عنوان مثال: \"الاستی<PERSON>\"", "admin.elasticsearch.usernameTitle": "نام کاربری سرور:", "admin.email.agreeHPNS": " من خدمات اعلان فشار میزبانی Mattermost را می‌پذیرم <linkTerms>شرایط خدمات</linkTerms> و <linkPrivacy>خط‌مشی رازداری</linkPrivacy>.", "admin.email.allowEmailSignInDescription": "وقتی درست است، Mattermost به کاربران اجازه می دهد تا با استفاده از ایمیل و رمز عبور خود وارد سیستم شوند.", "admin.email.allowEmailSignInTitle": "فعال کردن ورود به سیستم با ایمیل: ", "admin.email.allowSignupDescription": "وقتی درست است، Mattermost اجازه می دهد تا با استفاده از ایمیل و رمز عبور حساب کاربری ایجاد کنید. این مقدار فقط زمانی باید نادرست باشد که بخواهید ثبت نام را به یک سرویس ورود به سیستم مانند AD/LDAP، SAML یا GitLab محدود کنید.", "admin.email.allowSignupTitle": "فعال کردن ایجاد حساب با ایمیل: ", "admin.email.allowUsernameSignInDescription": "در صورت درست بودن، کاربرانی که وارد ایمیل شده‌اند می‌توانند با استفاده از نام کاربری و رمز عبور خود وارد سیستم شوند. این تنظیم روی ورود به سیستم AD/LDAP تأثیری ندارد.", "admin.email.allowUsernameSignInTitle": "فعال کردن ورود با نام کاربری: ", "admin.email.easHelp": "درباره کامپایل و استقرار برنامه های تلفن همراه خود از یک <link>Enterprise App Store</link>  بیشتر بیاموزید.", "admin.email.mhpns": "برای ارسال اعلان‌ها به برنامه‌های iOS و Android از اتصال HPNS با uptime SLA استفاده کنید", "admin.email.mhpnsHelp": "<linkIOS>برنامه Mattermost iOS</linkIOS>  را از iTunes دانلود کنید. <linkAndroid>برنامه Mattermost Android</linkAndroid> را از Google Play دانلود کنید. درباره <linkHPNS>HPNS</linkHPNS>  بیشتر بیاموزید.", "admin.email.mtpns": "از اتصال TPNS برای ارسال اعلان‌ها به برنامه‌های iOS و Android استفاده کنید", "admin.email.mtpnsHelp": "<linkIOS>برنامه Mattermost iOS</linkIOS>  را از iTunes دانلود کنید. <linkAndroid>برنامه Mattermost Android</linkAndroid> را از Google Play دانلود کنید. درباره <linkHPNS>TPNS</linkHPNS>  بیشتر بیاموزید.", "admin.email.pushOff": "اعلان فشار ارسال نکنید", "admin.email.pushOffHelp": "لطفاً برای کسب اطلاعات بیشتر درباره گزینه‌های راه‌اندازی، به <link>مستندات مربوط به اعلان‌های فشار</link>  مراجعه کنید.", "admin.email.pushServerEx": "به عنوان مثال: \"https://push-test.mattermost.com\"", "admin.email.pushServerTitle": "سرور اعلان فشاری:", "admin.email.pushTitle": "فعال کردن Push Notifications: ", "admin.email.requireVerificationDescription": "معمولاً در تولید روی true تنظیم می شود. وقتی درست است، Mattermost نیاز به تأیید ایمیل پس از ایجاد حساب قبل از اجازه ورود به سیستم دارد. برنامه‌نویسان ممکن است این فیلد را روی false تنظیم کنند تا از ارسال ایمیل‌های تأیید برای توسعه سریع‌تر رد شوند.", "admin.email.requireVerificationTitle": "نیاز به تایید ایمیل: ", "admin.email.selfPush": "به صورت دستی محل سرویس Push Notification Service را وارد کنید", "admin.environment.fileStorage": "ذخیره سازی فایل", "admin.environment.imageProxy": "پروکسی تصویر", "admin.environment.notifications": "اطلاعیه", "admin.environment.notifications.contents.full": "ارسال کامل مطالب پیام", "admin.environment.notifications.contents.generic": "توضیحات عمومی را فقط با نام فرستنده ارسال کنید", "admin.environment.notifications.contents.help": "**ارسال محتوای کامل پیام** - نام فرستنده و کانال در اعلان های ایمیل گنجانده شده است. اگر Mattermost حاوی اطلاعات محرمانه باشد، معمولاً به دلایل انطباق استفاده می شود و خط مشی ایجاب می کند که نمی توان آن را در ایمیل ذخیره کرد.\n  **ارسال توضیحات عمومی فقط با نام فرستنده** - فقط نام شخصی که پیام را ارسال کرده است، بدون هیچ اطلاعاتی در مورد نام کانال یا محتوای پیام در اعلان های ایمیل گنجانده شده است. اگر Mattermost حاوی اطلاعات محرمانه باشد، معمولاً به دلایل انطباق استفاده می شود و خط مشی ایجاب می کند که نمی توان آن را در ایمیل ذخیره کرد.", "admin.environment.notifications.contents.label": "محتوای اعلان ایمیل:", "admin.environment.notifications.enable.help": "معمولاً در تولید روی true تنظیم می شود. وقتی درست است، Mattermost تلاش می‌کند تا اعلان‌های ایمیل ارسال کند. هنگامی که نادرست است، دعوت نامه های ایمیل و ایمیل های تغییر تنظیمات حساب کاربری همچنان ارسال می شوند تا زمانی که سرور SMTP پیکربندی شده باشد. توسعه دهندگان ممکن است این فیلد را روی false تنظیم کنند تا از تنظیم ایمیل برای توسعه سریعتر صرفنظر کنند.", "admin.environment.notifications.enable.label": "فعال کردن اعلان‌های ایمیل:", "admin.environment.notifications.enableConfirmNotificationsToChannel.help": "وقتی درست باشد، از کاربران خواسته می‌شود هنگام پست کردن @channel، @all، @here و نام‌های گروهی در کانال‌هایی با بیش از پنج عضو، تأیید کنند. در صورت نادرست بودن، نیازی به تایید نیست.", "admin.environment.notifications.enableConfirmNotificationsToChannel.label": "کانال @، @all، @here و گفتگوی تایید ذکر گروه را نشان دهید:", "admin.environment.notifications.enableEmailBatching.help": "وقتی درست باشد، کاربران اعلان‌های ایمیلی برای چندین پیام مستقیم و اشاره‌ها در یک ایمیل واحد خواهند داشت. دسته‌بندی در فاصله زمانی پیش‌فرض 15 دقیقه اتفاق می‌افتد که در تنظیمات > اعلان‌ها قابل تنظیم است.", "admin.environment.notifications.enableEmailBatching.label": "فعال کردن دسته‌بندی ایمیل:", "admin.environment.notifications.enablePreviewModeBanner.help": "وقتی فعال است، بنر حالت پیش نمایش، نمایش داده می شود تا کاربران از غیرفعال شدن اعلان های ایمیل آگاه شوند. در صورت غیرفعال بودن، بنر حالت پیش نمایش به کاربران نمایش داده نمی شود.", "admin.environment.notifications.enablePreviewModeBanner.label": "بنر حالت پیش نمایش را فعال کنید:", "admin.environment.notifications.feedbackEmail.help": "آدرس ایمیل نمایش داده شده در حساب ایمیلی که هنگام ارسال ایمیل‌های اعلان از Mattermost استفاده می‌شود.", "admin.environment.notifications.feedbackEmail.label": "اطلاعیه از آدرس:", "admin.environment.notifications.feedbackEmail.placeholder": "به عنوان مثال: \"<EMAIL>\"، \"<EMAIL>\"", "admin.environment.notifications.feedbackOrganization.help": "نام و آدرس سازمان در اعلان‌های ایمیل از Mattermost، مانند \"© ABC Corporation, 565 Knight Way, Palo Alto, California, 94305, USA\" نمایش داده می‌شود. اگر فیلد خالی بماند، نام و آدرس سازمان نمایش داده نخواهد شد.", "admin.environment.notifications.feedbackOrganization.label": "آدرس پستی پاورقی اطلاعیه:", "admin.environment.notifications.feedbackOrganization.placeholder": "مثال: \"© ABC Corporation, 565 Knight Way, Palo Alto, California, 94305, USA\"", "admin.environment.notifications.notificationDisplay.help": "نام نمایشی در حساب ایمیلی که هنگام ارسال ایمیل‌های اعلان از Mattermost استفاده می‌شود.", "admin.environment.notifications.notificationDisplay.label": "نام نمایشی اعلان:", "admin.environment.notifications.notificationDisplay.placeholder": "به عنوان مثال: \"Mattermost Notification\"، \"System\"، \"No-Reply\"", "admin.environment.notifications.pushContents.full": "محتوای پیام کامل در محموله اعلان ارسال شده است", "admin.environment.notifications.pushContents.generic": "توضیحات عمومی با نام فرستنده و کانال", "admin.environment.notifications.pushContents.genericNoChannel": "توضیحات عمومی فقط با نام فرستنده", "admin.environment.notifications.pushContents.help": "**توضیحات عمومی فقط با نام فرستنده** - فقط شامل نام شخصی است که پیام را در اعلان های فشار ارسال کرده است، بدون هیچ اطلاعاتی در مورد نام کانال یا محتوای پیام.\n **توضیحات عمومی با نام فرستنده و کانال** - شامل نام شخصی که پیام را ارسال کرده و کانالی که در آن ارسال شده است می باشد، اما محتوای پیام را شامل نمی شود.\n **محتوای پیام کامل ارسال شده در محموله اعلان** - شامل محتویات پیام در بار اعلان فشاری است که از طریق سرویس اعلان فشاری اپل (APNS) یا پیام‌رسانی ابری Firebase Google (FCM) منتقل می‌شود. **به شدت توصیه می شود** این گزینه فقط با پروتکل \"https\" برای رمزگذاری اتصال و محافظت از اطلاعات محرمانه ارسال شده در پیام ها استفاده شود.", "admin.environment.notifications.pushContents.idLoaded": "محتوای کامل پیام پس از دریافت از سرور دریافت شد", "admin.environment.notifications.pushContents.label": "محتویات Push Notification:", "admin.environment.notifications.pushContents.withIdLoaded.help": "**توضیحات عمومی فقط با نام فرستنده** - فقط شامل نام شخصی است که پیام را در اعلان های فشار ارسال کرده است، بدون هیچ اطلاعاتی در مورد نام کانال یا محتوای پیام.\n **توضیحات عمومی با نام فرستنده و کانال** - شامل نام شخصی که پیام را ارسال کرده و کانالی که در آن ارسال شده است می باشد، اما محتوای پیام را شامل نمی شود.\n **محتوای پیام کامل ارسال شده در محموله اعلان** - شامل محتویات پیام در بار اعلان فشاری است که از طریق سرویس اعلان فشاری اپل (APNS) یا پیام‌رسانی ابری Firebase Google (FCM) منتقل می‌شود. **به شدت توصیه می شود** این گزینه فقط با پروتکل \"https\" برای رمزگذاری اتصال و محافظت از اطلاعات محرمانه ارسال شده در پیام ها استفاده شود.\n**محتوای کامل پیام که هنگام دریافت از سرور واکشی شده** - بار اعلان ارسال شده از طریق APNS یا FCM حاوی محتوای پیامی نیست، در عوض حاوی یک شناسه پیام منحصربفرد است که برای دریافت محتوای پیام از سرور هنگام دریافت اعلان فشاری توسط یک سرویس دهنده استفاده می شود. دستگاه اگر دسترسی به سرور امکان پذیر نباشد، یک اعلان عمومی نمایش داده می شود.", "admin.environment.notifications.replyToAddress.help": "آدرس ایمیلی که در هدر Reply-To هنگام ارسال ایمیل‌های اعلان از Mattermost استفاده می‌شود.", "admin.environment.notifications.replyToAddress.label": "آدرس پاسخ به اطلاعیه:", "admin.environment.notifications.replyToAddress.placeholder": "به عنوان مثال: \"<EMAIL>\"، \"<EMAIL>\"", "admin.environment.pushNotificationServer": "Push Notification Server", "admin.environment.smtp": "SMTP", "admin.environment.smtp.connectionSecurity.option.none": "هیچ یک", "admin.environment.smtp.connectionSecurity.option.starttls": "STARTTLS", "admin.environment.smtp.connectionSecurity.option.tls": "TLS (توصیه می شود)", "admin.environment.smtp.connectionSecurity.title": "امنیت اتصال:", "admin.environment.smtp.connectionSmtpTest": "تست اتصال", "admin.environment.smtp.enableSecurityFixAlert.description": "در صورت صحت، اگر در 12 ساعت گذشته هشدار رفع مشکل امنیتی مربوطه اعلام شده باشد، مدیران سیستم از طریق ایمیل مطلع می شوند. برای فعال کردن ایمیل نیاز است.", "admin.environment.smtp.enableSecurityFixAlert.title": "فعال کردن هشدارهای امنیتی:", "admin.environment.smtp.skipServerCertificateVerification.description": "وقتی درست باشد، Mattermost گواهی سرور ایمیل را تأیید نمی کند.", "admin.environment.smtp.skipServerCertificateVerification.title": "رد شدن از تأیید گواهی سرور:", "admin.environment.smtp.smtpAuth.description": "وقتی درست است، احراز هویت SMTP فعال است.", "admin.environment.smtp.smtpAuth.title": "فعال کردن احراز هویت SMTP:", "admin.environment.smtp.smtpFail": "اتصال ناموفق: {error}", "admin.environment.smtp.smtpPassword.description": "این اعتبار را از مدیر راه اندازی سرور ایمیل خود دریافت کنید.", "admin.environment.smtp.smtpPassword.placeholder": "به عنوان مثال: \"رمز عبور شما\"، \"jcuS8PuvcpGhpgHhlcpT1Mx42pnqMxQY\"", "admin.environment.smtp.smtpPassword.title": "رمز عبور سرور SMTP:", "admin.environment.smtp.smtpPort.description": "پورت سرور ایمیل SMTP.", "admin.environment.smtp.smtpPort.placeholder": "به عنوان مثال: \"25\"، \"465\"، \"587\"", "admin.environment.smtp.smtpPort.title": "پورت سرور SMTP:", "admin.environment.smtp.smtpServer.description": "محل سرور ایمیل SMTP.", "admin.environment.smtp.smtpServer.placeholder": "به عنوان مثال: \"smtp.yourcompany.com\"، \"email-smtp.us-east-1.amazonaws.com\"", "admin.environment.smtp.smtpServer.title": "سرور SMTP:", "admin.environment.smtp.smtpSuccess": "هنگام ارسال ایمیل هیچ خطایی گزارش نشد. لطفا صندوق ورودی خود را بررسی کنید تا مطمئن شوید.", "admin.environment.smtp.smtpUsername.description": "این اعتبار را از مدیر راه اندازی سرور ایمیل خود دریافت کنید.", "admin.environment.smtp.smtpUsername.placeholder": "به عنوان مثال: \"<EMAIL>\"، \"AKIADTOVBGERKLCBV\"", "admin.environment.smtp.smtpUsername.title": "نام کاربری سرور SMTP:", "admin.environment.smtp.testing": "آزمایش کردن...", "admin.environment.webServer": "وب سرور", "admin.experimental.allowCustomThemes.desc": "بخش **نمایش > تم > تم سفارشی** را در تنظیمات فعال می کند.", "admin.experimental.allowCustomThemes.title": "اجازه دادن به تم های سفارشی:", "admin.experimental.clientSideCertCheck.desc": "هنگامی که **اولیه**، پس از تایید گواهی سمت مشتری، ایمیل کاربر از گواهی بازیابی می شود و برای ورود بدون رمز عبور استفاده می شود. وقتی **ثانویه**، پس از تأیید گواهی سمت مشتری، ایمیل کاربر از گواهی بازیابی می شود و با ایمیل ارائه شده توسط کاربر مطابقت داده می شود. اگر مطابقت داشته باشند، کاربر با اعتبار نامه های ایمیل/گذرواژه معمولی وارد سیستم می شود.", "admin.experimental.clientSideCertCheck.title": "روش ورود به سیستم گواهی سمت مشتری:", "admin.experimental.clientSideCertEnable.desc": "صدور گواهینامه سمت سرویس گیرنده را برای سرور Mattermost شما فعال می کند. برای اطلاعات بیشتر به <link>documentation</link> مراجعه کنید.", "admin.experimental.clientSideCertEnable.title": "فعال کردن گواهی سمت مشتری:", "admin.experimental.collapsedThreads.always_on": "همیشه روشن", "admin.experimental.collapsedThreads.default_off": "فعال (پیش‌فرض خاموش)", "admin.experimental.collapsedThreads.desc": "وقتی فعال است (پیش‌فرض خاموش)، کاربران باید رشته‌های پاسخ جمع‌شده را در تنظیمات فعال کنند. وقتی غیرفعال است، کاربران نمی توانند به رشته های پاسخ جمع شده دسترسی داشته باشند. لطفاً <linkKnownIssues>اسناد مربوط به مشکلات شناخته شده</linkKnownIssues>  ما را مرور کنید و در ارائه بازخورد در <linkCommunityChannel>کانال انجمن</linkCommunityChannel> ما  کمک کنید .mattermost.com/core/channels/folded-reply-threads).", "admin.experimental.collapsedThreads.off": "معلول", "admin.experimental.collapsedThreads.title": "موضوعات پاسخ جمع شده", "admin.experimental.defaultTheme.desc": "یک تم پیش‌فرض تنظیم کنید که برای همه کاربران جدید در سیستم اعمال شود.", "admin.experimental.defaultTheme.title": "تم پیش فرض:", "admin.experimental.emailBatchingBufferSize.desc": "حداکثر تعداد اعلان‌های دسته‌بندی شده در یک ایمیل را مشخص کنید.", "admin.experimental.emailBatchingBufferSize.example": "به عنوان مثال: \"256\"", "admin.experimental.emailBatchingBufferSize.title": "اندازه بافر دسته بندی ایمیل:", "admin.experimental.emailBatchingInterval.desc": "حداکثر فرکانس را در ثانیه مشخص کنید که کار دسته‌بندی برای اعلان‌های جدید بررسی می‌کند. فواصل بچینگ طولانی تر باعث افزایش عملکرد می شود.", "admin.experimental.emailBatchingInterval.example": "به عنوان مثال: \"30\"", "admin.experimental.emailBatchingInterval.title": "فاصله دسته بندی ایمیل:", "admin.experimental.emailSettingsLoginButtonBorderColor.desc": "رنگ حاشیه دکمه ورود به ایمیل را برای اهداف برچسب‌گذاری سفید مشخص کنید. قبل از کد از یک کد هگز با علامت # استفاده کنید. این تنظیم فقط برای برنامه های تلفن همراه اعمال می شود.", "admin.experimental.emailSettingsLoginButtonBorderColor.title": "رنگ حاشیه دکمه ورود ایمیل:", "admin.experimental.emailSettingsLoginButtonColor.desc": "رنگ دکمه ورود به ایمیل را برای اهداف برچسب گذاری سفید مشخص کنید. قبل از کد از یک کد هگز با علامت # استفاده کنید. این تنظیم فقط برای برنامه های تلفن همراه اعمال می شود.", "admin.experimental.emailSettingsLoginButtonColor.title": "رنگ دکمه ورود ایمیل:", "admin.experimental.emailSettingsLoginButtonTextColor.desc": "رنگ متن دکمه ورود به ایمیل را برای اهداف برچسب‌گذاری سفید مشخص کنید. قبل از کد از یک کد هگز با علامت # استفاده کنید. این تنظیم فقط برای برنامه های تلفن همراه اعمال می شود.", "admin.experimental.emailSettingsLoginButtonTextColor.title": "رنگ متن دکمه ورود به ایمیل:", "admin.experimental.enableChannelViewedMessages.desc": "این تنظیم تعیین می‌کند که آیا رویدادهای WebSocket «channel_viewed» ارسال می‌شوند، که اعلان‌های خوانده‌نشده را در بین کلاینت‌ها و دستگاه‌ها همگام‌سازی می‌کند. غیرفعال کردن تنظیمات در استقرارهای بزرگتر ممکن است عملکرد سرور را بهبود بخشد.", "admin.experimental.enableChannelViewedMessages.title": "فعال کردن پیام‌های WebSocket مشاهده شده از کانال:", "admin.experimental.enableOnboardingFlow.desc": "وقتی درست است، مراحلی را به کاربران جدید نشان داده می‌شود که باید آنها را به عنوان بخشی از فرآیند سوار شدن کامل انجام دهند", "admin.experimental.enableOnboardingFlow.title": "فعال کردن Onboarding:", "admin.experimental.enableThemeSelection.desc": "برگه **نمایش > تم** را در تنظیمات فعال می کند تا کاربران بتوانند موضوع خود را انتخاب کنند.", "admin.experimental.enableThemeSelection.title": "فعال کردن انتخاب تم:", "admin.experimental.enableTutorial.desc": "هنگامی که درست است، کاربران وقتی Mattermost را برای اولین بار پس از ایجاد حساب باز می کنند، یک آموزش از آنها خواسته می شود. در صورت نادرست بودن، آموزش غیرفعال می‌شود و کاربران وقتی Mattermost را برای اولین بار پس از ایجاد حساب باز می‌کنند، در میدان شهر قرار می‌گیرند.", "admin.experimental.enableTutorial.title": "فعال کردن آموزش:", "admin.experimental.enableUserDeactivation.desc": "وقتی درست باشد، کاربران ممکن است حساب خود را از **تنظیمات > پیشرفته** غیرفعال کنند. اگر کاربر حساب کاربری خود را غیرفعال کند، یک اعلان ایمیلی دریافت می کند که تایید می کند غیرفعال شده است. در صورت نادرست بودن، کاربران نمی توانند حساب خود را غیرفعال کنند.", "admin.experimental.enableUserDeactivation.title": "فعال کردن غیرفعال کردن حساب:", "admin.experimental.enableUserTypingMessages.desc": "این تنظیم تعیین می کند که آیا پیام های \"کاربر در حال تایپ...\" در زیر کادر پیام نمایش داده می شوند یا خیر. غیرفعال کردن تنظیمات در استقرارهای بزرگتر ممکن است عملکرد سرور را بهبود بخشد.", "admin.experimental.enableUserTypingMessages.title": "فعال کردن پیام‌های تایپ کاربر:", "admin.experimental.experimentalEnableAuthenticationTransfer.desc": "وقتی درست است، کاربران می‌توانند روش ورود به سیستم خود را به روشی که در سرور فعال است، از طریق نمایه یا APIها تغییر دهند. وقتی نادرست است، کاربران نمی توانند روش ورود به سیستم خود را تغییر دهند، صرف نظر از اینکه کدام گزینه های احراز هویت فعال هستند.", "admin.experimental.experimentalEnableAuthenticationTransfer.title": "اجازه انتقال احراز هویت:", "admin.experimental.experimentalEnableAutomaticReplies.desc": "وقتی درست است، کاربران می‌توانند پاسخ‌های خودکار را در **تنظیمات > اعلان‌ها** فعال کنند. کاربران یک پیام سفارشی تنظیم می کنند که به طور خودکار در پاسخ به پیام های مستقیم ارسال می شود. وقتی نادرست است، ویژگی پاسخ‌های مستقیم پیام مستقیم را غیرفعال می‌کند و آن را از تنظیمات پنهان می‌کند.", "admin.experimental.experimentalEnableAutomaticReplies.title": "فعال کردن پاسخ های خودکار:", "admin.experimental.experimentalEnableDefaultChannelLeaveJoinMessages.desc": "این تنظیم تعیین می‌کند که آیا پیام‌های سیستم خروج/پیوستن تیم در کانال پیش‌فرض میدان شهر پست می‌شود یا خیر.", "admin.experimental.experimentalEnableDefaultChannelLeaveJoinMessages.title": "فعال کردن پیام های سیستم خروج/پیوستن به کانال پیش فرض:", "admin.experimental.experimentalEnableHardenedMode.desc": "یک حالت سخت‌شده را برای Mattermost فعال می‌کند که باعث می‌شود تجربه کاربر به نفع امنیت مبادله شود. برای کسب اطلاعات بیشتر به <link>Documentation</link> مراجعه کنید.", "admin.experimental.experimentalEnableHardenedMode.title": "حالت سخت شده را فعال کنید:", "admin.experimental.experimentalFeatures": "ویژگی های تجربی", "admin.experimental.experimentalPrimaryTeam.desc": "تیم اصلی که کاربران روی سرور اعضای آن هستند. وقتی یک تیم اصلی تنظیم می‌شود، گزینه‌های پیوستن به تیم‌های دیگر یا ترک تیم اصلی غیرفعال می‌شوند.", "admin.experimental.experimentalPrimaryTeam.example": "به عنوان مثال: \"نام تیم\"", "admin.experimental.experimentalPrimaryTeam.title": "تیم اصلی:", "admin.experimental.ldapSettingsLoginButtonBorderColor.desc": "رنگ حاشیه دکمه ورود به سیستم AD/LDAP را برای اهداف برچسب‌گذاری سفید مشخص کنید. قبل از کد از یک کد هگز با علامت # استفاده کنید. این تنظیم فقط برای برنامه های تلفن همراه اعمال می شود.", "admin.experimental.ldapSettingsLoginButtonBorderColor.title": "رنگ حاشیه دکمه ورود AD/LDAP:", "admin.experimental.ldapSettingsLoginButtonColor.desc": "رنگ دکمه ورود به سیستم AD/LDAP را برای اهداف برچسب گذاری سفید مشخص کنید. قبل از کد از یک کد هگز با علامت # استفاده کنید. این تنظیم فقط برای برنامه های تلفن همراه اعمال می شود.", "admin.experimental.ldapSettingsLoginButtonColor.title": "رنگ دکمه ورود AD/LDAP:", "admin.experimental.ldapSettingsLoginButtonTextColor.desc": "رنگ متن دکمه ورود به سیستم AD/LDAP را برای اهداف برچسب‌گذاری سفید مشخص کنید. قبل از کد از یک کد هگز با علامت # استفاده کنید. این تنظیم فقط برای برنامه های تلفن همراه اعمال می شود.", "admin.experimental.ldapSettingsLoginButtonTextColor.title": "رنگ متن دکمه ورود AD/LDAP:", "admin.experimental.linkMetadataTimeoutMilliseconds.desc": "تعداد میلی‌ثانیه‌هایی که باید منتظر فراداده از پیوند شخص ثالث باشید. با متادیتای پست استفاده می شود.", "admin.experimental.linkMetadataTimeoutMilliseconds.example": "به عنوان مثال: \"5000\"", "admin.experimental.linkMetadataTimeoutMilliseconds.title": "مهلت زمانی فراداده پیوند:", "admin.experimental.samlSettingsLoginButtonBorderColor.desc": "رنگ حاشیه دکمه ورود SAML را برای اهداف برچسب‌گذاری سفید مشخص کنید. قبل از کد از یک کد هگز با علامت # استفاده کنید. این تنظیم فقط برای برنامه های تلفن همراه اعمال می شود.", "admin.experimental.samlSettingsLoginButtonBorderColor.title": "رنگ حاشیه دکمه ورود SAML:", "admin.experimental.samlSettingsLoginButtonColor.desc": "رنگ دکمه ورود SAML را برای برچسب گذاری سفید مشخص کنید. قبل از کد از یک کد هگز با علامت # استفاده کنید. این تنظیم فقط برای برنامه های تلفن همراه اعمال می شود.", "admin.experimental.samlSettingsLoginButtonColor.title": "رنگ دکمه ورود SAML:", "admin.experimental.samlSettingsLoginButtonTextColor.desc": "رنگ متن دکمه ورود به سیستم SAML را برای اهداف برچسب‌گذاری سفید مشخص کنید. قبل از کد از یک کد هگز با علامت # استفاده کنید. این تنظیم فقط برای برنامه های تلفن همراه اعمال می شود.", "admin.experimental.samlSettingsLoginButtonTextColor.title": "رنگ متن دکمه ورود SAML:", "admin.experimental.timeBetweenUserTypingUpdatesMilliseconds.desc": "تعداد میلی‌ثانیه‌های انتظار بین ارسال رویدادهای وب سوکت توسط کاربر.", "admin.experimental.timeBetweenUserTypingUpdatesMilliseconds.example": "به عنوان مثال: \"5000\"", "admin.experimental.timeBetweenUserTypingUpdatesMilliseconds.title": "مهلت زمانی تایپ کاربر:", "admin.experimental.useChannelInEmailNotifications.desc": "وقتی درست است، نام کانال و تیم در خطوط موضوع اعلان ایمیل ظاهر می شود. برای سرورهایی که فقط از یک تیم استفاده می کنند مفید است. وقتی نادرست است، فقط نام تیم در موضوع اعلان ایمیل ظاهر می شود.", "admin.experimental.useChannelInEmailNotifications.title": "از نام کانال در اعلان‌های ایمیل استفاده کنید:", "admin.experimental.userStatusAwayTimeout.desc": "این تنظیم تعداد ثانیه‌هایی را که پس از آن نشانگر وضعیت کاربر به «دور» تغییر می‌کند، زمانی که از Mattermost دور هستند، تعیین می‌کند.", "admin.experimental.userStatusAwayTimeout.example": "به عنوان مثال: \"300\"", "admin.experimental.userStatusAwayTimeout.title": "مهلت زمانی خروج از وضعیت کاربر:", "admin.false": "نادرست", "admin.feature_flags.flag": "پرچم", "admin.feature_flags.flag_value": "مقدار", "admin.feature_flags.title": "پرچم‌های ویژگی", "admin.file.enableFileAttachments": "اجازه اشتراک گذاری فایل:", "admin.file.enableFileAttachmentsDesc": "وقتی نادرست است، اشتراک گذاری فایل در سرور را غیرفعال می کند. تمام آپلود فایل ها و تصاویر در پیام ها در بین مشتریان و دستگاه ها، از جمله تلفن همراه، ممنوع است.", "admin.file.enableMobileDownloadDesc": "هنگامی که نادرست است، دانلود فایل در برنامه های تلفن همراه را غیرفعال می کند. کاربران همچنان می توانند فایل ها را از یک مرورگر وب موبایل دانلود کنند.", "admin.file.enableMobileDownloadTitle": "اجازه دانلود فایل در موبایل:", "admin.file.enableMobileUploadDesc": "هنگامی که نادرست است، آپلود فایل در برنامه های تلفن همراه را غیرفعال می کند. اگر Allow File Sharing روی درست تنظیم شده باشد، کاربران همچنان می‌توانند فایل‌ها را از یک مرورگر وب تلفن همراه آپلود کنند.", "admin.file.enableMobileUploadTitle": "اجازه آپلود فایل در موبایل:", "admin.file_upload.chooseFile": "انتخاب فایل", "admin.file_upload.noFile": "هیچ فایلی آپلود نشد", "admin.file_upload.uploadFile": "بارگذاری", "admin.filter.apply": "درخواست دادن", "admin.filter.filters": "فیلترها", "admin.filter.reset": "بازنشانی فیلترها", "admin.filter.title": "محدود شده توسط", "admin.general.localization.availableLocalesDescription": "زبان‌هایی که در دسترس کاربران است را در **تنظیمات > نمایشگر > زبان**، در دسترس آن‌ها قرار دهید (این قسمت را خالی بگذارید تا همه زبان های پشتیبانی شده در دسترس باشند). اگر به صورت دستی زبان‌های جدیدی اضافه می‌کنید، **زبان پیش‌فرض کلاینت** باید قبل از ذخیره این تنظیم اضافه شود.\n\nآیا می خواهید در ترجمه کمک کنید؟ برای مشارکت به <link>سرور ترجمه Mattermost </link> بپیوندید.", "admin.general.localization.availableLocalesNoResults": "نتیجه ای پیدا نشد", "admin.general.localization.availableLocalesTitle": "زبان های موجود:", "admin.general.localization.clientLocaleDescription": "زبان پیش‌فرض برای کاربران تازه ایجاد شده و صفحاتی که کاربر در آنها وارد نشده است.", "admin.general.localization.clientLocaleTitle": "زبان پیش فرض مشتری:", "admin.general.localization.serverLocaleDescription": "زبان پیش‌فرض برای پیام‌های سیستم برای تغییر این مورد نیاز به راه اندازی مجدد سرور قبل از اعمال می شود.", "admin.general.localization.serverLocaleTitle": "زبان پیش فرض سرور:", "admin.general.log": "ورود به سیستم", "admin.gitlab.EnableMarkdownDesc": "1. وارد حساب GitLab خود شوید و به تنظیمات پروفایل -> برنامه ها بروید.\n2. URI های Redirect \"'<your-mattermost-url>'/login/gitlab/complete\" (مثال: http://localhost:8065/login/gitlab/complete) و \"'<your-mattermost-url>\" را وارد کنید /signup/gitlab/complete\".\n3. سپس از فیلدهای \"Application Secret Key\" و \"Application ID\" از GitLab برای تکمیل گزینه های زیر استفاده کنید.\n4. URL های Endpoint زیر را تکمیل کنید.", "admin.gitlab.authTitle": "نقطه پایان تأیید اعتبار:", "admin.gitlab.clientIdDescription": "این مقدار را از طریق دستورالعمل های بالا برای ورود به GitLab بدست آورید.", "admin.gitlab.clientIdExample": "به عنوان مثال: \"jcuS8PuvcpGhpgHhlcpT1Mx42pnqMxQY\"", "admin.gitlab.clientIdTitle": "شناسه برنامه:", "admin.gitlab.clientSecretDescription": "این مقدار را از طریق دستورالعمل های بالا برای ورود به GitLab بدست آورید.", "admin.gitlab.clientSecretExample": "به عنوان مثال: \"jcuS8PuvcpGhpgHhlcpT1Mx42pnqMxQY\"", "admin.gitlab.clientSecretTitle": "کلید مخفی برنامه:", "admin.gitlab.enableDescription": "وقتی درست است، Mattermost اجازه می‌دهد تیم ایجاد کرده و با استفاده از GitLab OAuth ثبت نام کنید.\n \n1. وارد حساب GitLab خود شوید و به تنظیمات پروفایل -> برنامه ها بروید.\n2. URI های Redirect \"'<your-mattermost-url>'/login/gitlab/complete\" (مثال: http://localhost:8065/login/gitlab/complete) و \"'<your-mattermost-url>\" را وارد کنید /signup/gitlab/complete\".\n3. سپس از فیلدهای \"Application Secret Key\" و \"Application ID\" از GitLab برای تکمیل گزینه های زیر استفاده کنید.\n4. URL های Endpoint زیر را تکمیل کنید.", "admin.gitlab.enableTitle": "فعال کردن احراز هویت با GitLab: ", "admin.gitlab.siteUrl": "آدرس سایت GitLab: ", "admin.gitlab.siteUrlDescription": "URL نمونه GitLab خود را وارد کنید، به عنوان مثال. https://example.com:3000. اگر نمونه GitLab شما با SSL تنظیم نشده است، URL را به جای https:// با http:// شروع کنید.", "admin.gitlab.siteUrlExample": "به عنوان مثال: https://", "admin.gitlab.tokenTitle": "نقطه پایان نشانه:", "admin.gitlab.userTitle": "User API Endpoint:", "admin.google.EnableMarkdownDesc": "1. <linkLogin>ورود به سیستم</linkLogin> به حساب Google خود.\n2. به <linkConsole>https://console.developers.google.com</linkConsole> بروید، روی Credentials در سمت چپ کلیک کنید.\n 3. در زیر سربرگ Credentials، روی Create Credentials کلیک کنید، OAuth Client ID را انتخاب کنید و Web Application را انتخاب کنید.\n 4. \"Mattermost - your-company-name\" را به عنوان Name وارد کنید.\n 5. در زیر URIهای تغییر مسیر مجاز your-mattermost-url/signup/google/complete را وارد کنید (به عنوان مثال: http://localhost:8065/signup/google/complete). روی ایجاد کلیک کنید.\n 6. Client ID و Client Secret را در فیلدهای زیر قرار دهید، سپس روی ذخیره کلیک کنید.\n 7. به <linkApi>Google People API</linkApi>  بروید و روی *فعال کردن* کلیک کنید.", "admin.google.authTitle": "نقطه پایان تأیید اعتبار:", "admin.google.clientIdDescription": "شناسه مشتری که هنگام ثبت درخواست خود در Google دریافت کرده اید.", "admin.google.clientIdExample": "به عنوان مثال: \"7602141235235-url0fhs1mayfasbmop5qlfns8dh4.apps.googleusercontent.com\"", "admin.google.clientIdTitle": "شناسه مشتری:", "admin.google.clientSecretDescription": "راز مشتری که هنگام ثبت درخواست خود در Google دریافت کرده اید.", "admin.google.clientSecretExample": "به عنوان مثال: \"H8sz0Az-dDs2p15-7QzD231\"", "admin.google.clientSecretTitle": "راز مشتری:", "admin.google.tokenTitle": "نقطه پایان نشانه:", "admin.google.userTitle": "User API Endpoint:", "admin.group_settings.filters.isConfigured": "پیکربندی شده است", "admin.group_settings.filters.isLinked": "وصل شده", "admin.group_settings.filters.isUnconfigured": "پیکربندی نشده است", "admin.group_settings.filters.isUnlinked": "پیوند داده نشده است", "admin.group_settings.group_detail.duplicateMentionNameError": "ذکر گروه قبلاً گرفته شده است.", "admin.group_settings.group_detail.groupProfileDescription": "نام این گروه", "admin.group_settings.group_detail.groupProfileTitle": "نمایه گروه", "admin.group_settings.group_detail.groupTeamsAndChannelsDescription": "تیم ها و کانال های پیش فرض را برای اعضای گروه تنظیم کنید. تیم‌های اضافه شده شامل کانال‌های پیش‌فرض، میدان شهر و خارج از موضوع خواهند بود. افزودن یک کانال بدون تنظیم تیم، به لیست زیر تیم ضمنی را اضافه می کند.", "admin.group_settings.group_detail.groupTeamsAndChannelsTitle": "عضویت تیم و کانال", "admin.group_settings.group_detail.groupUsersDescription": "فهرست کاربران در Mattermost مرتبط با این گروه.", "admin.group_settings.group_detail.groupUsersTitle": "کاربران", "admin.group_settings.group_detail.group_configuration": "پیکربندی گروه", "admin.group_settings.group_detail.introBanner": "تیم ها و کانال های پیش فرض را پیکربندی کنید و کاربران متعلق به این گروه را مشاهده کنید.", "admin.group_settings.group_detail.invalidOrReservedMentionNameError": "فقط حروف (a-z)، اعداد (0-9)، نقطه، خط تیره و زیرخط مجاز هستند.", "admin.group_settings.group_detail.invalid_length": "نام باید از 1 تا 64 نویسه حروف کوچک عددی باشد.", "admin.group_settings.group_details.add_channel": "افزودن کانال", "admin.group_settings.group_details.add_team": "تیم را اضافه کنید", "admin.group_settings.group_details.add_team_or_channel": "تیم یا کانال را اضافه کنید", "admin.group_settings.group_details.group_mention.name": "ذکر گروه:", "admin.group_settings.group_details.group_profile.name": "نام:", "admin.group_settings.group_details.group_teams_and_channels.no-teams-or-channels-speicified": "هنوز هیچ تیم یا کانالی مشخص نشده است", "admin.group_settings.group_details.group_teams_and_channels_row.privateChannel": "کانال (خصوصی)", "admin.group_settings.group_details.group_teams_and_channels_row.privateTeam": "تیم (خصوصی)", "admin.group_settings.group_details.group_teams_and_channels_row.publicChannel": "کانال", "admin.group_settings.group_details.group_teams_and_channels_row.publicTeam": "تیم", "admin.group_settings.group_details.group_teams_and_channels_row.remove": "<PERSON><PERSON><PERSON>", "admin.group_settings.group_details.group_teams_and_channels_row.remove.confirm_body": "حذف این عضویت از اضافه شدن کاربران بعدی در این گروه به {name} {displayType} جلوگیری می کند.", "admin.group_settings.group_details.group_teams_and_channels_row.remove.confirm_button": "بله، حذف کنید", "admin.group_settings.group_details.group_teams_and_channels_row.remove.confirm_header": "عضویت از {name} {displayType} حذف شود؟", "admin.group_settings.group_details.group_users.email": "پست الکترونیک:", "admin.group_settings.group_details.group_users.no-users-found": "کاربری پیدا نشد", "admin.group_settings.group_details.menuAriaLabel": "تیم یا منوی کانال را اضافه کنید", "admin.group_settings.group_profile.group_teams_and_channels.assignedRoles": "نقش های تعیین شده", "admin.group_settings.group_profile.group_teams_and_channels.name": "نام", "admin.group_settings.group_profile.group_teams_and_channels.type": "تای<PERSON> کنید", "admin.group_settings.group_row.configure": "پیکربندی کنید", "admin.group_settings.group_row.edit": "ویرایش کنید", "admin.group_settings.group_row.link_failed": "پیوند ناموفق بود", "admin.group_settings.group_row.linked": "مرتبط", "admin.group_settings.group_row.linking": "رب<PERSON> دادن", "admin.group_settings.group_row.not_linked": "مرتبط نشده", "admin.group_settings.group_row.unlink_failed": "لغو پیوند انجام نشد", "admin.group_settings.group_row.unlinking": "لغو پیوند", "admin.group_settings.groupsPageTitle": "گروه ها", "admin.group_settings.groups_list.link_selected": "پیوند گروه های انتخاب شده", "admin.group_settings.groups_list.mappingHeader": "Mattermost Linking", "admin.group_settings.groups_list.nameHeader": "نام", "admin.group_settings.groups_list.no_groups_found": "هیچ گروهی پیدا نشد", "admin.group_settings.groups_list.paginatorCount": "{startCount, number} - {endCount, number} از {total, number}", "admin.group_settings.groups_list.unlink_selected": "لغو پیوند گروه های انتخاب شده", "admin.group_settings.introBanner": "گروه ها راهی برای سازماندهی کاربران و اعمال اقدامات برای همه کاربران درون آن گروه هستند.\nبرای اطلاعات بیشتر درباره گروه‌ها، لطفاً به <link>اسناد</link>  مراجعه کنید.", "admin.group_settings.ldapGroupsDescription": "AD/LDAP را وصل کنید و در Mattermost گروه ایجاد کنید. برای شروع، ویژگی های گروه را در صفحه پیکربندی [AD/LDAP]({siteURL}/admin_console/authentication/ldap) پیکربندی کنید.", "admin.group_settings.ldapGroupsTitle": "گروه های AD/LDAP", "admin.group_settings.need_groupname": "شما باید نام گروهی را مشخص کنید.", "admin.group_teams_and_channels_row.channelAdmin": "ادمین کانال", "admin.group_teams_and_channels_row.member": "عضو", "admin.group_teams_and_channels_row.teamAdmin": "ادمین تیم", "admin.groups_feature_discovery.copy": "از گروه‌های AD/LDAP برای سازماندهی و اعمال کنش‌ها برای چندین کاربر به طور همزمان استفاده کنید. عضویت تیم و کانال، مجوزها و موارد دیگر را مدیریت کنید.", "admin.groups_feature_discovery.title": "گروه های Active Directory/LDAP خود را با Mattermost Enterprise همگام کنید", "admin.guest_access.disableConfirmButton": "ذخیره و غیرفعال کردن دسترسی مهمان", "admin.guest_access.disableConfirmMessage": "غیرفعال کردن دسترسی مهمان، تمام جلسات فعلی حساب مهمان را لغو می کند. مهمان‌ها دیگر نمی‌توانند وارد شوند و مهمان‌های جدید را نمی‌توان به Mattermost دعوت کرد. کاربران مهمان در لیست کاربران به عنوان غیرفعال علامت گذاری می شوند. فعال کردن این ویژگی، حساب های مهمان قبلی را بازیابی نمی کند. آیا مطمئن هستید که می خواهید این کاربران را حذف کنید؟", "admin.guest_access.disableConfirmTitle": "دسترسی مهمان ذخیره و غیرفعال شود؟", "admin.guest_access.disableConfirmWarning": "تمام جلسات فعلی حساب مهمان لغو شده و به عنوان غیرفعال علامت گذاری می شود", "admin.guest_access.enableTitle": "فعال کردن دسترسی مهمان: ", "admin.guest_access.mfaDescription": "وقتی درست است، برای ورود به سیستم، <link>احراز هویت چند عاملی</link> برای مهمانان لازم است. کاربران مهمان جدید باید MFA را هنگام ثبت نام پیکربندی کنند. کاربران مهمان وارد شده بدون پیکربندی MFA به صفحه راه اندازی MFA هدایت می شوند تا پیکربندی کامل شود.\n \nاگر سیستم شما دارای کاربران مهمان با روش‌های ورود به سیستم غیر از AD/LDAP و ایمیل است، MFA باید با ارائه‌دهنده احراز هویت خارج از Mattermost اجرا شود.", "admin.guest_access.mfaDescriptionMFANotEnabled": "[تأیید هویت چند عاملی] (./mfa) در حال حاضر فعال نیست.", "admin.guest_access.mfaDescriptionMFANotEnforced": "[تأیید هویت چند عاملی] (./mfa) در حال حاضر اجرا نمی شود.", "admin.guest_access.mfaTitle": "اجرای احراز هویت چند عاملی: ", "admin.guest_access.whitelistedDomainsDescription": "(اختیاری) حساب های مهمان را می توان در سطح سیستم از این لیست دامنه های مهمان مجاز ایجاد کرد.", "admin.guest_access.whitelistedDomainsExample": "به عنوان مثال: \"company.com، othercorp.org\"", "admin.guest_access.whitelistedDomainsTitle": "دامنه های مهمان در لیست سفید:", "admin.guest_access_feature_discovery.copy": "با کاربران خارج از سازمان خود همکاری کنید و در عین حال کانال های دسترسی و اعضای تیم آنها را به شدت کنترل کنید.", "admin.guest_access_feature_discovery.title": "فعال کردن حساب های مهمان با Mattermost Professional", "admin.image.amazonS3BucketDescription": "نامی که برای سطل S3 خود در AWS انتخاب کرده اید.", "admin.image.amazonS3BucketExample": "به عنوان مثال: \"مهمترین رسانه\"", "admin.image.amazonS3BucketTitle": "سطل آمازون S3:", "admin.image.amazonS3EndpointDescription": "نام میزبان ارائه‌دهنده فضای ذخیره‌سازی سازگار با S3 شما. پیش‌فرض «s3.amazonaws.com» است.", "admin.image.amazonS3EndpointExample": "به عنوان مثال: \"s3.amazonaws.com\"", "admin.image.amazonS3EndpointTitle": "نقطه پایانی آمازون S3:", "admin.image.amazonS3IdDescription": "(اختیاری) فقط در صورتی لازم است که نمی‌خواهید با استفاده از <link>نقش IAM</link>  در S3 احراز هویت کنید. شناسه کلید دسترسی ارائه شده توسط سرپرست Amazon EC2 خود را وارد کنید.", "admin.image.amazonS3IdExample": "به عنوان مثال: \"AKIADTOVBGERKLCBV\"", "admin.image.amazonS3IdTitle": "شناسه کلید دسترسی آمازون S3:", "admin.image.amazonS3PathPrefixDescription": "پیشوندی که برای سطل S3 خود در AWS انتخاب کرده اید.", "admin.image.amazonS3PathPrefixExample": "به عنوان مثال: \"subdir1\" یا می توانید آن را خالی بگذارید.", "admin.image.amazonS3PathPrefixTitle": "پیشوند مسیر Amazon S3:", "admin.image.amazonS3RegionDescription": "منطقه AWS که هنگام ایجاد سطل S3 خود انتخاب کردید. اگر هیچ منطقه ای تنظیم نشده باشد، Mattermost سعی می کند منطقه مناسب را از AWS دریافت کند، یا اگر هیچ منطقه ای پیدا نشد، آن را روی 'us-east-1' تنظیم می کند.", "admin.image.amazonS3RegionExample": "به عنوان مثال: \"us-east-1\"", "admin.image.amazonS3RegionTitle": "منطقه آمازون S3:", "admin.image.amazonS3SSEDescription": "در صورت صحت، فایل‌ها را در Amazon S3 با استفاده از رمزگذاری سمت سرور با کلیدهای مدیریت شده توسط Amazon S3 رمزگذاری کنید. برای کسب اطلاعات بیشتر به <link>اسناد</link>  مراجعه کنید.", "admin.image.amazonS3SSETitle": "فعال کردن رمزگذاری سمت سرور برای Amazon S3:", "admin.image.amazonS3SSLDescription": "در صورت نادرست، اتصالات ناامن به آمازون S3 را مجاز کنید. پیش‌فرض فقط اتصالات امن است.", "admin.image.amazonS3SSLTitle": "اتصالات امن آمازون S3 را فعال کنید:", "admin.image.amazonS3SecretDescription": "(اختیاری) کلید دسترسی مخفی مرتبط با شناسه کلید دسترسی Amazon S3 شما.", "admin.image.amazonS3SecretExample": "به عنوان مثال: \"jcuS8PuvcpGhpgHhlcpT1Mx42pnqMxQY\"", "admin.image.amazonS3SecretTitle": "کلید دسترسی مخفی آمازون S3:", "admin.image.amazonS3TraceDescription": "(حالت توسعه) هنگامی که درست است، اطلاعات رفع اشکال اضافی را در گزارش های سیستم ثبت کنید.", "admin.image.amazonS3TraceTitle": "فعال کردن Amazon S3 Debugging:", "admin.image.archiveRecursionDescription": "وقتی فعال باشد، محتوای اسناد درون فایل‌های ZIP در نتایج جستجو برگردانده می‌شود. این ممکن است بر عملکرد سرور برای فایل های حجیم تأثیر بگذارد.", "admin.image.archiveRecursionTitle": "فعال کردن جستجوی محتوای اسناد در فایل های ZIP:", "admin.image.enableProxy": "فعال کردن پروکسی تصویر:", "admin.image.enableProxyDescription": "وقتی درست است، یک پراکسی تصویر را برای بارگیری همه تصاویر Markdown فعال می کند.", "admin.image.extractContentDescription": "وقتی فعال باشد، انواع سند پشتیبانی شده بر اساس محتوایشان قابل جستجو هستند. نتایج جستجو برای اسناد موجود ممکن است <link>تا زمانی که انتقال داده اجرا نشود</link> ناقص باشد .", "admin.image.extractContentTitle": "فعال کردن جستجوی سند بر اساس محتوا:", "admin.image.localDescription": "دایرکتوری که فایل ها و تصاویر در آن نوشته می شوند. اگر خالی باشد، پیش‌فرض روی ./data/ است.", "admin.image.localExample": "به عنوان مثال: \"./data/\"", "admin.image.localTitle": "فهرست ذخیره‌سازی محلی:", "admin.image.maxFileSizeDescription": "حداکثر اندازه فایل برای پیوست های پیام در مگابایت. احتیاط: بررسی کنید که حافظه سرور می تواند از تنظیمات شما پشتیبانی کند. حجم فایل های بزرگ خطر خرابی سرور و آپلود ناموفق به دلیل قطع شدن شبکه را افزایش می دهد.", "admin.image.maxFileSizeExample": "50", "admin.image.maxFileSizeTitle": "حداکثر اندازه فایل:", "admin.image.proxyOptions": "گزینه های پروکسی تصویر از راه دور:", "admin.image.proxyOptionsDescription": "گزینه های اضافی مانند کلید امضای URL. برای اطلاعات بیشتر در مورد گزینه هایی که پشتیبانی می شوند، به مستندات پروکسی تصویر خود مراجعه کنید.", "admin.image.proxyType": "نوع پروکسی تصویر:", "admin.image.proxyTypeDescription": "یک پروکسی تصویر را برای بارگیری تمام تصاویر Markdown از طریق یک پروکسی پیکربندی کنید. پروکسی تصویر از درخواست‌های ناامن تصویر توسط کاربران جلوگیری می‌کند، حافظه پنهان را برای افزایش عملکرد فراهم می‌کند و تنظیمات تصویر مانند تغییر اندازه را خودکار می‌کند. برای کسب اطلاعات بیشتر به <link>documentation</link> مراجعه کنید.", "admin.image.proxyURL": "URL پروکسی تصویر از راه دور:", "admin.image.proxyURLDescription": "URL سرور پروکسی تصویر راه دور شما.", "admin.image.publicLinkDescription": "نمک 32 کاراکتری به امضای پیوندهای تصویر عمومی اضافه شده است. به صورت تصادفی در هنگام نصب ایجاد شد. برای ایجاد نمک جدید، روی \"بازسازی\" کلیک کنید.", "admin.image.publicLinkTitle": "نمک پیوند عمومی:", "admin.image.shareDescription": "به کاربران اجازه می دهد پیوندهای عمومی فایل ها و تصاویر را به اشتراک بگذارند.", "admin.image.shareTitle": "فعال کردن پیوندهای فایل عمومی: ", "admin.image.storeAmazonS3": "آمازون S3", "admin.image.storeDescription": "سیستم ذخیره سازی که در آن فایل ها و پیوست های تصویر ذخیره می شوند.\n \nانتخاب \"Amazon S3\" فیلدها را قادر می سازد تا اطلاعات کاربری آمازون و جزئیات سطل شما را وارد کنند.\n \nبا انتخاب \"Local File System\" این فیلد را قادر می سازد تا یک فهرست فایل محلی را مشخص کند.", "admin.image.storeLocal": "سیستم فایل محلی", "admin.image.storeTitle": "سیستم ذخیره سازی فایل:", "admin.integrations.botAccounts": "حساب های ربات", "admin.integrations.botAccounts.title": "حساب های ربات", "admin.integrations.cors": "CORS", "admin.integrations.gif": "GIF (بتا)", "admin.integrations.integrationManagement": "مدیریت یکپارچه سازی", "admin.integrations.integrationManagement.title": "مدیریت یکپارچه سازی", "admin.jobTable.cancelButton": "انصراف", "admin.jobTable.downloadLink": "د<PERSON><PERSON><PERSON>د", "admin.jobTable.headerExtraInfo": "جزئیات", "admin.jobTable.headerFiles": "فایل ها", "admin.jobTable.headerFinishAt": "زم<PERSON> خاتمه", "admin.jobTable.headerRunTime": "زمان اجرا", "admin.jobTable.headerStatus": "وضعیت", "admin.jobTable.jobId": "شناسه کار: ", "admin.jobTable.lastActivityAt": "آخرین فعالیت: ", "admin.jobTable.runLengthMinutes": " دقایق", "admin.jobTable.runLengthSeconds": " ثانیه", "admin.jobTable.statusCanceled": "لغو شد", "admin.jobTable.statusCanceling": "در حال لغو...", "admin.jobTable.statusError": "خطا", "admin.jobTable.statusInProgress": "در حال پیش رفت", "admin.jobTable.statusPending": "در انتظار", "admin.jobTable.statusSuccess": "مو<PERSON><PERSON><PERSON>ت", "admin.jobTable.statusWarning": "هشدار", "admin.ldap.adminFilterEx": "به عنوان مثال: \"(objectClass=user)\"", "admin.ldap.adminFilterFilterDesc": "(اختیاری) یک فیلتر AD/LDAP را وارد کنید تا از آن برای تعیین مدیران سیستم استفاده کنید. کاربران انتخاب شده توسط پرس و جو به سرور Mattermost شما به عنوان سرپرست سیستم دسترسی خواهند داشت. به طور پیش فرض، مدیران سیستم به کنسول سیستم Mattermost دسترسی کامل دارند.\n \nاعضای موجود که با این ویژگی شناسایی می شوند، پس از ورود به سیستم، از عضو به مدیر سیستم ارتقا می یابند. ورود بعدی بر اساس طول جلسه تنظیم شده در **کنسول سیستم > طول جلسه** است. به شدت توصیه می شود که به صورت دستی کاربران را به اعضا در **کنسول سیستم > مدیریت کاربر** تنزل دهید تا مطمئن شوید دسترسی فوراً محدود می شود.\n \nتوجه: در صورت حذف/تغییر این فیلتر، مدیران سیستمی که از طریق این فیلتر ارتقا یافته اند به اعضا تنزل پیدا می کنند و دسترسی به کنسول سیستم را حفظ نخواهند کرد. وقتی از این فیلتر استفاده نمی‌شود، مدیران سیستم را می‌توان به‌صورت دستی در **کنسول سیستم > مدیریت کاربر** ارتقا/تنزل داد.", "admin.ldap.adminFilterTitle": "فیلتر مدیریت:", "admin.ldap.baseDesc": "Base DN نام متمایز مکانی است که Mattermost باید جستجوی خود را برای کاربر و اشیاء گروه در درخت AD/LDAP آغاز کند.", "admin.ldap.baseEx": "به عنوان مثال: \"ou=نام واحد،dc=corp،dc=example,dc=com\"", "admin.ldap.baseTitle": "DN پایه:", "admin.ldap.bindPwdDesc": "رمز عبور کاربر داده شده در \"Bind Username\".", "admin.ldap.bindPwdTitle": "اتصال رمز عبور:", "admin.ldap.bindUserDesc": "نام کاربری مورد استفاده برای انجام جستجوی AD/LDAP. این معمولاً باید یک حساب کاربری باشد که به طور خاص برای استفاده با Mattermost ایجاد شده است. باید دسترسی محدود به خواندن بخشی از درخت AD/LDAP مشخص شده در قسمت Base DN داشته باشد.", "admin.ldap.bindUserTitle": "پیوند نام کاربری:", "admin.ldap.emailAttrDesc": "ویژگی در سرور AD/LDAP که برای پر کردن فیلد آدرس ایمیل در Mattermost استفاده می‌شود.", "admin.ldap.emailAttrEx": "به عنوان مثال: \"mail\" یا \"userPrincipalName\"", "admin.ldap.emailAttrTitle": "ویژگی ایمیل:", "admin.ldap.enableAdminFilterTitle": "فیلتر مدیریت را فعال کنید", "admin.ldap.enableDesc": "وقتی درست است، Mattermost اجازه ورود با استفاده از AD/LDAP را می دهد", "admin.ldap.enableSyncDesc": "وقتی درست است، Mattermost به صورت دوره‌ای کاربران را از AD/LDAP همگام‌سازی می‌کند. هنگامی که نادرست است، ویژگی های کاربر از AD/LDAP فقط در حین ورود کاربر به روز می شوند.", "admin.ldap.enableSyncTitle": "فعال کردن همگام سازی با AD/LDAP:", "admin.ldap.enableTitle": "فعال کردن ورود به سیستم با AD/LDAP:", "admin.ldap.firstnameAttrDesc": "(اختیاری) ویژگی در سرور AD/LDAP که برای پر کردن نام کوچک کاربران در Mattermost استفاده می‌شود. وقتی تنظیم شود، کاربران نمی توانند نام کوچک خود را ویرایش کنند، زیرا با سرور LDAP همگام شده است. وقتی خالی بماند، کاربران می‌توانند نام کوچک خود را در **نمایه > تنظیمات نمایه** تنظیم کنند.", "admin.ldap.firstnameAttrEx": "به عنوان مثال: \"given<PERSON>ame\"", "admin.ldap.firstnameAttrTitle": "ویژگی نام:", "admin.ldap.groupDisplayNameAttributeDesc": "ویژگی در سرور AD/LDAP که برای پر کردن نام‌های نمایشگر گروه استفاده می‌شود.", "admin.ldap.groupDisplayNameAttributeEx": "به عنوان مثال: \"cn\"", "admin.ldap.groupDisplayNameAttributeTitle": "ویژگی نام نمایشگر گروه:", "admin.ldap.groupFilterEx": "به عنوان مثال: \"(objectClass=group)\"", "admin.ldap.groupFilterFilterDesc": "(اختیاری) یک فیلتر AD/LDAP برای استفاده در هنگام جستجوی اشیاء گروه وارد کنید. فقط گروه‌های انتخاب شده توسط پرس و جو در دسترس Mattermost خواهند بود. از [User Management > Groups] ({siteURL}/admin_console/user_management/groups)، گروه‌های AD/LDAP را انتخاب و پیکربندی کنید.", "admin.ldap.groupFilterTitle": "فیلتر گروه:", "admin.ldap.groupIdAttributeDesc": "ویژگی در سرور AD/LDAP به عنوان یک شناسه منحصر به فرد برای گروه ها استفاده می شود. این باید یک ویژگی AD/LDAP با مقداری باشد که تغییر نمی‌کند، مانند «entryUUID» برای LDAP یا «objectGUID» برای اکتیو دایرکتوری.", "admin.ldap.groupIdAttributeEx": "به عنوان مثال: \"objectGUID\" یا \"entryUUID\"", "admin.ldap.groupIdAttributeTitle": "ویژگی شناسه گروه:", "admin.ldap.guestFilterEx": "به عنوان مثال: \"(objectClass=user)\"", "admin.ldap.guestFilterFilterDesc": "(اختیاری) نیاز به فعال کردن دسترسی مهمان قبل از اعمال دارد. یک فیلتر AD/LDAP را وارد کنید تا هنگام جستجوی اشیاء مهمان از آن استفاده کنید. فقط کاربرانی که توسط پرس و جو انتخاب شده اند می توانند به Mattermost به عنوان مهمان دسترسی داشته باشند. مهمانان پس از ورود به سیستم از دسترسی به تیم ها یا کانال ها جلوگیری می کنند تا زمانی که یک تیم و حداقل یک کانال به آنها اختصاص داده شود.\n \nتوجه: در صورت حذف/تغییر این فیلتر، مهمانان فعال به عضویت ارتقاء نخواهند یافت و نقش مهمان خود را حفظ خواهند کرد. میهمانان را می توان در **کنسول سیستم > مدیریت کاربر** ارتقا داد.\n \n \nاعضای موجود که با این ویژگی به عنوان مهمان شناسایی می شوند، زمانی که از آنها خواسته می شود که وارد سیستم شوند، از یک عضو به یک مهمان تنزل می یابند. ورود بعدی بر اساس طول جلسه تنظیم شده در **کنسول سیستم > طول جلسه** است. به شدت توصیه می شود که به صورت دستی کاربران را به مهمانان در **کنسول سیستم > مدیریت کاربر ** کاهش دهید تا اطمینان حاصل شود که دسترسی بلافاصله محدود می شود.", "admin.ldap.guestFilterTitle": "فیلتر مهمان:", "admin.ldap.idAttrDesc": "ویژگی در سرور AD/LDAP به عنوان یک شناسه منحصر به فرد در Mattermost استفاده می شود. باید یک ویژگی AD/LDAP با مقداری باشد که تغییر نمی‌کند، مانند «uid» برای LDAP یا «objectGUID» برای اکتیو دایرکتوری. اگر ویژگی شناسه کاربر تغییر کند، یک حساب جدید Mattermost ایجاد می کند که با حساب قبلی او مرتبط نیست.\n \nاگر بعد از اینکه کاربران قبلاً وارد سیستم شده اند، نیاز به تغییر این فیلد دارید، از ابزار CLI <link>mattermost ldap idmigrate</link> استفاده کنید.", "admin.ldap.idAttrEx": "به عنوان مثال: \"objectGUID\" یا \"uid\"", "admin.ldap.idAttrTitle": "ویژگی شناسه: ", "admin.ldap.jobExtraInfo.addedGroupMembers": "اعضای گروه {groupMemberAddCount, number} اضافه شد.", "admin.ldap.jobExtraInfo.deactivatedUsers": "کاربران {deleteCount, number} غیرفعال شدند.", "admin.ldap.jobExtraInfo.deletedGroupMembers": "اعضای گروه {groupMemberDeleteCount, number} حذف شدند.", "admin.ldap.jobExtraInfo.deletedGroups": "گروه های {groupDeleteCount, number} حذف شد.", "admin.ldap.jobExtraInfo.updatedUsers": "کاربران {updateCount, number} به‌روزرسانی شدند.", "admin.ldap.lastnameAttrDesc": "(اختیاری) ویژگی در سرور AD/LDAP که برای پر کردن نام خانوادگی کاربران در Mattermost استفاده می‌شود. وقتی تنظیم شود، کاربران نمی توانند نام خانوادگی خود را ویرایش کنند، زیرا با سرور LDAP همگام شده است. وقتی خالی بماند، کاربران می توانند نام خانوادگی خود را در **نمایه > تنظیمات نمایه** تنظیم کنند.", "admin.ldap.lastnameAttrEx": "به عنوان مثال: \"sn\"", "admin.ldap.lastnameAttrTitle": "ویژگی نام خانوادگی:", "admin.ldap.ldap_test_button": "تست AD/LDAP", "admin.ldap.loginAttrDesc": "ویژگی در سرور AD/LDAP که برای ورود به Mattermost استفاده می شود. معمولاً این ویژگی همان قسمت \"ویژگی نام کاربری\" در بالا است.\n \nاگر تیم شما معمولاً از دامنه/نام کاربری برای ورود به سرویس‌های دیگر با AD/LDAP استفاده می‌کند، می‌توانید دامنه/نام کاربری را در این قسمت وارد کنید تا هماهنگی بین سایت‌ها حفظ شود.", "admin.ldap.loginAttrTitle": "شناسه ورود به سیستم: ", "admin.ldap.loginIdAttrEx": "به عنوان مثال: \"sAMAccountName\"", "admin.ldap.loginNameDesc": "متن نگهدارنده مکان که در قسمت ورود به سیستم در صفحه ورود ظاهر می شود. پیش‌فرض «نام کاربری AD/LDAP».", "admin.ldap.loginNameEx": "به عنوان مثال: \"AD/LDAP نام کاربری\"", "admin.ldap.loginNameTitle": "نام فیلد ورود:", "admin.ldap.maxPageSizeEx": "به عنوان مثال: \"2000\"", "admin.ldap.maxPageSizeHelpText": "حداکثر تعداد کاربرانی که سرور Mattermost از سرور AD/LDAP در یک زمان درخواست می کند. 0 نامحدود است.", "admin.ldap.maxPageSizeTitle": "حداکثر اندازه صفحه:", "admin.ldap.nicknameAttrDesc": "(اختیاری) ویژگی در سرور AD/LDAP که برای پر کردن نام مستعار کاربران در Mattermost استفاده می‌شود. وقتی تنظیم شود، کاربران نمی توانند نام مستعار خود را ویرایش کنند، زیرا با سرور LDAP همگام شده است. وقتی خالی بماند، کاربران می توانند نام مستعار خود را در **نمایه > تنظیمات نمایه** تنظیم کنند.", "admin.ldap.nicknameAttrEx": "به عنوان مثال: \"نام مستعار\"", "admin.ldap.nicknameAttrTitle": "ویژگی نام مستعار:", "admin.ldap.pictureAttrDesc": "ویژگی در سرور AD/LDAP که برای پر کردن عکس نمایه در Mattermost استفاده می‌شود.", "admin.ldap.pictureAttrEx": "به عنوان مثال: \"عکس کوچک\" یا \"jpegPhoto\"", "admin.ldap.pictureAttrTitle": "ویژگی تصویر نمایه:", "admin.ldap.portDesc": "پورت Mattermost برای اتصال به سرور AD/LDAP استفاده خواهد شد. پیش فرض 389 است.", "admin.ldap.portEx": "به عنوان مثال: \"389\"", "admin.ldap.portTitle": "پورت AD/LDAP:", "admin.ldap.positionAttrDesc": "(اختیاری) ویژگی در سرور AD/LDAP که برای پر کردن فیلد موقعیت در Mattermost استفاده می‌شود. وقتی تنظیم شود، کاربران نمی توانند موقعیت خود را ویرایش کنند، زیرا با سرور LDAP همگام شده است. وقتی خالی بماند، کاربران می‌توانند موقعیت خود را در **نمایه > تنظیمات نمایه** تنظیم کنند.", "admin.ldap.positionAttrEx": "به عنوان مثال: \"عنوان\"", "admin.ldap.positionAttrTitle": "ویژگی موقعیت:", "admin.ldap.privateKeyFileFileDesc": "فایل کلید خصوصی برای گواهی TLS. اگر از گواهی های کلاینت TLS به عنوان مکانیزم احراز هویت اولیه استفاده کنید. این توسط ارائه دهنده احراز هویت LDAP شما ارائه می شود.", "admin.ldap.privateKeyFileFileRemoveDesc": "فایل کلید خصوصی را برای گواهی TLS حذف کنید.", "admin.ldap.privateKeyFileTitle": "<PERSON><PERSON><PERSON><PERSON> خصوصی:", "admin.ldap.publicCertificateFileDesc": "فایل گواهی عمومی برای گواهی TLS. اگر از گواهی های کلاینت TLS به عنوان مکانیزم احراز هویت اولیه استفاده کنید. این توسط ارائه دهنده احراز هویت LDAP شما ارائه می شود.", "admin.ldap.publicCertificateFileRemoveDesc": "فایل گواهی عمومی را برای گواهی TLS حذف کنید.", "admin.ldap.publicCertificateFileTitle": "گواهی عمومی:", "admin.ldap.queryDesc": "مقدار مهلت زمانی برای درخواست های سرور AD/LDAP. اگر خطاهای مهلت زمانی ناشی از کندی سرور AD/LDAP را دریافت می کنید، افزایش دهید.", "admin.ldap.queryEx": "به عنوان مثال: \"60\"", "admin.ldap.queryTitle": "مهلت پرس و جو (ثانیه):", "admin.ldap.remove.privKey": "کلید خصوصی گواهی TLS را حذف کنید", "admin.ldap.remove.sp_certificate": "حذف گواهی ارائه دهنده خدمات", "admin.ldap.removing.certificate": "در حال حذف گواهی...", "admin.ldap.removing.privKey": "در حال حذف کلید خصوصی...", "admin.ldap.serverDesc": "دامنه یا آدرس IP سرور AD/LDAP.", "admin.ldap.serverEx": "به عنوان مثال: \"*********\"", "admin.ldap.serverTitle": "سرور AD/LDAP:", "admin.ldap.skipCertificateVerification": "رد شدن از تأیید گواهی:", "admin.ldap.skipCertificateVerificationDesc": "از مرحله تأیید گواهی برای اتصالات TLS یا STARTTLS می گذرد. رد شدن از تأیید گواهی برای محیط های تولیدی که TLS مورد نیاز است توصیه نمی شود.", "admin.ldap.syncIntervalHelpText": "AD/LDAP Synchronization به روز رسانی مهم ترین اطلاعات کاربر برای منعکس کردن به روز رسانی در سرور AD/LDAP. به عنوان مثال، هنگامی که نام کاربر در سرور AD/LDAP تغییر می کند، هنگامی که همگام سازی انجام می شود، تغییر در Mattermost به روز می شود. حساب‌هایی که از سرور AD/LDAP حذف یا غیرفعال شده‌اند، حساب‌های Mattermost آن‌ها روی «غیرفعال» تنظیم شده و جلسات حسابشان لغو می‌شود. Mattermost همگام سازی را در بازه وارد شده انجام می دهد. به عنوان مثال، اگر 60 وارد شود، Mattermost هر 60 دقیقه همگام می شود.", "admin.ldap.syncIntervalTitle": "فاصله همگام سازی (دق<PERSON><PERSON><PERSON>):", "admin.ldap.syncNowHelpText": "همگام سازی AD/LDAP را فوراً آغاز می کند. برای وضعیت هر همگام سازی به جدول زیر مراجعه کنید. لطفاً برای عیب‌یابی خطاها، «کنسول سیستم > گزارش‌ها» و <link>اسناد</link> را مرور کنید.", "admin.ldap.sync_button": "AD/LDAP اکنون همگام سازی کنید", "admin.ldap.testFailure": "شکست تست AD/LDAP: {error}", "admin.ldap.testHelpText": "آزمایش می کند که آیا سرور Mattermost می تواند به سرور AD/LDAP مشخص شده متصل شود یا خیر. لطفاً برای عیب‌یابی خطاها، «کنسول سیستم > گزارش‌ها» و <link>اسناد</link> را مرور کنید.", "admin.ldap.testSuccess": "تست AD/LDAP با موفقیت انجام شد", "admin.ldap.uploading.certificate": "در حال بارگذاری گواهی...", "admin.ldap.uploading.privateKey": "در حال بارگذاری کلید خصوصی...", "admin.ldap.userFilterDisc": "(اختیاری) یک فیلتر AD/LDAP را برای استفاده در هنگام جستجوی اشیاء کاربر وارد کنید. فقط کاربران انتخاب شده توسط پرس و جو می توانند به Mattermost دسترسی داشته باشند. برای اکتیو دایرکتوری، درخواست فیلتر کردن کاربران غیرفعال به صورت (&(objectCategory=Person)(!(UserAccountControl:1.2.840.113556.1.4.803:=2)) است.", "admin.ldap.userFilterEx": "به عنوان مثال: \"(objectClass=user)\"", "admin.ldap.userFilterTitle": "فیلتر کاربر:", "admin.ldap.usernameAttrDesc": "ویژگی در سرور AD/LDAP که برای پر کردن فیلد نام کاربری در Mattermost استفاده می‌شود. این ممکن است مانند ویژگی شناسه ورود به سیستم باشد.", "admin.ldap.usernameAttrEx": "به عنوان مثال: \"sAMAccountName\"", "admin.ldap.usernameAttrTitle": "ویژگی نام کاربری:", "admin.ldap_feature_discovery.call_to_action.primary": "شروع آزمایشی", "admin.ldap_feature_discovery.call_to_action.secondary": "مطالعه بیشتر", "admin.ldap_feature_discovery.copy": "وقتی Mattermost را به Active Directory/LDAP سازمان خود متصل می کنید، کاربران می توانند بدون نیاز به ایجاد نام کاربری و رمز عبور جدید وارد شوند.", "admin.ldap_feature_discovery.title": "Active Directory/LDAP را با Mattermost Professional ادغام کنید", "admin.ldap_feature_discovery_cloud.call_to_action.primary_sales": "تماس با بخش فروش", "admin.license.Trial": "آزمایش", "admin.license.choose": "انتخاب فایل", "admin.license.confirm-license-removal.cancel": "لغو", "admin.license.confirm-license-removal.confirm": "تاییدکردن", "admin.license.confirm-license-removal.title": "اطمینان دارید؟", "admin.license.enterprise.restarting": "راه اندازی مجدد", "admin.license.enterprise.upgrade": "به نسخه Enterprise ارتقا دهید", "admin.license.enterprise.upgrade.eeLicenseLink": "مجوز نسخه Enterprise", "admin.license.enterprise.upgrading": "ارتقاء {percentage}%", "admin.license.enterpriseEdition": "نسخه تجاری", "admin.license.enterpriseEdition.subtitle": "این یک نسخه Enterprise برای طرح Mattermost {skuName} است", "admin.license.enterprisePlanSubtitle": "ما اینجا هستیم تا با شما و نیازهای شما کار کنیم. برای دریافت صندلی های بیشتر در طرح خود، همین امروز با ما تماس بگیرید.", "admin.license.enterprisePlanTitle": "آیا نیاز به افزایش تعداد کار دارید؟", "admin.license.freeEdition.title": "رایگان", "admin.license.key": "کلید مجوز: ", "admin.license.keyRemove": "حذف مجوز و تنزل دادن به مترموست رایگان", "admin.license.modal.done": "انجام‌شد", "admin.license.modal.upload": "بارگذاری", "admin.license.modal.uploading": "در حال بارگذاری", "admin.license.no-file-selected": "هیچ پرونده‌ای انتخاب نشده", "admin.license.purchaseEnterprisePlanSubtitle": "با خرید مجوز همین امروز به دسترسی خود به ویژگی های Enterprise ادامه دهید.", "admin.license.purchaseEnterprisePlanTitle": "طرح سازمانی را خریداری کنید", "admin.license.remove": "<PERSON><PERSON><PERSON>", "admin.license.removing": "در حال حذف مجوز...", "admin.license.title": "نسخه و مجوز", "admin.license.trial-request.startTrial": "شروع دوره آزمایشی", "admin.license.trial-request.title": "Mattermost Enterprise Edition را به صورت رایگان تا 30 روز آینده تجربه کنید. هیچ الزامی برای خرید یا کارت اعتباری لازم نیست. ", "admin.license.trialCard.contactSales": "تماس با بخش فروش", "admin.license.trialCard.licenseExpiring": "شما در حال حاضر در یک دوره آزمایشی رایگان مجوز Mattermost Enterprise ما هستید.", "admin.license.trialCard.purchase_license": "م<PERSON><PERSON><PERSON> خرید", "admin.license.upgradeTitle": "ارتقا به طرح حرفه ای", "admin.license.upgradeToEnterprise": "به طرح Enterprise ارتقا دهید", "admin.license.upload-modal.file": "پرونده", "admin.license.upload-modal.successfulUpgrade": "ارتقا موفقیت آمیز بود!", "admin.license.uploadFile": "بارگذاری پرونده", "admin.lockTeammateNameDisplay": "قفل نمایش نام هم تیمی برای همه کاربران: ", "admin.lockTeammateNameDisplayHelpText": "وقتی درست است، توانایی کاربران را برای تغییر تنظیمات در قسمت **تنظیمات > نمایشگر > نمایش نام هم تیمی** غیرفعال می کند.", "admin.log.consoleDescription": "معمولاً در تولید روی false تنظیم می شود. برنامه‌نویسان ممکن است این فیلد را روی true تنظیم کنند تا پیام‌های گزارش خروجی به کنسول بر اساس گزینه سطح کنسول ارسال شود. اگر درست باشد، سرور پیام ها را به جریان خروجی استاندارد (stdout) می نویسد. تغییر این تنظیم نیاز به راه اندازی مجدد سرور قبل از اعمال دارد.", "admin.log.consoleJsonTitle": "گزارش های کنسول خروجی به صورت JSON:", "admin.log.consoleTitle": "گزارش های خروجی به کنسول: ", "admin.log.enableDiagnostics": "فعال کردن تشخیص و گزارش خطا:", "admin.log.enableDiagnosticsDescription": "برای بهبود کیفیت و عملکرد Mattermost با ارسال گزارش خطا و اطلاعات تشخیصی به Mattermost، Inc، این ویژگی را فعال کنید. بیشتر.", "admin.log.enableWebhookDebugging": "فعال کردن Webhook Debugging:", "admin.log.enableWebhookDebuggingDescription": "وقتی درست باشد، پیام‌های اشکال‌زدایی وب هوک را به گزارش‌های سرور ارسال می‌کند. برای همچنین خروجی بدنه درخواست وبکهک های ورودی، {boldedLogLevel} را روی 'DEBUG' تنظیم کنید.", "admin.log.fileDescription": "معمولاً در تولید روی true تنظیم می شود. هنگامی که درست است، رویدادهای ثبت شده در فایل mattermost.log در دایرکتوری مشخص شده در قسمت File Log Directory نوشته می شوند. گزارش‌ها در 10000 خط چرخانده شده و در فایلی در همان فهرست بایگانی می‌شوند و نامی با مهر تاریخ و شماره سریال داده می‌شود. برای مثال mattermost.2017-03-31.001. تغییر این تنظیم نیاز به راه اندازی مجدد سرور قبل از اعمال دارد.", "admin.log.fileJsonTitle": "خروجی لاگ فایل به صورت JSON:", "admin.log.fileLevelDescription": "این تنظیم سطح جزئیاتی را که رویدادهای گزارش در فایل گزارش نوشته می‌شوند، تعیین می‌کند. ERROR: فقط پیام های خطا را خروجی می کند. INFO: پیام های خطا و اطلاعات مربوط به راه اندازی و مقداردهی اولیه را خروجی می دهد. DEBUG: جزئیات بالایی را برای توسعه دهندگانی که روی مشکلات اشکال زدایی کار می کنند چاپ می کند.", "admin.log.fileLevelTitle": "سطح گزارش فایل:", "admin.log.fileTitle": "خروجی گزارش‌ها به فایل: ", "admin.log.jsonDescription": "وقتی درست است، رویدادهای ثبت‌شده در قالب JSON قابل خواندن توسط ماشین نوشته می‌شوند. در غیر این صورت به صورت متن ساده چاپ می شوند. تغییر این تنظیم نیاز به راه اندازی مجدد سرور قبل از اعمال دارد.", "admin.log.levelDescription": "این تنظیم سطح جزئیاتی را که رویدادهای گزارش روی کنسول نوشته می‌شوند، تعیین می‌کند. ERROR: فقط پیام های خطا را خروجی می کند. INFO: پیام های خطا و اطلاعات مربوط به راه اندازی و مقداردهی اولیه را خروجی می دهد. DEBUG: جزئیات بالایی را برای توسعه دهندگانی که روی مشکلات اشکال زدایی کار می کنند چاپ می کند.", "admin.log.levelTitle": "سطح گزارش کنسول:", "admin.log.locationDescription": "محل فایل های لاگ. اگر خالی باشد، در دایرکتوری ./logs ذخیره می شوند. مسیری که تعیین می کنید باید وجود داشته باشد و Mattermost باید مجوز نوشتن در آن داشته باشد. تغییر این تنظیم نیاز به راه اندازی مجدد سرور قبل از اعمال دارد.", "admin.log.locationPlaceholder": "محل <PERSON><PERSON><PERSON><PERSON> خود را وارد کنید", "admin.log.locationTitle": "پوشه ردپای پرونده‌ها:", "admin.log.logLevel": "سطح ورود به سیستم", "admin.logs.Alllevels": "همه سطح‌ها", "admin.logs.Debug": "اشکال زدایی", "admin.logs.Error": "اخطار", "admin.logs.Info": "اطلاع‌رسانی", "admin.logs.ReloadLogs": "بارگذاری مجدد ردپاها", "admin.logs.Warn": "هشدار", "admin.logs.bannerDesc": "برای جستجوی کاربران بر اساس شناسه کاربری یا شناسه رمز، به مدیریت کاربر > کاربران بروید و شناسه را در فیلتر جستجو قرار دهید.", "admin.logs.caller": "تماس گیرنده", "admin.logs.options": "امکانات", "admin.logs.title": "گزارش های سرور", "admin.manage_roles.additionalRoles": "مجوزهای اضافی را برای حساب انتخاب کنید. <link>درباره نقش ها و مجوزها بیشتر بخوانید</link>.", "admin.manage_roles.allowUserAccessTokens": "به این حساب اجازه دهید <link>نشانه‌های دسترسی شخصی</link>  ایج<PERSON> کند.", "admin.manage_roles.allowUserAccessTokensDesc": "حذف این مجوز، نشانه‌های موجود را حذف نمی‌کند. برای حذف آنها، به منوی Manage Tokens کاربر بروید.", "admin.manage_roles.cancel": "انصراف", "admin.manage_roles.manageRolesTitle": "نقش ها را مدیریت کنید", "admin.manage_roles.postAllPublicRole": "دسترسی به پست به همه کانال های عمومی Mattermost.", "admin.manage_roles.postAllPublicRoleTitle": "پست: کانال ها", "admin.manage_roles.postAllRole": "دسترسی به پست به تمام کانال های Mattermost از جمله پیام های مستقیم.", "admin.manage_roles.postAllRoleTitle": "پست: همه", "admin.manage_roles.save": "صرفه جویی", "admin.manage_roles.saveError": "امکان ذخیره نقش ها وجود ندارد.", "admin.manage_roles.systemAdmin": "ادمین سیستم", "admin.manage_roles.systemMember": "عضو", "admin.manage_tokens.manageTokensTitle": "توکن های دسترسی شخصی را مدیریت کنید", "admin.manage_tokens.userAccessTokensDescription": "نشانه‌های دسترسی شخصی مانند نشانه‌های جلسه عمل می‌کنند و می‌توانند با ادغام برای <linkAuthtentication>تعامل با این سرور Mattermost</linkAuthtentication>  استفاده شوند. در صورت غیرفعال شدن کاربر، توکن ها غیرفعال می شوند. درباره <linkPersonalAccessTokens>نشان‌های دسترسی شخصی</linkPersonalAccessTokens>  بیشتر بیاموزید.", "admin.manage_tokens.userAccessTokensIdLabel": "شناسه رمز: ", "admin.manage_tokens.userAccessTokensNameLabel": "توضیحات توکن: ", "admin.manage_tokens.userAccessTokensNone": "بدون نشانه دسترسی شخصی", "admin.member_list_group.name": "نام", "admin.member_list_group.notFound": "کاربری پیدا نشد", "admin.metrics.enableDescription": "زمانی که درست باشد، Mattermost جمع آوری و پروفایل نظارت بر عملکرد را فعال می کند. لطفاً برای کسب اطلاعات بیشتر درباره پیکربندی نظارت بر عملکرد برای Mattermost، به <link>Documentation</link>  مراجعه کنید.", "admin.metrics.enableTitle": "فعال کردن نظارت بر عملکرد:", "admin.metrics.listenAddressDesc": "آدرسی که سرور به آن گوش می دهد تا معیارهای عملکرد را نشان دهد.", "admin.metrics.listenAddressEx": "به عنوان مثال: \":8067\"", "admin.metrics.listenAddressTitle": "آدرس گوش دادن:", "admin.mfa.bannerDesc": "<link>احراز هویت چند عاملی</link> برای حساب‌های دارای AD/LDAP یا ورود به ایمیل در دسترس است. اگر روش‌های ورود به سیستم دیگر استفاده می‌شود، MFA باید با ارائه‌دهنده احراز هویت پیکربندی شود.", "admin.nav.administratorsGuide": "راهنمای مدیر", "admin.nav.commercialSupport": "پشتیبانی تجاری", "admin.nav.menuAriaLabel": "منوی کنسول مدیریت", "admin.nav.switch": "انتخاب تیم", "admin.nav.troubleshootingForum": "انجمن عیب یابی", "admin.notices.enableAdminNoticesDescription": "وقتی فعال باشد، مدیران سیستم اخطاریه‌هایی را درباره ارتقای سرور موجود و ویژگی‌های مدیریت سیستم مربوطه دریافت خواهند کرد. <link>درباره اعلامیه‌ها بیشتر بیاموزید</link> در اسناد ما.", "admin.notices.enableAdminNoticesTitle": "فعال کردن اطلاعیه های مدیریت: ", "admin.notices.enableEndUserNoticesDescription": "وقتی فعال باشد، همه کاربران اخطاریه‌هایی درباره ارتقاهای مشتری در دسترس و ویژگی‌های کاربر نهایی مرتبط برای بهبود تجربه کاربر دریافت خواهند کرد. <link>درباره اعلامیه‌ها بیشتر بیاموزید</link> در اسناد ما.", "admin.notices.enableEndUserNoticesTitle": "فعال کردن اعلان‌های کاربر نهایی: ", "admin.oauth.gitlab": "GitLab", "admin.oauth.google": "Google Apps", "admin.oauth.off": "ورود به سیستم از طریق ارائه دهنده OAuth 2.0 مجاز نیست", "admin.oauth.office365": "دفتر 365", "admin.oauth.openid": "OpenID Connect (Other)", "admin.oauth.providerDescription": "وقتی درست باشد، Mattermost می‌تواند به عنوان یک ارائه‌دهنده خدمات OAuth 2.0 عمل کند که به Mattermost اجازه می‌دهد درخواست‌های API از برنامه‌های خارجی را مجاز کند. برای کسب اطلاعات بیشتر به <link>اسناد</link> مراجعه کنید.", "admin.oauth.providerTitle": "فعال کردن OAuth 2.0 Service Provider: ", "admin.office365.EnableMarkdownDesc": "1. h<linkLogin>ورود به سیستم</linkLogin> به حساب Microsoft یا Office 365 خود. مطمئن شوید که این حساب در همان <linkTenant>مستاجر</linkTenant>  است که می‌خواهید کاربران با آن وارد شوند.\n2. به <linkApps>https://apps.dev.microsoft.com</linkApps> بروید، روی برو به Azure Portal کلیک کنید > روی ثبت نام جدید کلیک کنید.\n3. از \"Mattermost - your-company-name\" به عنوان نام برنامه استفاده کنید، روی ثبت نام کلیک کنید، شناسه مشتری و شناسه مستاجر را در زیر قرار دهید.\n4. روی Authentication، در زیر پلتفرم، روی افزودن پلتفرم کلیک کنید، Web را انتخاب کنید و your-mattermost-url/signup/office365/complete را وارد کنید (مثال: http://localhost:8065/signup/office365/complete) تحت Redirect URI. همچنین علامت Allow Implicit Flow را بردارید.\n5. روی Certificates & Secrets، Generate New Client Secret کلیک کنید و مقدار مخفی را در قسمت Client Secret زیر قرار دهید.", "admin.office365.authTitle": "نقطه پایان تأیید اعتبار:", "admin.office365.clientIdDescription": "Application/Client ID که هنگام ثبت برنامه خود در Microsoft دریافت کرده اید.", "admin.office365.clientIdExample": "به عنوان مثال: \"adf3sfa2-ag3f-sn4n-ids0-sh1hdax192qq\"", "admin.office365.clientIdTitle": "شناسه برنامه:", "admin.office365.clientSecretDescription": "رمز عبور مخفی برنامه که هنگام ثبت برنامه خود در مایکروسافت ایجاد کرده اید.", "admin.office365.clientSecretExample": "به عنوان مثال: \"shAieM47sNBfgl20f8ci294\"", "admin.office365.clientSecretTitle": "رمز عبور مخفی برنامه:", "admin.office365.directoryIdDescription": "شناسه دایرکتوری (مستاجر) که هنگام ثبت برنامه خود در مایکروسافت دریافت کرده اید.", "admin.office365.directoryIdExample": "به عنوان مثال: \"adf3sfa2-ag3f-sn4n-ids0-sh1hdax192qq\"", "admin.office365.directoryIdTitle": "شناسه دایرکتوری (مستاجر):", "admin.office365.tokenTitle": "نقطه پایان نشانه:", "admin.office365.userTitle": "User API Endpoint:", "admin.openIdConvert.help": "مطالعه بیشتر", "admin.openid.buttonColorTitle": "رنگ دکمه:", "admin.openid.buttonTextEx": "متن سفارشی دکمه", "admin.openid.buttonTextTitle": "نام دکمه:", "admin.openid.clientIdExample": "مثال: \"adf3sfa2-ag3f-sn4n-ids0-sh1hdax192qq\"", "admin.openid.clientIdTitle": "شناسه کلاینت:", "admin.openid.clientSecretExample": "مثال: \"adf3sfa2-ag3f-sn4n-ids0-sh1hdax192qq\"", "admin.openid.clientSecretTitle": "عبارت‌ر<PERSON>ز کلاینت:", "admin.openid.discovery.placeholder": "https://id.mydomain.com/.well-known/openid-configuration", "admin.openid.discoveryEndpointTitle": "Discovery Endpoint:", "admin.openid.gitlab": "GitLab", "admin.openid.google": "Google Apps", "admin.openid.office365": "Office 365", "admin.openid.select": "انتخاب ارائه‌دهنده سرویس:", "admin.openid_feature_discovery.copy": "از OpenID Connect برای احراز هویت و ورود به سیستم (SSO) با هر سرویسی که از مشخصات OIDC پشتیبانی می کند مانند Google، Office 365، Apple، Okta، OneLogin و غیره استفاده کنید.", "admin.openid_feature_discovery.title": "OpenID Connect را با Mattermost Professional ادغام کنید", "admin.password.lowercase": "حدا<PERSON><PERSON> یک حرف کوچک", "admin.password.minimumLength": "حداقل طول رمز عبور:", "admin.password.minimumLengthDescription": "حداقل تعداد کاراکترهای مورد نیاز برای رمز عبور معتبر. باید یک عدد کامل بزرگتر یا مساوی با {min} و کوچکتر یا مساوی با {max} باشد.", "admin.password.minimumLengthExample": "به عنوان مثال: \"5\"", "admin.password.number": "حد<PERSON><PERSON><PERSON> یک عدد", "admin.password.preview": "پیش نمایش پیام خطا", "admin.password.symbol": "حداقل یک نماد (به عنوان مثال \"~!@#$%^&*()\")", "admin.password.uppercase": "حداقل یک حرف بزرگ", "admin.permissions.group.convert_public_channel_to_private.description": "تبدیل کانال های عمومی به خصوصی", "admin.permissions.group.convert_public_channel_to_private.name": "تبدیل کانال ها", "admin.permissions.group.custom_groups.name": "گروه‌های سفارشی", "admin.permissions.group.delete_posts.description": "پست های خود و دیگران را حذف کنید.", "admin.permissions.group.delete_posts.name": "حذف پست ها", "admin.permissions.group.edit_posts.description": "پست های خود و دیگران را ویرایش کنید.", "admin.permissions.group.edit_posts.name": "ویرایش پست ها", "admin.permissions.group.guest_create_post.description": "به کاربران اجازه ایجاد پست بدهید.", "admin.permissions.group.guest_create_post.name": "ایجاد پست ها", "admin.permissions.group.guest_create_private_channel.description": "ایجاد کانال های خصوصی جدید", "admin.permissions.group.guest_create_private_channel.name": "ایج<PERSON> کانال ها", "admin.permissions.group.guest_delete_post.description": "پست های خود نویسنده را می توان حذف کرد.", "admin.permissions.group.guest_delete_post.name": "حذف پست های شخصی", "admin.permissions.group.guest_edit_post.description": "{editTimeLimitButton} پس از ارسال، به کاربران اجازه دهید پست های خود را ویرایش کنند.", "admin.permissions.group.guest_edit_post.name": "ویرایش پست های شخصی", "admin.permissions.group.guest_reactions.description": "افزودن و حذف واکنش‌ها در پست‌ها.", "admin.permissions.group.guest_reactions.name": "پست واکنش ها", "admin.permissions.group.guest_use_channel_mentions.description": "اعضای کانال را با @all، @channel و @here اطلاع دهید", "admin.permissions.group.guest_use_channel_mentions.name": "ذکر کانال", "admin.permissions.group.guest_use_group_mentions.description": "اعضای گروه را با ذکر گروه مطلع کنید", "admin.permissions.group.guest_use_group_mentions.name": "اشاره های گروهی", "admin.permissions.group.integrations.description": "OAuth 2.0، دستورات اسلش، وب هوک ها و ایموجی ها را مدیریت کنید.", "admin.permissions.group.integrations.name": "ادغام و سفارشی سازی", "admin.permissions.group.manage_private_channel_members_and_read_groups.description": "اعضای کانال خصوصی را اضافه و حذف کنید.", "admin.permissions.group.manage_private_channel_members_and_read_groups.name": "مدیریت اعضای کانال", "admin.permissions.group.manage_public_channel_members_and_read_groups.description": "اعضای کانال عمومی را اضافه و حذف کنید.", "admin.permissions.group.manage_public_channel_members_and_read_groups.name": "مدیریت اعضای کانال", "admin.permissions.group.manage_shared_channels.description": "مدیریت کانال‌های مشترک", "admin.permissions.group.manage_shared_channels.name": "کانال‌های مشترک", "admin.permissions.group.playbook_private.description": "مدی<PERSON><PERSON>ت", "admin.permissions.group.playbook_private.name": "مدیریت اجرانامه‌های خصوصی", "admin.permissions.group.playbook_public.description": "مدیریت اجرانامه‌های عمومی.", "admin.permissions.group.playbook_public.name": "مدیریت اجرانامه‌های عمومی.", "admin.permissions.group.posts.description": "نوشته ها را بنویسید، ویرایش کنید و حذف کنید.", "admin.permissions.group.posts.name": "مدیریت پست ها", "admin.permissions.group.private_channel.description": "ایجاد و بایگانی کانال ها، مدیریت تنظیمات و اعضا.", "admin.permissions.group.private_channel.name": "مدیریت کانال های خصوصی", "admin.permissions.group.public_channel.description": "به کانال ها بپیوندید، ایجاد و بایگانی کنید، تنظیمات و اعضا را مدیریت کنید.", "admin.permissions.group.public_channel.name": "مدیریت کانال های عمومی", "admin.permissions.group.reactions.description": "افزودن و حذف واکنش‌ها در پست‌ها.", "admin.permissions.group.reactions.name": "پست واکنش ها", "admin.permissions.group.runs.description": "مدیریت اجراها.", "admin.permissions.group.runs.name": "مدیریت اجراها", "admin.permissions.group.send_invites.description": "اعضای تیم را اضافه کنید، دعوت نامه های ایمیلی ارسال کنید و پیوند دعوت تیم را به اشتراک بگذارید.", "admin.permissions.group.send_invites.name": "اعضای تیم را اضافه کنید", "admin.permissions.group.teams.description": "ایجاد تیم و مدیریت اعضا.", "admin.permissions.group.teams.name": "تیم ها", "admin.permissions.group.teams_team_scope.description": "اعضای تیم را مدیریت کنید.", "admin.permissions.group.teams_team_scope.name": "تیم ها", "admin.permissions.inherited_from": "به ارث رسیده از [{name}]().", "admin.permissions.introBanner": "طرح‌های مجوز، مجوزهای پیش‌فرض را برای مدیران تیم، مدیران کانال و سایر افراد تنظیم می‌کنند. درباره طرح‌های مجوز در <link>اسناد</link> ما بیشتر بیاموزید .", "admin.permissions.loadMoreSchemes": "بارگیری طرح های بیشتر", "admin.permissions.loadingMoreSchemes": "بارگذاری...", "admin.permissions.permission.assign_system_admin_role.description": "نقش مدیر سیستم را تعیین کنید", "admin.permissions.permission.assign_system_admin_role.name": "نقش مدیر سیستم را تعیین کنید", "admin.permissions.permission.convert_private_channel_to_public.description": "تبدیل کانال های خصوصی به عمومی", "admin.permissions.permission.convert_private_channel_to_public.name": "تبدیل کانال ها", "admin.permissions.permission.convert_public_channel_to_private.description": "تبدیل کانال های عمومی به خصوصی", "admin.permissions.permission.convert_public_channel_to_private.name": "تبدیل کانال ها", "admin.permissions.permission.create_custom_group.name": "ایجاد", "admin.permissions.permission.create_direct_channel.description": "ایجاد کانال مستقیم", "admin.permissions.permission.create_direct_channel.name": "ایجاد کانال مستقیم", "admin.permissions.permission.create_emojis.description": "به کاربران اجازه ایجاد ایموجی سفارشی را بدهید.", "admin.permissions.permission.create_emojis.name": "ایموجی سفارشی ایجاد کنید", "admin.permissions.permission.create_group_channel.description": "ایجاد کانال گروهی", "admin.permissions.permission.create_group_channel.name": "ایجاد کانال گروهی", "admin.permissions.permission.create_post.description": "به کاربران اجازه ایجاد پست بدهید.", "admin.permissions.permission.create_post.name": "ایجاد پست ها", "admin.permissions.permission.create_private_channel.description": "ایجاد کانال های خصوصی جدید", "admin.permissions.permission.create_private_channel.name": "ایج<PERSON> کانال ها", "admin.permissions.permission.create_public_channel.description": "ایجاد کانال های عمومی جدید", "admin.permissions.permission.create_public_channel.name": "ایج<PERSON> کانال ها", "admin.permissions.permission.create_team.description": "تیم های جدید ایجاد کنید.", "admin.permissions.permission.create_team.name": "ایجاد تیم ها", "admin.permissions.permission.create_user_access_token.description": "رمز دسترسی کاربر را ایجاد کنید", "admin.permissions.permission.create_user_access_token.name": "رمز دسترسی کاربر را ایجاد کنید", "admin.permissions.permission.delete_custom_group.name": "<PERSON><PERSON><PERSON>", "admin.permissions.permission.delete_emojis.description": "به کاربران اجازه دهید تا ایموجی های سفارشی را که ایجاد کرده اند حذف کنند.", "admin.permissions.permission.delete_emojis.name": "ایموجی سفارشی خود را حذف کنید", "admin.permissions.permission.delete_others_emojis.description": "به کاربران اجازه دهید شکلک های سفارشی را که توسط سایر کاربران ایجاد شده است حذف کنند.", "admin.permissions.permission.delete_others_emojis.name": "ایموجی های سفارشی دیگران را حذف کنید", "admin.permissions.permission.delete_others_posts.description": "پست های ارسال شده توسط سایر کاربران قابل حذف است.", "admin.permissions.permission.delete_others_posts.name": "پست های دیگران را حذف کنید", "admin.permissions.permission.delete_post.description": "پست های خود نویسنده را می توان حذف کرد.", "admin.permissions.permission.delete_post.name": "حذف پست های شخصی", "admin.permissions.permission.delete_private_channel.description": "آرشیو کانال های خصوصی", "admin.permissions.permission.delete_private_channel.name": "آرشیو کانال ها", "admin.permissions.permission.delete_public_channel.description": "آرشیو کانال های عمومی", "admin.permissions.permission.delete_public_channel.name": "آرشیو کانال ها", "admin.permissions.permission.edit_custom_group.name": "ویرایش", "admin.permissions.permission.edit_other_users.description": "سایر کاربران را ویرایش کنید", "admin.permissions.permission.edit_other_users.name": "سایر کاربران را ویرایش کنید", "admin.permissions.permission.edit_others_posts.description": "به کاربران اجازه دهید پست های دیگران را ویرایش کنند.", "admin.permissions.permission.edit_others_posts.name": "ویرایش پست های دیگران", "admin.permissions.permission.edit_post.description": "{editTimeLimitButton} پس از ارسال، به کاربران اجازه دهید پست های خود را ویرایش کنند.", "admin.permissions.permission.edit_post.name": "ویرایش پست های شخصی", "admin.permissions.permission.import_team.description": "تیم واردات", "admin.permissions.permission.import_team.name": "تیم واردات", "admin.permissions.permission.invite_guest.description": "مهمانان را به کانال ها دعوت کنید و دعوت نامه های مهمان را ایمیل کنید.", "admin.permissions.permission.invite_guest.name": "مهمانان را دعوت کنید", "admin.permissions.permission.list_team_channels.description": "لیست کانال های تیم", "admin.permissions.permission.list_team_channels.name": "لیست کانال های تیم", "admin.permissions.permission.list_users_without_team.description": "لیست کاربران بدون تیم", "admin.permissions.permission.list_users_without_team.name": "لیست کاربران بدون تیم", "admin.permissions.permission.manage_channel_roles.description": "نقش های کانال را مدیریت کنید", "admin.permissions.permission.manage_channel_roles.name": "نقش های کانال را مدیریت کنید", "admin.permissions.permission.manage_custom_group_members.name": "مدیریت اعضا", "admin.permissions.permission.manage_incoming_webhooks.description": "وب هوک های ورودی را ایجاد، ویرایش و حذف کنید.", "admin.permissions.permission.manage_incoming_webhooks.name": "مدیریت وب هوک های ورودی", "admin.permissions.permission.manage_jobs.description": "مشاغل را مدیریت کنید", "admin.permissions.permission.manage_jobs.name": "مشاغل را مدیریت کنید", "admin.permissions.permission.manage_oauth.description": "توکن های برنامه OAuth 2.0 را ایجاد، ویرایش و حذف کنید.", "admin.permissions.permission.manage_oauth.name": "مدیریت برنامه های OAuth", "admin.permissions.permission.manage_outgoing_webhooks.description": "وب هوک های خروجی را ایجاد، ویرایش و حذف کنید.", "admin.permissions.permission.manage_outgoing_webhooks.name": "مدیریت وب هوک های خروجی", "admin.permissions.permission.manage_private_channel_properties.description": "نام ها، سرصفحه ها و اهداف کانال های خصوصی را به روز کنید.", "admin.permissions.permission.manage_private_channel_properties.name": "تنظیمات کانال را مدیریت کنید", "admin.permissions.permission.manage_public_channel_properties.description": "نام ها، سرصفحه ها و اهداف کانال های عمومی را به روز کنید.", "admin.permissions.permission.manage_public_channel_properties.name": "تنظیمات کانال را مدیریت کنید", "admin.permissions.permission.manage_roles.description": "نقش ها را مدیریت کنید", "admin.permissions.permission.manage_roles.name": "نقش ها را مدیریت کنید", "admin.permissions.permission.manage_secure_connections.description": "ایجاد، حذف و مشاهده اتصالات امن برای کانال های مشترک", "admin.permissions.permission.manage_secure_connections.name": "اتصالات امن را مدیریت کنید", "admin.permissions.permission.manage_shared_channels.name": "مدی<PERSON><PERSON>ت", "admin.permissions.permission.manage_slash_commands.description": "دستورات اسلش سفارشی را ایجاد، ویرایش و حذف کنید.", "admin.permissions.permission.manage_slash_commands.name": "دستورات اسلش را مدیریت کنید", "admin.permissions.permission.manage_system.description": "سیستم را مدیریت کنید", "admin.permissions.permission.manage_system.name": "سیستم را مدیریت کنید", "admin.permissions.permission.manage_team.description": "تیم را مدیریت کنید", "admin.permissions.permission.manage_team.name": "تیم را مدیریت کنید", "admin.permissions.permission.manage_team_roles.description": "نقش های تیم را مدیریت کنید", "admin.permissions.permission.manage_team_roles.name": "نقش های تیم را مدیریت کنید", "admin.permissions.permission.permanent_delete_user.description": "حذف دائمی کاربر", "admin.permissions.permission.permanent_delete_user.name": "حذف دائمی کاربر", "admin.permissions.permission.playbook_private_create.name": "ایجاد اجرانامه خصوصی", "admin.permissions.permission.playbook_private_make_public.name": "تبدیل اجرانامه‌ها", "admin.permissions.permission.playbook_private_manage_members.name": "مدیریت اعضا اجرانامه", "admin.permissions.permission.playbook_private_manage_properties.name": "مدیریت پیکربندی‌های اجرانامه", "admin.permissions.permission.playbook_public_create.name": "ایجاد اجرانامه عمومی", "admin.permissions.permission.playbook_public_make_private.name": "تبددیل اجرانامه‌ها", "admin.permissions.permission.playbook_public_manage_members.name": "مدیریت اعضا اجرانامه", "admin.permissions.permission.playbook_public_manage_properties.name": "مدیریت پیکربندی‌های اجرانامه", "admin.permissions.permission.read_channel.description": "کانال را بخوانید", "admin.permissions.permission.read_channel.name": "کانال را بخوانید", "admin.permissions.permission.read_user_access_token.description": "خواندن نشانه دسترسی کاربر", "admin.permissions.permission.read_user_access_token.name": "خواندن نشانه دسترسی کاربر", "admin.permissions.permission.remove_user_from_team.description": "حذف کاربر از تیم", "admin.permissions.permission.remove_user_from_team.name": "حذف کاربر از تیم", "admin.permissions.permission.restore_custom_group.name": "بازگردانی", "admin.permissions.permission.revoke_user_access_token.description": "رمز دسترسی کاربر را لغو کنید", "admin.permissions.permission.revoke_user_access_token.name": "رمز دسترسی کاربر را لغو کنید", "admin.permissions.permission.run_create.description": "اجرای اجرانامه‌ها.", "admin.permissions.permission.run_create.name": "ایجاد اجراها", "admin.permissions.permission.upload_file.description": "آپلود فایل", "admin.permissions.permission.upload_file.name": "آپلود فایل", "admin.permissions.permission.use_channel_mentions.description": "اعضای کانال را با @all، @channel و @here اطلاع دهید", "admin.permissions.permission.use_channel_mentions.name": "ذکر کانال", "admin.permissions.permission.use_group_mentions.description": "اعضای گروه را با ذکر گروه مطلع کنید", "admin.permissions.permission.use_group_mentions.name": "اشاره های گروهی", "admin.permissions.permission.view_team.description": "مشاهده تیم", "admin.permissions.permission.view_team.name": "مشاهده تیم", "admin.permissions.permissionSchemes": "طرح های مجوز", "admin.permissions.permissionSchemes.cancel": "انصراف", "admin.permissions.permissionsSchemeSummary.delete": "<PERSON><PERSON><PERSON>", "admin.permissions.permissionsSchemeSummary.deleteConfirmButton": "بله حذف کنید", "admin.permissions.permissionsSchemeSummary.deleteConfirmQuestion": "مجوزهای تیم هایی که از این طرح استفاده می کنند به حالت پیش فرض در طرح سیستم بازنشانی می شوند. آیا مطمئنید که می خواهید طرح {schemeName} را حذف کنید؟", "admin.permissions.permissionsSchemeSummary.deleteSchemeTitle": "طرح {scheme} حذف شود؟", "admin.permissions.permissionsSchemeSummary.deleting": "در حال حذف...", "admin.permissions.permissionsSchemeSummary.edit": "ویرایش کنید", "admin.permissions.permissionsSchemeSummary.moreTeams": "+{number} بیشتر", "admin.permissions.permissionsTree.description": "شرح", "admin.permissions.permissionsTree.permission": "اجازه", "admin.permissions.roles.all_users.name": "همه اعضا", "admin.permissions.roles.channel_admin.name": "ادمین کانال", "admin.permissions.roles.channel_user.name": "کار<PERSON>ر کانال", "admin.permissions.roles.edit": "ویرایش", "admin.permissions.roles.system_admin.name": "ادمین سیستم", "admin.permissions.roles.system_admin.type": "نقش سامانه", "admin.permissions.roles.system_custom_group_admin.type": "نقش سامانه", "admin.permissions.roles.system_manager.name": "مدیر سامانه", "admin.permissions.roles.system_manager.type": "نقش سامانه", "admin.permissions.roles.system_read_only_admin.name": "بیننده", "admin.permissions.roles.system_read_only_admin.type": "نقش سامانه", "admin.permissions.roles.system_user.name": "کاربر سیستم", "admin.permissions.roles.system_user_manager.name": "مدی<PERSON><PERSON>ت کاربر", "admin.permissions.roles.system_user_manager.type": "نقش سامانه", "admin.permissions.roles.team_admin.name": "ادمین تیم", "admin.permissions.roles.team_user.name": "کاربر تیم", "admin.permissions.sysconsole_section_about.name": "درباره", "admin.permissions.sysconsole_section_about_edition_and_license.name": "نسخه و مجوز", "admin.permissions.sysconsole_section_authentication.name": "احر<PERSON>ز هویت", "admin.permissions.sysconsole_section_authentication_email.name": "پست الکترونیک", "admin.permissions.sysconsole_section_authentication_guest_access.name": "دسترسی مهمان", "admin.permissions.sysconsole_section_authentication_ldap.name": "AD/LDAP", "admin.permissions.sysconsole_section_authentication_mfa.name": "وزارت امور خارجه", "admin.permissions.sysconsole_section_authentication_openid.name": "OpenID Connect", "admin.permissions.sysconsole_section_authentication_password.name": "کلمه عبور", "admin.permissions.sysconsole_section_authentication_saml.name": "SAML 2.0", "admin.permissions.sysconsole_section_authentication_signup.name": "ثبت نام", "admin.permissions.sysconsole_section_billing.name": "صورتحساب", "admin.permissions.sysconsole_section_compliance.name": "قبول", "admin.permissions.sysconsole_section_compliance_compliance_export.name": "مطابقت صادرات", "admin.permissions.sysconsole_section_compliance_compliance_monitoring.name": "نظارت بر انطباق", "admin.permissions.sysconsole_section_compliance_custom_terms_of_service.name": "شرایط سفارشی خدمات", "admin.permissions.sysconsole_section_compliance_data_retention_policy.name": "سیاست حفظ داده ها", "admin.permissions.sysconsole_section_environment.name": "<PERSON><PERSON><PERSON><PERSON>", "admin.permissions.sysconsole_section_environment_database.name": "پایگاه داده", "admin.permissions.sysconsole_section_environment_developer.name": "توسعه دهنده", "admin.permissions.sysconsole_section_environment_elasticsearch.name": "Elasticsearch", "admin.permissions.sysconsole_section_environment_file_storage.name": "ذخیره سازی فایل", "admin.permissions.sysconsole_section_environment_high_availability.name": "در دسترس بودن بالا", "admin.permissions.sysconsole_section_environment_image_proxy.name": "پروکسی تصویر", "admin.permissions.sysconsole_section_environment_logging.name": "ورود به سیستم", "admin.permissions.sysconsole_section_environment_performance_monitoring.name": "نظارت بر عملکرد", "admin.permissions.sysconsole_section_environment_push_notification_server.name": "Push Notification Server", "admin.permissions.sysconsole_section_environment_rate_limiting.name": "محدود کردن نرخ", "admin.permissions.sysconsole_section_environment_session_lengths.name": "طو<PERSON> جلسه", "admin.permissions.sysconsole_section_environment_smtp.name": "SMTP", "admin.permissions.sysconsole_section_environment_web_server.name": "وب سرور", "admin.permissions.sysconsole_section_experimental.name": "آزمایشی", "admin.permissions.sysconsole_section_experimental_bleve.name": "Bleve", "admin.permissions.sysconsole_section_experimental_feature_flags.name": "پرچم های ویژگی", "admin.permissions.sysconsole_section_experimental_features.name": "امکانات", "admin.permissions.sysconsole_section_integrations.name": "یکپارچه‌سازها", "admin.permissions.sysconsole_section_integrations_bot_accounts.name": "حساب های ربات", "admin.permissions.sysconsole_section_integrations_cors.name": "CORS", "admin.permissions.sysconsole_section_integrations_gif.name": "GIF (بتا)", "admin.permissions.sysconsole_section_integrations_integration_management.name": "مدیریت یکپارچه سازی", "admin.permissions.sysconsole_section_plugins.name": "افزونه‌ها", "admin.permissions.sysconsole_section_reporting.name": "گزارش", "admin.permissions.sysconsole_section_reporting_server_logs.name": "گزارش های سرور", "admin.permissions.sysconsole_section_reporting_site_statistics.name": "آمار سایت", "admin.permissions.sysconsole_section_reporting_team_statistics.name": "آ<PERSON>ار تیم", "admin.permissions.sysconsole_section_site.name": "پیکربندی سایت", "admin.permissions.sysconsole_section_site_announcement_banner.name": "بنر اطلاعیه", "admin.permissions.sysconsole_section_site_customization.name": "سفارشی سازی", "admin.permissions.sysconsole_section_site_emoji.name": "ایموجی", "admin.permissions.sysconsole_section_site_file_sharing_and_downloads.name": "اشتراک گذاری و دانلود فایل", "admin.permissions.sysconsole_section_site_localization.name": "بومی سازی", "admin.permissions.sysconsole_section_site_notices.name": "اطلاعیه ها", "admin.permissions.sysconsole_section_site_notifications.name": "اطلاعیه", "admin.permissions.sysconsole_section_site_posts.name": "نوشته ها", "admin.permissions.sysconsole_section_site_public_links.name": "پیوندهای عمومی", "admin.permissions.sysconsole_section_site_users_and_teams.name": "کاربران و تیم ها", "admin.permissions.sysconsole_section_user_management.name": "مدی<PERSON><PERSON>ت کاربر", "admin.permissions.sysconsole_section_user_management_channels.name": "کانال‌ها", "admin.permissions.sysconsole_section_user_management_groups.name": "گروه‌ها", "admin.permissions.sysconsole_section_user_management_permissions.name": "مجوزها", "admin.permissions.sysconsole_section_user_management_system_roles.name": "نقش‌های سامانه", "admin.permissions.sysconsole_section_user_management_teams.name": "تیم‌ها", "admin.permissions.sysconsole_section_user_management_users.name": "کاربران", "admin.permissions.systemRoles": "نقش‌های سامانه", "admin.permissions.systemRolesBannerTitle": "نقش‌های سرپرست", "admin.permissions.systemScheme": "طرح سیستم", "admin.permissions.systemScheme.GuestsDescription": "مجوزهایی که به کاربران مهمان داده شده است.", "admin.permissions.systemScheme.GuestsTitle": "میه<PERSON>انان", "admin.permissions.systemScheme.allMembersDescription": "مجوزها به همه اعضا، از جمله مدیران و کاربران تازه ایجاد شده اعطا شده است.", "admin.permissions.systemScheme.allMembersTitle": "همه اعضا", "admin.permissions.systemScheme.channelAdminsDescription": "مجوزهایی که به سازندگان کانال و هر کاربر ارتقا یافته به مدیر کانال اعطا شده است.", "admin.permissions.systemScheme.channelAdminsTitle": "مد<PERSON><PERSON><PERSON> کانال", "admin.permissions.systemScheme.introBanner": "مجوزهای پیش فرض را برای مدیران تیم، مدیران کانال و سایر اعضا پیکربندی کنید. این طرح توسط همه تیم‌ها به ارث می‌رسد مگر اینکه در تیم‌های خاصی از <link>Team Override Scheme</link>  استفاده شود.", "admin.permissions.systemScheme.playbookAdmin": "سرپرست اجرانامه", "admin.permissions.systemScheme.resetDefaultsButton": "به حالت پیش فرض بازنشانی کنید", "admin.permissions.systemScheme.resetDefaultsButtonModalBody": "با این کار همه انتخاب‌های این صفحه به تنظیمات پیش‌فرضشان بازنشانی می‌شود. آیا مطمئن هستید که می خواهید بازنشانی کنید؟", "admin.permissions.systemScheme.resetDefaultsButtonModalTitle": "تنظیم مجدد به حالت پیش فرض؟", "admin.permissions.systemScheme.resetDefaultsConfirmationButton": "بله، تنظیم مجدد", "admin.permissions.systemScheme.systemAdminsDescription": "مجوزهای کامل به مدیران سیستم داده شده است.", "admin.permissions.systemScheme.systemAdminsTitle": "مدیران سیستم", "admin.permissions.systemScheme.teamAdminsDescription": "مجوزهایی که به سازندگان تیم و هر کاربر ارتقا یافته به Team Administrator داده شده است.", "admin.permissions.systemScheme.teamAdminsTitle": "مدی<PERSON>ان تیم", "admin.permissions.systemSchemeBannerButton": "ویرایش طرح", "admin.permissions.systemSchemeBannerText": "مجوزهای پیش‌فرض به ارث رسیده توسط همه تیم‌ها را تنظیم کنید، مگر اینکه <link>Team Override Scheme</link>  اعمال شود.", "admin.permissions.systemSchemeBannerTitle": "طرح سیستم", "admin.permissions.system_role_permissions.hide_subsections": "مخ<PERSON>ی کردن {subsectionsCount} بخش", "admin.permissions.system_role_permissions.mixed_access.title": "دسترسی ترکیبی", "admin.permissions.system_role_permissions.no_access.title": "بدون دسترسی", "admin.permissions.system_role_permissions.read.title": "<PERSON><PERSON><PERSON> خواندنی", "admin.permissions.system_role_permissions.show_subsections": "نمایش {subsectionsCount} بخش", "admin.permissions.system_role_permissions.title": "اختیارات", "admin.permissions.system_role_permissions.write.title": "مجوز ویرایش", "admin.permissions.system_role_users.add_people": "افزودن افراد", "admin.permissions.system_role_users.title": "افراد تعیین‌شده", "admin.permissions.teamOverrideSchemesBannerText": "زمانی استفاده کنید که تیم‌های خاصی به استثناهای مجوز <link>System Scheme</link>  نیاز داشته باشند.", "admin.permissions.teamOverrideSchemesInProgress": "کار انتقال در حال انجام است: تا زمانی که سرور کار انتقال مجوزها را کامل نکند، طرح‌های لغو تیم در دسترس نیستند. در {documentationLink} بیشتر بیاموزید.", "admin.permissions.teamOverrideSchemesNewButton": "طرح نادیده گرفتن تیم جدید", "admin.permissions.teamOverrideSchemesNoJobsEnabled": "کار انتقال در انتظار: طرح‌های لغو تیم تا زمانی که سرور شغل نتواند انتقال مجوزها را اجرا کند، در دسترس نیستند. هنگامی که سرور شغل فعال شود، کار به طور خودکار شروع می شود. در {documentationLink} بیشتر بیاموزید.", "admin.permissions.teamOverrideSchemesNoSchemes": "هیچ طرح نادیده گرفتن تیم ایجاد نشد.", "admin.permissions.teamOverrideSchemesTitle": "طرح های لغو تیم", "admin.permissions.teamScheme": "طرح تیمی", "admin.permissions.teamScheme.addTeams": "تیم ها را اضافه کنید", "admin.permissions.teamScheme.introBanner": "<linkOverrideTeam>Team Override Schemes</linkOverrideTeam> مجوزها را برای سرپرستان تیم، مدیران کانال و سایر اعضای تیم های خاص تنظیم می کند. زمانی که تیم‌های خاصی به استثناهای مجوز <linkSystemScheme>System Scheme</linkSystemScheme>  نیاز دارند، از یک طرح لغو تیم استفاده کنید.", "admin.permissions.teamScheme.noTeams": "هیچ تیمی انتخاب نشد لطفا تیم ها را به این لیست اضافه کنید.", "admin.permissions.teamScheme.removeTeam": "<PERSON><PERSON><PERSON>", "admin.permissions.teamScheme.schemeDescriptionLabel": "توضیحات طرح:", "admin.permissions.teamScheme.schemeDescriptionPlaceholder": "شرح طرح", "admin.permissions.teamScheme.schemeDetailsDescription": "نام و توضیحات را برای این طرح تنظیم کنید.", "admin.permissions.teamScheme.schemeDetailsTitle": "جزئیات طرح", "admin.permissions.teamScheme.schemeNameLabel": "نام طرح:", "admin.permissions.teamScheme.schemeNamePlaceholder": "نام طرح", "admin.permissions.teamScheme.selectTeamsDescription": "تیم هایی را انتخاب کنید که در آن استثنائات مجوز مورد نیاز است.", "admin.permissions.teamScheme.selectTeamsTitle": "تیم هایی را برای لغو مجوزها انتخاب کنید", "admin.plugin.backToPlugins": "به پلاگین ها برگردید", "admin.plugin.choose": "انتخاب فایل", "admin.plugin.cluster_instance": "نمونه خوشه", "admin.plugin.customSetting.pluginDisabledWarning": "برای مشاهده این تنظیمات، افزونه را فعال کرده و روی ذخیره کلیک کنید.", "admin.plugin.disable": "غیر فعال کردن", "admin.plugin.disabling": "غیرفعال کردن...", "admin.plugin.enable": "فعال کردن", "admin.plugin.enable_plugin": "فعال کردن افزونه: ", "admin.plugin.enable_plugin.help": "وقتی درست باشد، این افزونه فعال می شود.", "admin.plugin.enabling": "را قادر می سازد...", "admin.plugin.error.activate": "آپلود افزونه ممکن نیست. ممکن است با افزونه دیگری در سرور شما تداخل داشته باشد.", "admin.plugin.error.extract": "هنگام استخراج افزونه با خطا مواجه شد. محتوای فایل افزونه خود را بررسی کنید و دوباره امتحان کنید.", "admin.plugin.installedDesc": "پلاگین های نصب شده روی سرور Mattermost شما.", "admin.plugin.installedTitle": "پلاگین های نصب شده: ", "admin.plugin.management.title": "مدیریت پلاگین", "admin.plugin.multiple_versions_warning": "چندین نسخه از این افزونه در کلاستر شما نصب شده است. این افزونه را دوباره نصب کنید تا مطمئن شوید که به طور مداوم کار می کند.", "admin.plugin.no_plugins": "هیچ پلاگین نصب شده ای وجود ندارد.", "admin.plugin.remove": "<PERSON><PERSON><PERSON>", "admin.plugin.remove_modal.desc": "آیا مطمئن هستید که می خواهید افزونه را حذف کنید؟", "admin.plugin.remove_modal.overwrite": "<PERSON><PERSON><PERSON>", "admin.plugin.remove_modal.title": "افزونه حذف شود؟", "admin.plugin.removing": "در حال حذف...", "admin.plugin.settingsButton": "تنظیمات", "admin.plugin.state": "دولت", "admin.plugin.state.failed_to_start": "شروع نشد", "admin.plugin.state.failed_to_start.description": "این افزونه شروع به کار نکرد. گزارش های سیستم خود را برای خطا بررسی کنید.", "admin.plugin.state.failed_to_stay_running": "در حال سقوط", "admin.plugin.state.failed_to_stay_running.description": "این افزونه چندین بار خراب شد و دیگر اجرا نمی شود. گزارش های سیستم خود را برای خطا بررسی کنید.", "admin.plugin.state.not_running": "در حال اجرا نیست", "admin.plugin.state.not_running.description": "این افزونه فعال نیست.", "admin.plugin.state.running": "در حال دویدن", "admin.plugin.state.running.description": "این افزونه در حال اجراست.", "admin.plugin.state.starting": "راه افتادن", "admin.plugin.state.starting.description": "این افزونه در حال راه اندازی است.", "admin.plugin.state.stopping": "توقف", "admin.plugin.state.stopping.description": "این افزونه در حال توقف است.", "admin.plugin.state.unknown": "ناشناس", "admin.plugin.upload": "بارگذاری", "admin.plugin.upload.overwrite_modal.desc": "افزونه ای با این شناسه از قبل وجود دارد. آیا می خواهید آن را بازنویسی کنید؟", "admin.plugin.upload.overwrite_modal.overwrite": "زیاد نوشتن", "admin.plugin.upload.overwrite_modal.title": "افزونه موجود بازنویسی شود؟", "admin.plugin.uploadAndPluginDisabledDesc": "برای فعال کردن افزونه ها، **Enable Plugins** را روی true تنظیم کنید. برای کسب اطلاعات بیشتر به <link>اسناد</link>  مراجعه کنید.", "admin.plugin.uploadDesc": "یک افزونه برای سرور Mattermost خود آپلود کنید. برای کسب اطلاعات بیشتر به <link>اسناد</link>  مراجعه کنید.", "admin.plugin.uploadDisabledDesc": "آپلود افزونه را در config.json فعال کنید. برای کسب اطلاعات بیشتر به <link>اسناد</link>  مراجعه کنید.", "admin.plugin.uploadTitle": "افزونه آپلود: ", "admin.plugin.uploading": "در حال آپلود...", "admin.plugin.version_title": "نسخه", "admin.plugins.pluginManagement": "مدیریت پلاگین", "admin.plugins.settings.automaticPrepackagedPlugins": "فعال کردن پلاگین های از پیش بسته بندی شده خودکار:", "admin.plugins.settings.automaticPrepackagedPluginsDesc": "وقتی درست باشد، سرور افزونه‌های فعال قبلی را روی سرور شناسایی می‌کند و به طور خودکار آنها را نصب می‌کند.", "admin.plugins.settings.enable": "فعال کردن پلاگین ها: ", "admin.plugins.settings.enableDesc": "وقتی درست است، افزونه‌ها را در سرور Mattermost شما فعال می‌کند. از پلاگین ها برای ادغام با سیستم های شخص ثالث، گسترش عملکرد یا سفارشی کردن رابط کاربری سرور Mattermost خود استفاده کنید. برای کسب اطلاعات بیشتر به <link>مستندات</link> مراجعه کنید.", "admin.plugins.settings.enableMarketplace": "فعال کردن بازار:", "admin.plugins.settings.enableMarketplaceDesc": "وقتی درست است، مدیران سیستم را قادر می‌سازد تا افزونه‌ها را از <link>marketplace</link>  نصب کنند.", "admin.plugins.settings.enableRemoteMarketplace": "فعال کردن Remote Marketplace:", "admin.plugins.settings.enableRemoteMarketplaceDesc": "وقتی درست باشد، بازار آخرین افزونه‌ها را از URL پیکربندی‌شده Marketplace واکشی می‌کند.", "admin.plugins.settings.marketplaceUrl": "URL بازار:", "admin.plugins.settings.marketplaceUrlDesc": "URL سرور بازار.", "admin.plugins.settings.marketplaceUrlDesc.empty": " URL بازار یک قسمت الزامی است.", "admin.plugins.settings.requirePluginSignature": "نیاز به امضای افزونه:", "admin.plugins.settings.requirePluginSignatureDesc": "در صورت صحت، آپلود افزونه‌ها غیرفعال است و ممکن است فقط از طریق Marketplace نصب شوند. پلاگین ها همیشه در هنگام راه اندازی و راه اندازی سرور Mattermost تأیید می شوند. برای کسب اطلاعات بیشتر به <link>مستندات</link> مراجعه کنید.", "admin.posts.postPriority.title": "اولویت پیام", "admin.privacy.showEmailDescription": "وقتی نادرست است، آدرس ایمیل اعضا را از همه به جز مدیران سیستم پنهان می کند.", "admin.privacy.showEmailTitle": "نمایش آدرس ایمیل: ", "admin.privacy.showFullNameDescription": "وقتی نادرست است، نام کامل اعضا را از همه به جز مدیران سیستم پنهان می کند. نام کاربری به جای نام کامل نشان داده شده است.", "admin.privacy.showFullNameTitle": "نمایش نام کامل: ", "admin.purge.button": "تمام کش ها را پاک کنید", "admin.purge.purgeDescription": "این کار تمام کش های حافظه را برای مواردی مانند جلسات، حساب ها، کانال ها و غیره پاک می کند. استقرارهایی که با استفاده از قابلیت دسترسی بالا (High Availability) استفاده می کنند، سعی می کنند همه سرورهای این کلاستر را پاک کنند. پاکسازی حافظه پنهان ممکن است بر عملکرد تأثیر منفی بگذارد.", "admin.purge.purgeFail": "پاکسازی ناموفق بود: {error}", "admin.rate.enableLimiterDescription": "وقتی درست است، APIها با نرخ های مشخص شده در زیر کاهش می یابند.\n \nمحدود کردن نرخ از اضافه بار سرور به دلیل درخواست های زیاد جلوگیری می کند. این برای جلوگیری از تأثیرگذاری برنامه های شخص ثالث یا حملات مخرب بر سرور شما مفید است.", "admin.rate.enableLimiterTitle": "فعال کردن محدودیت نرخ: ", "admin.rate.httpHeaderDescription": "پس از پر کردن، محدودیت نرخ را با فیلد هدر HTTP مشخص شده تغییر دهید (به عنوان مثال هنگام پیکربندی NGINX روی \"X-Real-IP\"، هنگام پیکربندی AmazonELB تنظیم شده روی \"X-Forwarded-For\").", "admin.rate.httpHeaderExample": "به عنوان مثال: \"X-Real-IP\"، \"X-Forwarded-For\"", "admin.rate.httpHeaderTitle": "تغییر محدودیت نرخ بر اساس هدر HTTP:", "admin.rate.maxBurst": "حداکثر اندازه انفجار:", "admin.rate.maxBurstDescription": "حداکثر تعداد درخواست های مجاز فراتر از محدودیت درخواست در ثانیه.", "admin.rate.maxBurstExample": "به عنوان مثال: \"100\"", "admin.rate.memoryDescription": "حداکثر تعداد جلسات کاربران متصل به سیستم طبق تنظیمات «تغییر محدودیت نرخ بر اساس آدرس راه دور» و «تغییر محدودیت نرخ بر اساس هدر HTTP» در زیر تعیین شده است.", "admin.rate.memoryExample": "به عنوان مثال: \"10000\"", "admin.rate.memoryTitle": "اندازه فروشگاه حافظه:", "admin.rate.noteDescription": "تغییر خصوصیات در این بخش نیاز به راه اندازی مجدد سرور قبل از اعمال دارد.", "admin.rate.queriesDescription": "API را با این تعداد درخواست در ثانیه کاهش می دهد.", "admin.rate.queriesExample": "به عنوان مثال: \"10\"", "admin.rate.queriesTitle": "حداکثر درخواست در ثانیه:", "admin.rate.remoteDescription": "وقتی درست است، دسترسی API را با آدرس IP محدود کنید.", "admin.rate.remoteTitle": "تغییر محدودیت نرخ بر اساس آدرس راه دور: ", "admin.rate.title": "محدود کردن نرخ", "admin.rate.varyByUser": "تغییر محدودیت نرخ بر اساس کاربر: ", "admin.rate.varyByUserDescription": "وقتی درست است، نرخ دسترسی API را با کد تأیید اعتبار کاربر محدود کنید.", "admin.recycle.button": "بازیافت اتصالات پایگاه داده", "admin.recycle.recycleDescription": "پیاده‌سازی‌هایی که از چندین پایگاه داده استفاده می‌کنند، می‌توانند بدون راه‌اندازی مجدد سرور Mattermost، با به‌روزرسانی «config.json» به پیکربندی جدید دلخواه و استفاده از ویژگی {reloadConfiguration} برای بارگیری تنظیمات جدید در حالی که سرور در حال اجرا است، از یک پایگاه‌داده اصلی به پایگاه‌داده اصلی دیگر جابجا شوند. سپس مدیر باید از ویژگی {featureName} برای بازیافت اتصالات پایگاه داده بر اساس تنظیمات جدید استفاده کند.", "admin.recycle.recycleDescription.featureName": "بازیافت اتصالات پایگاه داده", "admin.recycle.recycleDescription.reloadConfiguration": "Environment > Web Server > Reload Configuration from Disk", "admin.recycle.reloadFail": "بازیافت ناموفق بود: {error}", "admin.regenerate": "بازسازی کنید", "admin.reload.button": "بارگیری مجدد پیکربندی از دیسک", "admin.reload.reloadDescription": "پیاده‌سازی‌هایی که از چندین پایگاه داده استفاده می‌کنند، می‌توانند بدون راه‌اندازی مجدد سرور Mattermost، با به‌روزرسانی «config.json» به پیکربندی جدید دلخواه و استفاده از ویژگی {featureName} برای بارگیری تنظیمات جدید در حالی که سرور در حال اجرا است، از یک پایگاه‌داده اصلی به دیگری سوئیچ کنند. سپس مدیر باید از ویژگی {recycleDatabaseConnections} برای بازیافت اتصالات پایگاه داده بر اساس تنظیمات جدید استفاده کند.", "admin.reload.reloadDescription.featureName": "بارگیری مجدد پیکربندی از دیسک", "admin.reload.reloadDescription.recycleDatabaseConnections": "Environment > Database > Recycle Connections Database", "admin.reload.reloadFail": "بارگیری مجدد ناموفق بود: {error}", "admin.reporting.workspace_optimization.access.title": "دسترسی محیط‌کار", "admin.reporting.workspace_optimization.chip_problems": "مشکلات: {count}", "admin.reporting.workspace_optimization.chip_suggestions": "پیشنهادات: {count}", "admin.reporting.workspace_optimization.chip_warnings": "هشدارها: {count}", "admin.reporting.workspace_optimization.configuration.title": "پیکربندی", "admin.reporting.workspace_optimization.cta.learnMore": "مطالعه بیشتر", "admin.reporting.workspace_optimization.cta.startTrial": "شروع دوره آزمایشی", "admin.reporting.workspace_optimization.cta.upgradeLicense": "تماس با بخش فروش", "admin.reporting.workspace_optimization.data_privacy.title": "حریم‌خصوصی داده", "admin.reporting.workspace_optimization.overall_workspace_score": "امتیاز سرجمع", "admin.reporting.workspace_optimization.performance.search.title": "کارایی جست‌وجو", "admin.reporting.workspace_optimization.performance.title": "کارایی", "admin.reporting.workspace_optimization.title": "بهینه‌سازی محیط‌کار", "admin.reporting.workspace_optimization.updates.server_version.update_type.major": "<PERSON><PERSON><PERSON><PERSON>", "admin.reporting.workspace_optimization.updates.server_version.update_type.minor": "جز<PERSON>ی", "admin.reporting.workspace_optimization.updates.server_version.update_type.patch": "وصله", "admin.reporting.workspace_optimization.updates.title": "به‌روزرسانی‌های سرور", "admin.requestButton.loading": " بارگذاری...", "admin.requestButton.requestFailure": "شکست تست: {خطا}", "admin.requestButton.requestSuccess": "تست موفقیت آمیز", "admin.reset_email.cancel": "انصراف", "admin.reset_email.newEmail": "ایمی<PERSON> جدید", "admin.reset_email.reset": "بازنشانی کنید", "admin.reset_email.titleReset": "ایمیل را به روز کنید", "admin.reset_password.cancel": "انصراف", "admin.reset_password.curentPassword": "ر<PERSON>ز عبور فعلی", "admin.reset_password.missing_current": "لطفا رمز عبور فعلی خود را وارد کنید.", "admin.reset_password.newPassword": "<PERSON><PERSON><PERSON> عبور جدید", "admin.reset_password.reset": "بازنشانی کنید", "admin.reset_password.titleReset": "بازیابی رمز عبور", "admin.reset_password.titleSwitch": "تغییر حساب به ایمیل / رمز عبور", "admin.revoke_token_button.delete": "<PERSON><PERSON><PERSON>", "admin.s3.connectionS3Test": "تست اتصال", "admin.s3.s3Fail": "اتصال ناموفق: {error}", "admin.s3.s3Success": "اتصال با موفقیت انجام شد", "admin.s3.testing": "آزمایش کردن...", "admin.saml.adminAttrDesc": "(اختیاری) ویژگی در ادعای SAML برای تعیین مدیران سیستم. کاربران انتخاب شده توسط پرس و جو به سرور Mattermost شما به عنوان سرپرست سیستم دسترسی خواهند داشت. به طور پیش فرض، مدیران سیستم به کنسول سیستم Mattermost دسترسی کامل دارند.\n \nاعضای موجود که با این ویژگی شناسایی می شوند، پس از ورود به سیستم، از عضو به مدیر سیستم ارتقا می یابند. ورود بعدی بر اساس طول جلسه تنظیم شده در **کنسول سیستم > طول جلسه** است. به شدت توصیه می شود که به صورت دستی کاربران را به اعضا در **کنسول سیستم > مدیریت کاربر** تنزل دهید تا مطمئن شوید دسترسی فوراً محدود می شود.\n \nتوجه: در صورت حذف/تغییر این فیلتر، مدیران سیستمی که از طریق این فیلتر ارتقا یافته اند به اعضا تنزل پیدا می کنند و دسترسی به کنسول سیستم را حفظ نخواهند کرد. وقتی از این فیلتر استفاده نمی‌شود، مدیران سیستم را می‌توان به‌صورت دستی در **کنسول سیستم > مدیریت کاربر** ارتقا/تنزل داد.", "admin.saml.adminAttrEx": "به عنوان مثال: \"usertype=Admin\" یا \"isAdmin=true\"", "admin.saml.adminAttrTitle": "ویژگی مدیریت:", "admin.saml.assertionConsumerServiceURLEx": "به عنوان مثال: \"https://'<your-mattermost-url>'/login/sso/saml\"", "admin.saml.assertionConsumerServiceURLPopulatedDesc": "این فیلد به عنوان URL خدمات مصرف کننده ادعا نیز شناخته می شود.", "admin.saml.assertionConsumerServiceURLTitle": "آدرس ورود ارائه دهنده خدمات:", "admin.saml.canonicalAlgorithmDescription.c14": "الگوریتم Canonicalization را مشخص کنید (Canonical XML 1.1). لطفاً اطلاعات بیشتر ارائه شده در http://www.w3.org/2006/12/xml-c14n11 را ببینید", "admin.saml.canonicalAlgorithmDescription.exc": "الگوریتم Canonicalization (Exclusive XML Canonicalization 1.0) را مشخص کنید. لطفاً اطلاعات بیشتر ارائه شده در http://www.w3.org/2001/10/xml-exc-c14n# را ببینید", "admin.saml.canonicalAlgorithmDisplay.n10": "انحصاری XML Canonicalization 1.0 (نظرات را حذف کنید)", "admin.saml.canonicalAlgorithmDisplay.n11": "Canonical XML 1.1 (نظرات را حذف کنید)", "admin.saml.canonicalAlgorithmTitle": "الگوریتم متعارف سازی", "admin.saml.emailAttrDesc": "ویژگی SAML Assertion که برای پر کردن آدرس‌های ایمیل کاربران در Mattermost استفاده می‌شود.", "admin.saml.emailAttrEx": "به عنوان مثال: \"ایمیل\" یا \"ایمیل اصلی\"", "admin.saml.emailAttrTitle": "ویژگی ایمیل:", "admin.saml.enableAdminAttrTitle": "فعال کردن ویژگی Admin:", "admin.saml.enableDescription": "وقتی درست است، Mattermost اجازه ورود با استفاده از SAML 2.0 را می دهد. لطفاً برای کسب اطلاعات بیشتر در مورد پیکربندی SAML برای Mattermost، به <link>Documentation</link>  مراجعه کنید.", "admin.saml.enableSyncWithLdapDescription": "وقتی درست است، Mattermost به صورت دوره‌ای ویژگی‌های کاربر SAML، از جمله غیرفعال کردن و حذف کاربر، از AD/LDAP را همگام‌سازی می‌کند. تنظیمات همگام سازی را در **Authentication > AD/LDAP** فعال و پیکربندی کنید. هنگامی که نادرست است، ویژگی های کاربر در حین ورود کاربر از SAML به روز می شوند. برای کسب اطلاعات بیشتر به <link>مستندات</link> مراجعه کنید.", "admin.saml.enableSyncWithLdapIncludeAuthDescription": "اگر درست باشد، Mattermost در صورت پیکربندی، ویژگی SAML ID را با ویژگی AD/LDAP ID لغو می‌کند یا اگر ویژگی SAML ID وجود نداشته باشد، ویژگی SAML Email را با ویژگی AD/LDAP Email لغو می‌کند. این به شما این امکان را می دهد که به طور خودکار کاربران را از الزام آور ایمیل به پیوند شناسه منتقل کنید تا از ایجاد کاربران جدید در هنگام تغییر آدرس ایمیل برای کاربر جلوگیری کنید. حرکت از درست به نادرست، لغو را از وقوع حذف می کند.\n \n**توجه:** شناسه های SAML باید با شناسه های LDAP مطابقت داشته باشند تا از غیرفعال شدن حساب های کاربری جلوگیری شود. لطفاً <link>مستندات</link>  را برای اطلاعات بیشتر مرور کنید.", "admin.saml.enableSyncWithLdapIncludeAuthTitle": "با اطلاعات AD/LDAP، داده‌های باند SAML را لغو کنید:", "admin.saml.enableSyncWithLdapTitle": "فعال کردن همگام سازی حساب های SAML با AD/LDAP:", "admin.saml.enableTitle": "فعال کردن ورود با SAML 2.0:", "admin.saml.encryptDescription": "در صورت نادرست بودن، Mattermost اظهارات SAML رمزگذاری شده با گواهی عمومی ارائه دهنده خدمات شما را رمزگشایی نمی کند. غیرفعال کردن رمزگذاری برای محیط های تولید توصیه نمی شود.", "admin.saml.encryptTitle": "فعال کردن رمزگذاری:", "admin.saml.firstnameAttrDesc": "(اختیاری) ویژگی در ادعای SAML که برای پر کردن نام کوچک کاربران در Mattermost استفاده می‌شود.", "admin.saml.firstnameAttrEx": "به عنوان مثال: \"نام\"", "admin.saml.firstnameAttrTitle": "ویژگی نام:", "admin.saml.getSamlMetadataFromIDPFail": "URL فراداده SAML متصل نشد و داده ها را با موفقیت جمع آوری نکرد", "admin.saml.getSamlMetadataFromIDPFetching": "در حال واکشی...", "admin.saml.getSamlMetadataFromIDPSuccess": "فراداده SAML با موفقیت بازیابی شد. دو فیلد و یک گواهی به روز شده است", "admin.saml.getSamlMetadataFromIDPUrl": "فراداده SAML را از IdP دریافت کنید", "admin.saml.guestAttrDesc": "(اختیاری) نیاز به فعال کردن دسترسی مهمان قبل از اعمال دارد. مشخصه ای در SAML Assertion که برای اعمال نقش مهمان به کاربران در Mattermost استفاده می شود. مهمانان پس از ورود به سیستم از دسترسی به تیم ها یا کانال ها جلوگیری می کنند تا زمانی که یک تیم و حداقل یک کانال به آنها اختصاص داده شود.\n \nتوجه: اگر این ویژگی از کاربر مهمان شما در SAML حذف/تغییر شود و کاربر همچنان فعال باشد، به عضوی ارتقا نمی‌یابد و نقش مهمان خود را حفظ می‌کند. میهمانان را می توان در **کنسول سیستم > مدیریت کاربر** ارتقا داد.\n \n \nاعضای موجود که با این ویژگی به عنوان مهمان شناسایی می شوند، زمانی که از آنها خواسته می شود که وارد سیستم شوند، از یک عضو به یک مهمان تنزل می یابند. ورود بعدی بر اساس طول جلسه تنظیم شده در **کنسول سیستم > طول جلسه** است. به شدت توصیه می شود که به صورت دستی کاربران را به مهمانان در **کنسول سیستم > مدیریت کاربر ** کاهش دهید تا اطمینان حاصل شود که دسترسی بلافاصله محدود می شود.", "admin.saml.guestAttrEx": "به عنوان مثال: \"usertype=Guest\" یا \"isGuest=true\"", "admin.saml.guestAttrTitle": "ویژگی مهمان:", "admin.saml.idAttrDesc": "(اختیاری) ویژگی در ادعای SAML که برای اتصال کاربران از SAML به کاربران در Mattermost استفاده می شود.", "admin.saml.idAttrEx": "به عنوان مثال: \"شناسه\"", "admin.saml.idAttrTitle": "ویژگی شناسه:", "admin.saml.idpCertificateFileDesc": "گواهی احراز هویت عمومی صادر شده توسط ارائه دهنده هویت شما.", "admin.saml.idpCertificateFileRemoveDesc": "گواهی احراز هویت عمومی صادر شده توسط ارائه دهنده هویت خود را حذف کنید.", "admin.saml.idpCertificateFileTitle": "گواهی عمومی ارائه دهنده هویت:", "admin.saml.idpDescriptorUrlDesc": "نشانی وب صادرکننده برای Identity Provider که برای درخواست‌های SAML استفاده می‌کنید.", "admin.saml.idpDescriptorUrlEx": "به عنوان مثال: \"https://idp.example.org/SAML2/issuer\"", "admin.saml.idpDescriptorUrlTitle": "نشانی اینترنتی صادرکننده ارائه دهنده هویت:", "admin.saml.idpMetadataUrlDesc": "نشانی اینترنتی که در آن Mattermost درخواستی برای دریافت فراداده ارسال می کند", "admin.saml.idpMetadataUrlEx": "به عنوان مثال: \"https://idp.example.org/SAML2/saml/metadata\"", "admin.saml.idpMetadataUrlTitle": "URL فراداده ارائه دهنده هویت:", "admin.saml.idpUrlDesc": "URL که در آن Mattermost یک درخواست SAML برای شروع توالی ورود به سیستم ارسال می کند.", "admin.saml.idpUrlEx": "به عنوان مثال: \"https://idp.example.org/SAML2/SSO/Login\"", "admin.saml.idpUrlTitle": "نشانی اینترنتی SAML SSO:", "admin.saml.lastnameAttrDesc": "(اختیاری) ویژگی در ادعای SAML که برای پر کردن نام خانوادگی کاربران در Mattermost استفاده خواهد شد.", "admin.saml.lastnameAttrEx": "به عنوان مثال: \"نام خانوادگی\"", "admin.saml.lastnameAttrTitle": "ویژگی نام خانوادگی:", "admin.saml.localeAttrDesc": "(اختیاری) ویژگی در ادعای SAML که برای پر کردن زبان کاربران در Mattermost استفاده می‌شود.", "admin.saml.localeAttrEx": "به عنوان مثال: «محلی» یا «زبان اصلی»", "admin.saml.localeAttrTitle": "ویژگی زبان ترجیحی:", "admin.saml.loginButtonTextDesc": "(اختیاری) متنی که در دکمه ورود به سیستم در صفحه ورود ظاهر می شود. پیش‌فرض «SAML» است.", "admin.saml.loginButtonTextEx": "به عنوان مثال: \"OKTA\"", "admin.saml.loginButtonTextTitle": "متن دکمه ورود:", "admin.saml.nicknameAttrDesc": "(اختیاری) ویژگی در ادعای SAML که برای پر کردن نام مستعار کاربران در Mattermost استفاده خواهد شد.", "admin.saml.nicknameAttrEx": "به عنوان مثال: \"نام مستعار\"", "admin.saml.nicknameAttrTitle": "ویژگی نام مستعار:", "admin.saml.positionAttrDesc": "(اختیاری) ویژگی در ادعای SAML که برای پر کردن موقعیت کاربران در Mattermost استفاده خواهد شد.", "admin.saml.positionAttrEx": "به عنوان مثال: \"نقش\"", "admin.saml.positionAttrTitle": "ویژگی موقعیت:", "admin.saml.privateKeyFileFileDesc": "کلید خصوصی که برای رمزگشایی اظهارات SAML از ارائه دهنده هویت استفاده می شود.", "admin.saml.privateKeyFileFileRemoveDesc": "کلید خصوصی مورد استفاده برای رمزگشایی ادعاهای SAML را از ارائه دهنده هویت حذف کنید.", "admin.saml.privateKeyFileTitle": "کلید خصوصی ارائه دهنده خدمات:", "admin.saml.publicCertificateFileDesc": "گواهی استفاده شده برای ایجاد امضا در درخواست SAML به ارائه‌دهنده هویت برای ارائه‌دهنده خدمات ورود SAML را آغاز کرده است، در حالی که Mattermost ارائه‌دهنده خدمات است.", "admin.saml.publicCertificateFileRemoveDesc": "هنگامی که Mattermost ارائه دهنده خدمات است، گواهی استفاده شده برای ایجاد امضا در درخواست SAML به ارائه دهنده هویت برای ورود SAML توسط ارائه دهنده خدمات را حذف کنید.", "admin.saml.publicCertificateFileTitle": "گواهی عمومی ارائه دهنده خدمات:", "admin.saml.remove.idp_certificate": "گواهی ارائه دهنده هویت را حذف کنید", "admin.saml.remove.privKey": "ک<PERSON>ید خصوصی ارائه دهنده خدمات را حذف کنید", "admin.saml.remove.sp_certificate": "حذف گواهی ارائه دهنده خدمات", "admin.saml.removing.certificate": "در حال حذف گواهی...", "admin.saml.removing.privKey": "در حال حذف کلید خصوصی...", "admin.saml.serviceProviderIdentifierDesc": "شناسه منحصربه‌فرد برای ارائه‌دهنده خدمات، معمولاً همان URL ورود به ارائه‌دهنده خدمات است. در ADFS، این باید با Relying Party Identifier مطابقت داشته باشد.", "admin.saml.serviceProviderIdentifierEx": "به عنوان مثال: \"https://'<your-mattermost-url>'/login/sso/saml\"", "admin.saml.serviceProviderIdentifierTitle": "شناسه ارائه دهنده خدمات:", "admin.saml.signRequestDescription": "وقتی درست باشد، Mattermost درخواست SAML را با استفاده از کلید خصوصی شما امضا می‌کند. وقتی نادرست باشد، Mattermost درخواست SAML را امضا نمی کند.", "admin.saml.signRequestTitle": "درخواست امضا:", "admin.saml.signatureAlgorithmDescription.sha1": "الگوریتم Signature مورد استفاده برای امضای درخواست (RSAwithSHA1) را مشخص کنید. لطفاً اطلاعات بیشتر ارائه شده در http://www.w3.org/2000/09/xmldsig#rsa-sha1 را ببینید", "admin.saml.signatureAlgorithmDescription.sha256": "الگوریتم Signature مورد استفاده برای امضای درخواست را مشخص کنید (RSAwithSHA256). لطفاً به اطلاعات بیشتر ارائه شده در http://www.w3.org/2001/04/xmldsig-more#rsa-sha256 [بخش 6.4.2 RSA (PKCS#1 v1.5)] مراجعه کنید.", "admin.saml.signatureAlgorithmDescription.sha512": "الگوریتم Signature مورد استفاده برای امضای درخواست را مشخص کنید (RSAwithSHA512). لطفاً اطلاعات بیشتر ارائه شده در http://www.w3.org/2001/04/xmldsig-more#rsa-sha512 را ببینید", "admin.saml.signatureAlgorithmDisplay.sha1": "RSAwithSHA1", "admin.saml.signatureAlgorithmDisplay.sha256": "RSAwithSHA256", "admin.saml.signatureAlgorithmDisplay.sha512": "RSAwithSHA512", "admin.saml.signatureAlgorithmTitle": "الگوریتم امضا", "admin.saml.uploading.certificate": "در حال بارگذاری گواهی...", "admin.saml.uploading.privateKey": "در حال بارگذاری کلید خصوصی...", "admin.saml.usernameAttrDesc": "ویژگی در ادعای SAML که برای پر کردن فیلد نام کاربری در Mattermost استفاده خواهد شد.", "admin.saml.usernameAttrEx": "به عنوان مثال: \"نام کاربری\"", "admin.saml.usernameAttrTitle": "ویژگی نام کاربری:", "admin.saml.verifyDescription": "در صورت نادرست بودن، Mattermost تأیید نمی کند که امضای ارسال شده از یک پاسخ SAML با URL ورود ارائه دهنده خدمات مطابقت دارد. غیرفعال کردن تأیید برای محیط های تولید توصیه نمی شود.", "admin.saml.verifyTitle": "تأیید امضا:", "admin.saml_feature_discovery.copy": "هنگامی که Mattermost را به ارائه‌دهنده ورود به سیستم سازمان خود متصل می‌کنید، کاربران می‌توانند بدون نیاز به وارد کردن مجدد اطلاعات کاربری خود به Mattermost دسترسی داشته باشند.", "admin.saml_feature_discovery.title": "SAML 2.0 را با Mattermost Professional ادغام کنید", "admin.saving": "در حال ذخیره پیکربندی...", "admin.security.password": "کلمه عبور", "admin.server_logs.CopyLog": "رونوشت ردپا", "admin.server_logs.DataCopied": "داده رونوشت شد", "admin.server_logs.LogEvent": "رویداد ردپا", "admin.service.CorsDebugLabel": "اشکال زدایی CORS:", "admin.service.attemptDescription": "تعداد تلاش‌های ورود به سیستم قبل از قفل شدن کاربر و نیاز به تنظیم مجدد رمز عبور از طریق ایمیل مجاز است.", "admin.service.attemptExample": "به عنوان مثال: \"10\"", "admin.service.attemptTitle": "حداکثر تلاش برای ورود به سیستم:", "admin.service.cmdsDesc": "وقتی درست باشد، دستورات اسلش سفارشی مجاز خواهند بود. برای کسب اطلاعات بیشتر به <link>مستندات</link> مراجعه کنید.", "admin.service.cmdsTitle": "دستورات اسلش سفارشی را فعال کنید: ", "admin.service.complianceExportDesc": "وقتی درست باشد، Mattermost همه پیام‌هایی را که در 24 ساعت گذشته پست شده‌اند صادر می‌کند. وظیفه صادرات یک بار در روز برنامه ریزی شده است. برای کسب اطلاعات بیشتر به <link>مستندات</link>  مراجعه کنید.", "admin.service.complianceExportTitle": "فعال کردن Export Compliance:", "admin.service.corsAllowCredentialsDescription": "وقتی درست باشد، درخواست‌هایی که تأیید اعتبار می‌کنند شامل سرصفحه Access-Control-Allow-Credentials می‌شوند.", "admin.service.corsAllowCredentialsLabel": "CORS اجازه می دهد اعتبارنامه ها:", "admin.service.corsDebugDescription": "هنگامی که درست است، پیام‌هایی را در گزارش‌ها چاپ می‌کند تا در ایجاد یکپارچگی که از CORS استفاده می‌کند کمک کند. این پیام ها شامل جفت ارزش کلید ساختاریافته \"source\":\"cors\" خواهد بود.", "admin.service.corsDescription": "درخواست مبدا HTTP Cross از یک دامنه خاص را فعال کنید. اگر می خواهید CORS را از هر دامنه ای مجاز کنید یا آن را خالی بگذارید تا غیرفعال شود، از \"*\" استفاده کنید. در تولید نباید روی \"*\" تنظیم شود.", "admin.service.corsEx": "http://example.com", "admin.service.corsExposedHeadersDescription": "لیست سفید سرصفحه هایی که برای درخواست کننده قابل دسترسی خواهد بود.", "admin.service.corsExposedHeadersTitle": "هدرهای در معرض CORS:", "admin.service.corsHeadersEx": "<PERSON>-<PERSON>-<PERSON>er", "admin.service.corsTitle": "فعال کردن درخواست های متقاطع از:", "admin.service.developerDesc": "وقتی درست است، خطاهای جاوا اسکریپت در نوار بنفش در بالای رابط کاربری نشان داده می شود. برای استفاده در تولید توصیه نمی شود. ", "admin.service.developerTitle": "فعال کردن حالت توسعه دهنده: ", "admin.service.disableBotOwnerDeactivatedTitle": "غیرفعال کردن حساب های ربات در صورت غیرفعال شدن مالک:", "admin.service.disableBotWhenOwnerIsDeactivated": "هنگامی که یک کاربر غیرفعال می شود، تمام حساب های ربات مدیریت شده توسط کاربر غیرفعال می شود. برای فعال کردن مجدد حساب‌های ربات، به [Integrations > Bot Accounts] ({siteURL}/_redirect/integrations/bots) بروید.", "admin.service.enableBotAccountCreation": "وقتی درست باشد، مدیران سیستم می‌توانند حساب‌های ربات را برای ادغام در [Integrations > Bot Accounts] ({siteURL}/_redirect/integrations/bots) ایجاد کنند. حساب‌های ربات شبیه حساب‌های کاربر هستند، با این تفاوت که نمی‌توان از آنها برای ورود به سیستم استفاده کرد. برای اطلاعات بیشتر به [مستندات](https://mattermost.com/pl/default-bot-accounts) مراجعه کنید.", "admin.service.enableBotTitle": "فعال کردن ایجاد حساب ربات: ", "admin.service.enforceMfaDesc": "وقتی درست است، برای ورود به سیستم، <link>احراز هویت چند عاملی</link> لازم است. کاربران جدید باید MFA را هنگام ثبت نام پیکربندی کنند. کاربرانی که بدون پیکربندی MFA وارد شده اند به صفحه راه اندازی MFA هدایت می شوند تا پیکربندی کامل شود.\n \nاگر سیستم شما کاربرانی با روش‌های ورود به سیستم غیر از AD/LDAP و ایمیل دارد، MFA باید با ارائه‌دهنده احراز هویت خارج از Mattermost اجرا شود.", "admin.service.enforceMfaTitle": "اجرای احراز هویت چند عاملی:", "admin.service.extendSessionLengthActivity.helpText": "وقتی درست است، وقتی کاربر در مشتری Mattermost خود فعال باشد، جلسات به طور خودکار تمدید می شوند. جلسات کاربران تنها در صورتی منقضی می‌شوند که در کل مدت زمان جلسات تعریف شده در فیلدهای زیر در مشتری Mattermost خود فعال نباشند. وقتی نادرست است، جلسات با فعالیت در Mattermost گسترش نمی‌یابد. جلسات کاربر بلافاصله در پایان طول جلسه یا زمان‌های بی‌حرکتی تعریف شده در زیر منقضی می‌شوند. ", "admin.service.extendSessionLengthActivity.label": "طول جلسه را با فعالیت بیشتر کنید: ", "admin.service.forward80To443": "فوروار<PERSON> پورت 80 به 443:", "admin.service.forward80To443Description": "تمام ترافیک ناامن را از درگاه 80 به پورت امن 443 هدایت می کند. هنگام استفاده از سرور پراکسی توصیه نمی شود.", "admin.service.forward80To443Description.disabled": "تمام ترافیک ناامن را از درگاه 80 به پورت امن 443 هدایت می کند. هنگام استفاده از سرور پراکسی توصیه نمی شود.\n \nاین تنظیم را نمی توان تا زمانی که سرور شما در حال [شنیدن] (#ServiceSettings.ListenAddress) در پورت 443 فعال کرد.", "admin.service.googleDescription": "این کلید را برای فعال کردن نمایش عناوین برای پیش‌نمایش ویدیوهای جاسازی شده YouTube تنظیم کنید. بدون کلید، پیش‌نمایش‌های YouTube همچنان بر اساس پیوندهایی که در پیام‌ها یا نظرات ظاهر می‌شوند ایجاد می‌شوند اما عنوان ویدیو را نشان نمی‌دهند. برای دستورالعمل‌هایی درباره نحوه دریافت کلید و افزودن YouTube Data API نسخه 3 به‌عنوان سرویس به کلید خود، <link>Google Developers Tutorial</link>  را مشاهده کنید.", "admin.service.googleExample": "به عنوان مثال: \"7rAh6iwQCkV4cA1Gsg3fgGOXJAQ43QV\"", "admin.service.googleTitle": "کلید Google API:", "admin.service.iconDescription": "وقتی درست باشد، وب‌قلاب‌ها، دستورات اسلش و سایر ادغام‌ها، مانند <link>Zapier</link>، مجاز خواهند بود عکس نمایه‌ای را که با آن پست می‌کنند تغییر دهند. توجه: همراه با اجازه دادن به ادغام‌ها برای لغو نام‌های کاربری، کاربران ممکن است بتوانند با جعل هویت سایر کاربران حملات فیشینگ را انجام دهند.", "admin.service.iconTitle": "ادغام ها را برای لغو نمادهای عکس نمایه فعال کنید:", "admin.service.insecureTlsDesc": "وقتی درست باشد، هر درخواست HTTPS خروجی گواهی‌های تأیید نشده و خودامضا را می‌پذیرد. به عنوان مثال، وبکهک‌های خروجی به سروری با گواهینامه TLS خودامضا، با استفاده از هر دامنه، مجاز خواهند بود. توجه داشته باشید که این باعث می شود این اتصالات مستعد حملات انسان در وسط باشند.", "admin.service.insecureTlsTitle": "اتصالات خروجی ناامن را فعال کنید: ", "admin.service.internalConnectionsDesc": "لیست سفید آدرس های شبکه محلی که می تواند توسط سرور Mattermost از طرف یک مشتری درخواست شود. هنگام پیکربندی این تنظیم باید مراقب باشید تا از دسترسی ناخواسته به شبکه محلی خود جلوگیری کنید. برای کسب اطلاعات بیشتر به <link>documentation</link> مراجعه کنید.", "admin.service.internalConnectionsEx": "webhooks.internal.example.com 127.0.0.1 *********/28", "admin.service.internalConnectionsTitle": "اتصالات داخلی غیرقابل اعتماد را مجاز کنید: ", "admin.service.letsEncryptCertificateCacheFile": "بیایید فایل حافظه پنهان گواهی را رمزگذاری کنیم:", "admin.service.letsEncryptCertificateCacheFileDescription": "گواهینامه های بازیابی شده و سایر داده های مربوط به سرویس Let's Encrypt در این فایل ذخیره می شود.", "admin.service.listenAddress": "آدرس گوش دادن:", "admin.service.listenDescription": "آدرس و درگاهی که به آن متصل شده و گوش دهید. تعیین \":8065\" به تمام رابط های شبکه متصل می شود. مشخص کردن \"127.0.0.1:8065\" فقط به رابط شبکه با آن آدرس IP متصل می شود. اگر پورتی از سطح پایین‌تری را انتخاب کنید (به نام «درگاه‌های سیستم» یا «پورت‌های معروف»، در محدوده 0-1023)، باید مجوز اتصال به آن پورت را داشته باشید. در لینوکس می توانید از: \"sudo setcap cap_net_bind_service=+ep ./bin/mattermost\" استفاده کنید تا به Mattermost اجازه دهید به پورت های معروف متصل شود.", "admin.service.listenExample": "به عنوان مثال: \":8065\"", "admin.service.managedResourcePaths": "مسیرهای منابع مدیریت‌شده:", "admin.service.mfaDesc": "وقتی درست باشد، کاربرانی که دارای AD/LDAP یا ورود به ایمیل هستند می‌توانند با استفاده از Google Authenticator احراز هویت چند عاملی را به حساب خود اضافه کنند.", "admin.service.mfaTitle": "احراز هویت چند عاملی را فعال کنید:", "admin.service.minimumHashtagLengthDescription": "حداقل تعداد کاراکتر در هشتگ این باید بزرگتر یا مساوی 2 باشد. پایگاه های داده MySQL باید برای پشتیبانی از رشته های جستجوی کوتاهتر از سه کاراکتر پیکربندی شوند، <link>به مستندات مراجعه کنید</link>.", "admin.service.minimumHashtagLengthExample": "به عنوان مثال: \"3\"", "admin.service.minimumHashtagLengthTitle": "حداقل طول هشتگ:", "admin.service.outWebhooksDesc": "وقتی درست باشد، وب‌قلاب‌های خروجی مجاز خواهند بود. برای کسب اطلاعات بیشتر به [documentation](!https://mattermost.com/pl/setup-outgoing-webhooks) مراجعه کنید.", "admin.service.outWebhooksTitle": "فعال کردن وب هوک های خروجی: ", "admin.service.overrideDescription": "وقتی درست باشد، وب‌قلاب‌ها، دستورات اسلش و سایر ادغام‌ها، مانند [Zapier](!https://developers.mattermost.com/integrate/admin-guide/admin-zapier-integration/)، مجاز خواهند بود نام کاربری را که به‌عنوان پست ارسال می‌کنند تغییر دهند. توجه: همراه با اجازه دادن به ادغام ها برای نادیده گرفتن نمادهای تصویر نمایه، کاربران ممکن است بتوانند با جعل هویت سایر کاربران حملات فیشینگ را انجام دهند.", "admin.service.overrideTitle": "ادغام ها را برای لغو نام های کاربری فعال کنید:", "admin.service.readTimeout": "خواندن تایم اوت:", "admin.service.readTimeoutDescription": "حداکثر زمان مجاز از زمان پذیرش اتصال تا زمانی که بدنه درخواست به طور کامل خوانده شود.", "admin.service.sessionCache": "حافظه پنهان جلسه (دقیقه):", "admin.service.sessionCacheDesc": "تعداد دقیقه‌های ذخیره یک جلسه در حافظه.", "admin.service.sessionHoursEx": "مثال: \"720\"", "admin.service.sessionIdleTimeout": "زمان توقف جلسه (دق<PERSON>قه):", "admin.service.sessionIdleTimeoutDesc": "تعداد دقیقه از آخرین باری که کاربر در سیستم فعال بوده تا پایان جلسه کاربر. پس از انقضا، کاربر برای ادامه باید وارد سیستم شود. حداقل 5 دقیقه، و عدد 0 نامحدود است.\n\nبرای برنامه دسکتاپ و مرورگرها اعمال می شود. برای برنامه‌های تلفن همراه، از یک ارائه‌دهنده EMM برای قفل کردن برنامه در صورت عدم استفاده کمک بگیرید. در حالت دسترسی بالا، متعادل‌سازی بار هش IP را برای اندازه‌گیری بازه زمانی مطمئن فعال کنید.", "admin.service.sessionIdleTimeoutEx": "به عنوان مثال: \"60\"", "admin.service.sessionMinutesEx": "به عنوان مثال: \"10\"", "admin.service.siteURL": "آدرس سایت:", "admin.service.siteURLDescription": "نشانی اینترنتی که کاربران برای دسترسی به Mattermost از آن استفاده خواهند کرد. پورت های استاندارد مانند 80 و 443 را می توان حذف کرد، اما پورت های غیر استاندارد مورد نیاز است. به عنوان مثال: http://example.com:8065. این تنظیم مورد نیاز است.\n \nMattermost ممکن است در یک مسیر فرعی میزبانی شود. به عنوان مثال: http://example.com:8065/company/mattermost. قبل از اینکه سرور به درستی کار کند نیاز به راه اندازی مجدد است.", "admin.service.siteURLExample": "به عنوان مثال: \"http://example.com:8065\"", "admin.service.testSiteURL": "URL زنده را تست کنید", "admin.service.testSiteURLFail": "تست ناموفق: {error}", "admin.service.testSiteURLSuccess": "تست موفقیت آمیز این یک URL معتبر است.", "admin.service.testSiteURLTesting": "آزمایش کردن...", "admin.service.testingDescription": "وقتی درست است، دستور اسلش /test برای بارگیری حساب‌های آزمایشی، داده‌ها و قالب‌بندی متن فعال می‌شود. برای تغییر این مورد نیاز به راه اندازی مجدد سرور قبل از اعمال است.", "admin.service.testingTitle": "فعال کردن دستورات تست: ", "admin.service.tlsCertFile": "فایل گواهی TLS:", "admin.service.tlsCertFileDescription": "فایل گواهی برای استفاده", "admin.service.tlsKeyFile": "فایل کلید TLS:", "admin.service.tlsKeyFileDescription": "فایل کلید خصوصی برای استفاده", "admin.service.useLetsEncrypt": "از Let's Encrypt استفاده کنید:", "admin.service.useLetsEncryptDescription": "بازیابی خودکار گواهی ها را از Let's Encrypt فعال کنید. زمانی که مشتری تلاش می کند از دامنه جدیدی متصل شود، گواهی بازیابی می شود. این با چندین دامنه کار می کند.", "admin.service.useLetsEncryptDescription.disabled": "بازیابی خودکار گواهی ها را از Let's Encrypt فعال کنید. زمانی که مشتری تلاش می کند از دامنه جدیدی متصل شود، گواهی بازیابی می شود. این با چندین دامنه کار می کند.\n \nاین تنظیم را نمی توان فعال کرد مگر اینکه تنظیم [Forward port 80 to 443] (#SystemSettings.Forward80To443) روی true تنظیم شود.", "admin.service.userAccessTokensDescription": "وقتی درست باشد، کاربران می‌توانند <link>نشانه‌های دسترسی کاربر</link> را برای ادغام در <strong>Profile > Security</strong> ایجاد کنند. می توان از آنها برای احراز هویت در برابر API و دسترسی کامل به حساب استفاده کرد.\n\n برای مدیریت افرادی که می‌توانند نشانه‌های دسترسی شخصی ایجاد کنند یا کاربران را با شناسه رمز جستجو کنند، به کنسول سیستم > مدیریت کاربر > کاربران بروید.", "admin.service.userAccessTokensTitle": "فعال کردن رمزهای دسترسی شخصی: ", "admin.service.webhooksDescription": "هنگامی که درست باشد، وب هوک های ورودی مجاز خواهند بود. برای کمک به مبارزه با حملات فیشینگ، همه پست‌های وب هوک با یک برچسب BOT برچسب‌گذاری می‌شوند. برای کسب اطلاعات بیشتر به [documentation](!https://mattermost.com/pl/setup-incoming-webhooks) مراجعه کنید.", "admin.service.webhooksTitle": "فعال کردن وب هوک های ورودی: ", "admin.service.writeTimeout": "نوشتن تایم اوت:", "admin.service.writeTimeoutDescription": "در صورت استفاده از HTTP (ناامن)، این حداکثر زمان مجاز از پایان خواندن هدرهای درخواست تا زمان نوشتن پاسخ است. اگر از HTTPS استفاده می کنید، کل زمان از زمان پذیرش اتصال تا زمان نوشتن پاسخ است.", "admin.sessionLengths.title": "طو<PERSON> جلسه", "admin.set_by_env": "این تنظیمات از طریق یک متغیر محیطی تنظیم شده است. نمی توان آن را از طریق کنسول سیستم تغییر داد.", "admin.sidebar.about": "در باره", "admin.sidebar.announcement": "بنر اطلاعیه", "admin.sidebar.authentication": "احر<PERSON>ز هویت", "admin.sidebar.billing": "صورتحساب و حساب", "admin.sidebar.billing_history": "تاریخچه صورتحساب", "admin.sidebar.blevesearch": "Bleve", "admin.sidebar.channels": "کانال ها", "admin.sidebar.company_info": "اطلاعات شرکت", "admin.sidebar.compliance": "انطباق", "admin.sidebar.complianceExport": "مطابقت صادرات", "admin.sidebar.complianceMonitoring": "نظارت بر انطباق", "admin.sidebar.cors": "CORS", "admin.sidebar.customTermsOfService": "شرایط سفارشی خدمات", "admin.sidebar.customization": "سفارشی سازی", "admin.sidebar.dataRetentionPolicy": "سیاست حفظ داده ها", "admin.sidebar.dataRetentionSettingsPolicies": "سیاست های حفظ داده ها", "admin.sidebar.database": "پایگاه داده", "admin.sidebar.developer": "توسعه دهنده", "admin.sidebar.elasticsearch": "Elasticsearch", "admin.sidebar.email": "پست الکترونیک", "admin.sidebar.emoji": "ایموجی", "admin.sidebar.environment": "<PERSON><PERSON><PERSON><PERSON>", "admin.sidebar.experimental": "تجربی", "admin.sidebar.experimentalFeatures": "امکانات", "admin.sidebar.fileSharingDownloads": "اشتراک گذاری و دانلود فایل", "admin.sidebar.fileStorage": "ذخیره سازی فایل", "admin.sidebar.filter": "تنظیمات را پیدا کنید", "admin.sidebar.gif": "GIF (بتا)", "admin.sidebar.gitlab": "GitLab", "admin.sidebar.groups": "گروه ها", "admin.sidebar.guest_access": "دسترسی مهمان", "admin.sidebar.highAvailability": "در دسترس بودن بالا", "admin.sidebar.imageProxy": "پروکسی تصویر", "admin.sidebar.integrations": "ادغام ها", "admin.sidebar.ldap": "AD/LDAP", "admin.sidebar.license": "نسخه و مجوز", "admin.sidebar.localization": "بومی سازی", "admin.sidebar.logging": "ورود به سیستم", "admin.sidebar.logs": "گزارش های سرور", "admin.sidebar.metrics": "نظارت بر عملکرد", "admin.sidebar.mfa": "وزارت امور خارجه", "admin.sidebar.notices": "اطلاعیه ها", "admin.sidebar.notifications": "اطلاعیه", "admin.sidebar.oauth": "OAuth 2.0", "admin.sidebar.openid": "اتصال OpenID", "admin.sidebar.password": "کلمه عبور", "admin.sidebar.permissions": "مجوزها", "admin.sidebar.plugins": "پلاگین ها", "admin.sidebar.posts": "نوشته ها", "admin.sidebar.publicLinks": "پیوندهای عمومی", "admin.sidebar.pushNotificationServer": "Push Notification Server", "admin.sidebar.rateLimiting": "محدود کردن نرخ", "admin.sidebar.reporting": "گزارش نویسی", "admin.sidebar.saml": "SAML 2.0", "admin.sidebar.sessionLengths": "طو<PERSON> جلسه", "admin.sidebar.signup": "ثبت نام", "admin.sidebar.site": "پیکربندی سایت", "admin.sidebar.siteStatistics": "آمار سایت", "admin.sidebar.smtp": "SMTP", "admin.sidebar.subscription": "اشتراک، ابونمان", "admin.sidebar.systemRoles": "نقش‌های سامانه", "admin.sidebar.teamStatistics": "آ<PERSON>ار تیم", "admin.sidebar.teams": "تیم ها", "admin.sidebar.userManagement": "مدی<PERSON><PERSON>ت کاربر", "admin.sidebar.users": "کاربران", "admin.sidebar.usersAndTeams": "کاربران و تیم ها", "admin.sidebar.webServer": "وب سرور", "admin.sidebar.workspaceOptimization": "بهینه‌سازی محیط‌کار", "admin.sidebarHeader.systemConsole": "کنسول سیستم", "admin.site.announcementBanner": "بنر اطلاعیه", "admin.site.customization": "سفارشی سازی", "admin.site.emoji": "ایموجی", "admin.site.fileSharingDownloads": "اشتراک گذاری و دانلود فایل", "admin.site.localization": "بومی سازی", "admin.site.notices": "اطلاعیه ها", "admin.site.posts": "نوشته ها", "admin.site.public_links": "پیوندهای عمومی", "admin.site.usersAndTeams": "کاربران و تیم ها", "admin.sql.connMaxIdleTimeExample": "مثال: \"300000\"", "admin.sql.connMaxLifetimeDescription": "حداکثر طول عمر (بر حسب میلی ثانیه) برای اتصال به پایگاه داده.", "admin.sql.connMaxLifetimeExample": "به عنوان مثال: \"3600000\"", "admin.sql.connMaxLifetimeTitle": "حداکثر طول عمر اتصال:", "admin.sql.dataSource": "منبع اطلاعات:", "admin.sql.dataSourceDescription": "منبع پایگاه داده را در فایل config.json تنظیم کنید.", "admin.sql.disableDatabaseSearchDescription": "استفاده از پایگاه داده برای انجام جستجو را غیرفعال می کند. فقط زمانی باید استفاده شود که سایر <link>موتورهای جستجو</link>  پیکربندی شده باشند.", "admin.sql.disableDatabaseSearchTitle": "غیرفعال کردن جستجوی پایگاه داده: ", "admin.sql.driverName": "نام راننده:", "admin.sql.driverNameDescription": "درایور پایگاه داده را در فایل config.json تنظیم کنید.", "admin.sql.maxConnectionsDescription": "حداکثر تعداد اتصالات بیکار که در پایگاه داده باز هستند.", "admin.sql.maxConnectionsExample": "به عنوان مثال: \"10\"", "admin.sql.maxConnectionsTitle": "حداکثر اتصالات بیکار:", "admin.sql.maxOpenDescription": "حداکثر تعداد اتصالات باز که به پایگاه داده باز می شوند.", "admin.sql.maxOpenExample": "به عنوان مثال: \"10\"", "admin.sql.maxOpenTitle": "حداکثر اتصالات باز:", "admin.sql.noteDescription": "تغییر خصوصیات در این بخش نیاز به راه اندازی مجدد سرور قبل از اعمال دارد.", "admin.sql.queryTimeoutDescription": "تعداد ثانیه های انتظار برای پاسخ از پایگاه داده پس از باز کردن اتصال و ارسال پرس و جو. خطاهایی که در UI یا در گزارش‌ها در نتیجه مهلت پرس و جو مشاهده می‌کنید، بسته به نوع پرس و جو می‌تواند متفاوت باشد.", "admin.sql.queryTimeoutExample": "به عنوان مثال: \"30\"", "admin.sql.queryTimeoutTitle": "زمان پرس و جو:", "admin.sql.traceDescription": "(حالت توسعه) هنگامی که true باشد، دستورات SQL در حال اجرا در گزارش نوشته می شوند.", "admin.sql.traceTitle": "ثبت بیانیه SQL: ", "admin.subscription.cloudTrialCard.upgrade": "ارتقا", "admin.support.aboutDesc": "نشانی اینترنتی پیوند About در صفحات ورود و ثبت نام Mattermost. اگر این قسمت خالی باشد، پیوند About از دید کاربران پنهان می شود.", "admin.support.aboutTitle": "لینک درباره:", "admin.support.enableAskCommunityDesc": "وقتی درست باشد، پیوند «از انجمن بپرسید» در رابط کاربری Mattermost و منوی راهنما ظاهر می‌شود، که به کاربران اجازه می‌دهد به انجمن Mattermost بپیوندند تا سؤال بپرسند و به دیگران در عیب‌یابی مشکلات کمک کنند. در صورت نادرست بودن، لینک از دید کاربران پنهان می شود.", "admin.support.enableAskCommunityTitle": "فعال کردن Ask Community Link:", "admin.support.enableTermsOfServiceHelp": "وقتی درست است، کاربران جدید باید قبل از دسترسی به تیم‌های Mattermost در دسک‌تاپ، وب یا تلفن همراه، شرایط خدمات را بپذیرند. کاربران موجود باید آنها را پس از ورود به سیستم یا بازخوانی صفحه بپذیرند.\n \nبرای به روز رسانی پیوند شرایط خدمات نمایش داده شده در صفحات ایجاد حساب و ورود به سیستم، به [Site Configuration > Customization] (../site_config/customization) بروید.", "admin.support.enableTermsOfServiceTitle": "فعال کردن شرایط خدمات سفارشی:", "admin.support.helpDesc": "URL پیوند راهنما در صفحه ورود به سیستم Mattermost، صفحات ثبت نام و منوی راهنما. اگر این قسمت خالی باشد، پیوند راهنما از دید کاربران پنهان می شود.", "admin.support.helpTitle": "لینک راهنما:", "admin.support.privacyDesc": "URL برای پیوند حریم خصوصی در صفحات ورود و ثبت نام. اگر این قسمت خالی باشد، پیوند Privacy از دید کاربران پنهان می شود.", "admin.support.privacyTitle": "لینک سیاست حفظ حریم خصوصی:", "admin.support.problemDesc": "نشانی اینترنتی پیوند گزارش مشکل در منوی راهنما. اگر این قسمت خالی باشد، پیوند از منوی Help حذف می شود.", "admin.support.problemTitle": "لینک مشکل را گزارش کنید:", "admin.support.termsDesc": "به شرایطی پیوند دهید که تحت آن کاربران می توانند از خدمات آنلاین شما استفاده کنند. به‌طور پیش‌فرض، این شامل «خط‌مشی استفاده قابل قبول Mattermost» است که شرایط ارائه نرم‌افزار Mattermost به کاربران نهایی را توضیح می‌دهد. اگر پیوند پیش‌فرض را تغییر دهید تا شرایط خود را برای استفاده از سرویسی که ارائه می‌دهید اضافه کنید، شرایط جدید شما باید شامل پیوندی به شرایط پیش‌فرض باشد تا کاربران نهایی از خط‌مشی استفاده قابل قبول Mattermost برای نرم‌افزار Mattermost آگاه باشند.", "admin.support.termsOfServiceReAcceptanceHelp": "تعداد روزهای قبل از انقضای پذیرش شرایط خدمات، و شرایط باید مجدداً پذیرفته شوند.", "admin.support.termsOfServiceReAcceptanceTitle": "دوره پذیرش مجدد:", "admin.support.termsOfServiceTextHelp": "متنی که در شرایط خدمات سفارشی شما ظاهر می شود. از متن با فرمت Markdown پشتیبانی می کند.", "admin.support.termsOfServiceTextTitle": "متن شرایط خدمات سفارشی:", "admin.support.termsOfServiceTitle": "شرایط سفارشی خدمات", "admin.support.termsTitle": "لینک شرایط استفاده:", "admin.systemUserDetail.teamList.header.name": "نام", "admin.systemUserDetail.teamList.header.role": "نقش", "admin.systemUserDetail.teamList.header.type": "تای<PERSON> کنید", "admin.systemUserDetail.teamList.teamRole.admin": "ادمین تیم", "admin.systemUserDetail.teamList.teamRole.guest": "مه<PERSON>ان", "admin.systemUserDetail.teamList.teamRole.member": "عضو تیم", "admin.systemUserDetail.teamList.teamType.anyoneCanJoin": "هرکسی میتواند بپیوندد", "admin.systemUserDetail.teamList.teamType.groupSync": "همگام سازی گروهی", "admin.systemUserDetail.teamList.teamType.inviteOnly": "تنها دعوت", "admin.systemUserDetail.title": "پیکربندی کاربر", "admin.system_roles_feature_discovery.copy": "از نقش های سیستم استفاده کنید تا به کاربران تعیین شده دسترسی خواندن و/یا نوشتن به بخش های منتخب کنسول سیستم را بدهید.", "admin.system_roles_feature_discovery.title": "با Mattermost Enterprise دسترسی کنترل شده ای به کنسول سیستم ارائه دهید", "admin.system_users.revokeAllSessions": "لغو همه جلسات", "admin.system_users.revoke_all_sessions_button": "لغو همه جلسات", "admin.system_users.revoke_all_sessions_modal_message": "این عمل تمام جلسات سیستم را باطل می کند. همه کاربران از همه دستگاه ها خارج خواهند شد. آیا مطمئن هستید که می خواهید همه جلسات را لغو کنید؟", "admin.system_users.revoke_all_sessions_modal_title": "تمام جلسات سیستم را لغو کنید", "admin.system_users.title": "کاربران {siteName}", "admin.team.brandDesc": "نام تجاری سفارشی را فعال کنید تا تصویری به انتخاب شما، آپلود شده در زیر، و برخی از متن های کمکی، نوشته شده در زیر، در صفحه ورود به سیستم نشان داده شود.", "admin.team.brandDescriptionHelp": "شرح خدمات در صفحات ورود به سیستم و UI نشان داده شده است. هنگامی که مشخص نیست، \"تمام ارتباطات تیم در یک مکان، قابل جستجو و در دسترس در هر نقطه\" نمایش داده می شود.", "admin.team.brandDescriptionTitle": "توضیحات سایت: ", "admin.team.brandImageTitle": "تصویر برند سفارشی:", "admin.team.brandTextDescription": "متنی که در زیر تصویر برند سفارشی شما در صفحه ورود ظاهر می شود. از متن با فرمت Markdown پشتیبانی می کند. حداکثر 500 کاراکتر مجاز است.", "admin.team.brandTextTitle": "متن برند سفارشی:", "admin.team.brandTitle": "فعال کردن نام تجاری سفارشی: ", "admin.team.chooseImage": "Image را انتخاب کنید", "admin.team.customUserStatusesTitle": "فعال کردن وضعیت‌های سفارشی: ", "admin.team.emailInvitationsDescription": "زمانی که کاربران واقعی می توانند با استفاده از ایمیل دیگران را به سیستم دعوت کنند.", "admin.team.emailInvitationsTitle": "فعال کردن دعوت نامه های ایمیل: ", "admin.team.invalidateEmailInvitesDescription": "این امر دعوت نامه های ایمیل فعالی را که توسط کاربر پذیرفته نشده اند باطل می کند. به‌طور پیش‌فرض، دعوت‌نامه‌های ایمیلی پس از 48 ساعت منقضی می‌شوند.", "admin.team.invalidateEmailInvitesFail": "نمی‌توان دعوت‌های ایمیلی معلق را باطل کرد: {error}", "admin.team.invalidateEmailInvitesSuccess": "دعوت نامه های معلق ایمیل با موفقیت باطل شد", "admin.team.invalidateEmailInvitesTitle": "دعوت نامه های معلق را باطل کنید", "admin.team.maxChannelsDescription": "حداکثر تعداد کل کانال ها در هر تیم، از جمله کانال های فعال و بایگانی شده.", "admin.team.maxChannelsExample": "به عنوان مثال: \"100\"", "admin.team.maxChannelsTitle": "حداکثر کانال در هر تیم:", "admin.team.maxNotificationsPerChannelDescription": "حداکثر تعداد کل کاربران در یک کانال قبل از اینکه کاربران پیام‌هایی را تایپ کنند، @all، @here و @channel دیگر به دلیل عملکرد، اعلان ارسال نمی‌کنند.", "admin.team.maxNotificationsPerChannelExample": "به عنوان مثال: \"1000\"", "admin.team.maxNotificationsPerChannelTitle": "حداکثر اعلان ها در هر کانال:", "admin.team.maxUsersDescription": "حداکثر تعداد کل کاربران در هر تیم، شامل کاربران فعال و غیرفعال.", "admin.team.maxUsersExample": "به عنوان مثال: \"25\"", "admin.team.maxUsersTitle": "حداکثر کاربر در هر تیم:", "admin.team.noBrandImage": "هیچ تصویر برندی آپلود نشده است", "admin.team.openServerDescription": "وقتی درست باشد، هر کسی می‌تواند بدون نیاز به دعوت، برای یک حساب کاربری در این سرور ثبت‌نام کند.", "admin.team.openServerTitle": "فعال کردن Open Server: ", "admin.team.removeBrandImage": "تصویر برند را حذف کنید", "admin.team.restrictDescription": "حساب‌های کاربری جدید به دامنه ایمیل مشخص‌شده در بالا (مانند «mattermost.org») یا فهرست دامنه‌های جدا شده با کاما (مانند «corp.mattermost.com، مادهmost.org») محدود می‌شوند. تیم های جدید فقط می توانند توسط کاربران دامنه(های) بالا ایجاد شوند. این تنظیم بر ورود ایمیل کاربران تأثیر می گذارد.", "admin.team.restrictDirectMessage": "فعال کردن کاربران برای باز کردن کانال های پیام مستقیم با:", "admin.team.restrictDirectMessageDesc": "«هر کاربر در سرور Mattermost» به کاربران امکان می‌دهد یک کانال پیام مستقیم با هر کاربر روی سرور باز کنند، حتی اگر در هیچ تیمی با هم نباشند. «هر عضوی از تیم» این امکان را در منوی «بیشتر» پیام‌های مستقیم محدود می‌کند تا فقط کانال‌های پیام مستقیم را با کاربرانی که در همان تیم هستند باز کند.\n \nتوجه: این تنظیم فقط بر رابط کاربری تأثیر می گذارد، نه مجوزهای سرور.", "admin.team.restrictExample": "به عنوان مثال: \"corp.mattermost.com، mattermost.com\"", "admin.team.restrictGuestDescription": "حساب‌های کاربری جدید به دامنه ایمیل مشخص‌شده در بالا (مانند «mattermost.org») یا فهرست دامنه‌های جدا شده با کاما (مانند «corp.mattermost.com، مادهmost.org») محدود می‌شوند. تیم های جدید فقط می توانند توسط کاربران دامنه(های) بالا ایجاد شوند. این تنظیم بر ورود ایمیل کاربران تأثیر می گذارد. برای کاربران مهمان، لطفاً دامنه‌هایی را در زیر ثبت نام > دسترسی مهمان اضافه کنید.", "admin.team.restrictTitle": "سیستم و اعضای تیم جدید را به دامنه های ایمیل مشخص محدود کنید:", "admin.team.restrict_direct_message_any": "هر کاربر در سرور Mattermost", "admin.team.restrict_direct_message_team": "هر یک از اعضای تیم", "admin.team.showFullname": "نمایش نام و نام خانوادگی", "admin.team.showNickname": "در صورت وجود نام مستعار را نشان دهید، در غیر این صورت نام و نام خانوادگی را نشان دهید", "admin.team.showUsername": "نمایش نام کاربری (پیش‌<PERSON>ر<PERSON>)", "admin.team.siteNameDescription": "نام سرویس در صفحات ورود به سیستم و رابط کاربری نشان داده شده است. وقتی مشخص نیست، به طور پیش فرض روی \"Mattermost\" قرار می گیرد.", "admin.team.siteNameExample": "به عنوان مثال: \"مهم<PERSON><PERSON><PERSON>ن\"", "admin.team.siteNameTitle": "نام سایت:", "admin.team.teammateNameDisplay": "نمایش نام هم تیمی:", "admin.team.teammateNameDisplayDesc": "نحوه نمایش نام کاربران در پست ها و لیست پیام های مستقیم را تنظیم کنید.", "admin.team.uploadDesc": "با افزودن یک تصویر سفارشی به صفحه ورود خود، تجربه کاربری خود را سفارشی کنید. حداکثر اندازه تصویر توصیه شده کمتر از 2 مگابایت است.", "admin.team.userCreationDescription": "در صورت نادرست بودن، امکان ایجاد حساب کاربری غیرفعال می شود. دکمه ایجاد حساب هنگام فشار دادن خطا را نشان می دهد.", "admin.team.userCreationTitle": "فعال کردن ایجاد حساب کاربری: ", "admin.team_channel_settings.cancel": "انصراف", "admin.team_channel_settings.channel_users_will_be_removed": "{amount, number} {amount, plural, one {User} other {Users}} از این کانال حذف خواهد شد. آنها در گروه های مرتبط با این کانال نیستند.", "admin.team_channel_settings.convertAndRemoveConfirmModal.cancel": "نه، لغو", "admin.team_channel_settings.convertAndRemoveConfirmModal.toPrivateConfirm": "بله، کانال را به خصوصی تبدیل کنید و {amount, number} {amount, plural, one {user} other {users}} را حذف کنید", "admin.team_channel_settings.convertAndRemoveConfirmModal.toPrivateTitle": "کانال را به خصوصی تبدیل کنید و {amount, number} {amount, plural, one {user} other {users}} حذف شود؟", "admin.team_channel_settings.convertAndRemoveConfirmModal.toPublicConfirm": "بله، کانال را به عمومی تبدیل کنید و {amount, number} {amount, plural, one {user} other {users}} را حذف کنید", "admin.team_channel_settings.convertAndRemoveConfirmModal.toPublicTitle": "تبدیل کانال به عمومی و حذف {amount, number} {amount, plural, one {user} other {users}}", "admin.team_channel_settings.convertConfirmModal.cancel": "نه، لغو", "admin.team_channel_settings.convertConfirmModal.toPrivateConfirm": "بله تبدیل به کانال خصوصی", "admin.team_channel_settings.convertConfirmModal.toPrivateTitle": "{displayName} به یک کانال خصوصی تبدیل شود؟", "admin.team_channel_settings.convertConfirmModal.toPublicConfirm": "بله، تبدیل به کانال عمومی", "admin.team_channel_settings.convertConfirmModal.toPublicTitle": "{displayName} به یک کانال عمومی تبدیل شود؟", "admin.team_channel_settings.groupMembers.close": "نزدیک", "admin.team_channel_settings.group_list.membersHeader": "تعداد اعضا", "admin.team_channel_settings.group_list.nameHeader": "اسم گروه", "admin.team_channel_settings.group_list.no-groups": "هنوز هیچ گروهی مشخص نشده است", "admin.team_channel_settings.group_list.no-synced-groups": "حداقل یک گروه باید مشخص شود", "admin.team_channel_settings.group_list.rolesHeader": "نقش ها", "admin.team_channel_settings.group_row.channelAdmin": "ادمین کانال", "admin.team_channel_settings.group_row.member": "عضو", "admin.team_channel_settings.group_row.memberRole": "نقش عضو", "admin.team_channel_settings.group_row.members": "{memberCount, number} {memberCount, plural, one {member} other {members}}", "admin.team_channel_settings.group_row.remove": "<PERSON><PERSON><PERSON>", "admin.team_channel_settings.group_row.teamAdmin": "ادمین تیم", "admin.team_channel_settings.list.paginatorCount": "{startCount, number} - {endCount, number} از {total, number}", "admin.team_channel_settings.need_domains": "لطفا دامنه های ایمیل مجاز را مشخص کنید.", "admin.team_channel_settings.need_groups": "برای مدیریت این تیم توسط اعضای گروه باید حداقل یک گروه اضافه کنید.", "admin.team_channel_settings.need_groups_channel": "برای مدیریت این کانال توسط اعضای گروه باید حداقل یک گروه اضافه کنید.", "admin.team_channel_settings.removeConfirmModal.messageChannel": "{amount, number} {amount, plural, one {user} other {users}} حذف خواهد شد. آنها در گروه های مرتبط با این کانال نیستند. آیا مطمئنید که می‌خواهید {amount, plural, one {this user} {this users}} را حذف کنید؟", "admin.team_channel_settings.removeConfirmModal.messageTeam": "{amount, number} {amount, plural, one {user} other {users}} حذف خواهد شد. آنها در گروه های مرتبط با این تیم نیستند. آیا مطمئنید که می‌خواهید {amount, plural, one {this user} {this users}} را حذف کنید؟", "admin.team_channel_settings.removeConfirmModal.remove": "ذخیره و حذف {amount, plural, one {user} other {users}}", "admin.team_channel_settings.removeConfirmModal.title": "ذخیره و حذف {amount, number} {amount, plural, one {user} other {users}}؟", "admin.team_channel_settings.saving": "در حال ذخیره پیکربندی...", "admin.team_channel_settings.user_list.groupsHeader": "گروه ها", "admin.team_channel_settings.user_list.nameHeader": "نام", "admin.team_channel_settings.user_list.roleHeader": "نقش", "admin.team_channel_settings.usersToBeRemovedModal.channel_message": "اعضای لیست شده در زیر در هیچ یک از گروه هایی که در حال حاضر به این کانال پیوند دارند، نیستند. از آنجایی که این کانال تنظیم شده است که توسط همگام‌سازی گروهی مدیریت شود، همه آنها پس از ذخیره حذف خواهند شد.", "admin.team_channel_settings.usersToBeRemovedModal.close": "نزدیک", "admin.team_channel_settings.usersToBeRemovedModal.message": "اعضای لیست شده در زیر در هیچ یک از گروه هایی که در حال حاضر به این تیم مرتبط هستند نیستند. از آنجایی که این تیم قرار است با همگام‌سازی گروهی مدیریت شود، پس از ذخیره همه آنها حذف خواهند شد.", "admin.team_channel_settings.users_will_be_removed": "{amount, number} {amount, plural, one {user} other {users}} از این تیم حذف خواهد شد. آنها در گروه های مرتبط با این تیم نیستند.", "admin.team_channel_settings.view_removed_users": "مشاهده این کاربران", "admin.team_settings.description": "تنظیمات تیم را مدیریت کنید", "admin.team_settings.groupsPageTitle": "تیم‌های {siteName}", "admin.team_settings.team_detail.archive_confirm.button": "بایگانی", "admin.team_settings.team_detail.archive_confirm.message": "ذخیره تیم را بایگانی می کند و محتوای آن را برای همه کاربران غیر قابل دسترس می کند. آیا مطمئن هستید که می خواهید این تیم را ذخیره و بایگانی کنید؟", "admin.team_settings.team_detail.archive_confirm.title": "ذخیره و بایگانی تیم", "admin.team_settings.team_detail.group_configuration": "پیکربندی تیم", "admin.team_settings.team_detail.groupsDescription": "اعضای گروه به تیم اضافه خواهند شد.", "admin.team_settings.team_detail.groupsTitle": "گروه ها", "admin.team_settings.team_detail.manageDescription": "بین دعوت از اعضا به صورت دستی یا همگام سازی اعضا به صورت خودکار از گروه ها را انتخاب کنید.", "admin.team_settings.team_detail.manageTitle": "مدیریت تیم", "admin.team_settings.team_detail.membersDescription": "لیستی از کاربرانی که در حال حاضر در تیم هستند", "admin.team_settings.team_detail.membersTitle": "اعضا", "admin.team_settings.team_detail.profileDescription": "خلاصه تیم، شامل نام و توضیحات تیم.", "admin.team_settings.team_detail.profileNoDescription": "هیچ توضیحی برای تیم اضافه نشده است.", "admin.team_settings.team_detail.profileTitle": "مشخصات تیم", "admin.team_settings.team_detail.syncedGroupsDescription": "اعضای تیم را بر اساس عضویت در گروه اضافه و حذف کنید.", "admin.team_settings.team_detail.syncedGroupsTitle": "گروه های همگام سازی شده", "admin.team_settings.team_details.add_group": "اضافه کردن گروه", "admin.team_settings.team_details.add_members": "اضافه کردن عضو", "admin.team_settings.team_details.anyoneCanJoin": "هر کسی می تواند به این تیم بپیوندد", "admin.team_settings.team_details.anyoneCanJoinDescr": "این تیم را می توان کشف کرد که به هر کسی که حساب کاربری دارد اجازه می دهد به این تیم بپیوندد.", "admin.team_settings.team_details.archiveTeam": "تیم آرشیو", "admin.team_settings.team_details.csvDomains": "لیست دامنه ایمیل جدا شده با کاما", "admin.team_settings.team_details.groupDetailsToggle": "فعال کردن ذکر گروه (بتا)", "admin.team_settings.team_details.groupDetailsToggleDescr": "در صورت فعال بودن، می توان از این گروه در کانال ها و تیم های دیگر نام برد. این ممکن است باعث شود که لیست اعضای گروه برای همه کاربران قابل مشاهده باشد.", "admin.team_settings.team_details.specificDomains": "فقط دامنه های ایمیل خاص می توانند به این تیم بپیوندند", "admin.team_settings.team_details.specificDomainsDescr": "کاربران تنها در صورتی می توانند به تیم ملحق شوند که ایمیل آنها با یکی از دامنه های مشخص شده مطابقت داشته باشد", "admin.team_settings.team_details.syncGroupMembers": "همگام سازی اعضای گروه", "admin.team_settings.team_details.syncGroupMembersDescr": "وقتی فعال باشد، افزودن و حذف کاربران از گروه‌ها باعث اضافه یا حذف آنها از این تیم می‌شود. تنها راه دعوت اعضا به این تیم، اضافه کردن گروه هایی است که به آنها تعلق دارند. <link>بیشتر بیاموزید</link>", "admin.team_settings.team_details.unarchiveTeam": "تیم لغو بایگانی", "admin.team_settings.team_list.mappingHeader": "مدی<PERSON><PERSON>ت", "admin.team_settings.team_list.nameHeader": "نام", "admin.team_settings.team_list.no_teams_found": "هیچ تیمی پیدا نشد", "admin.team_settings.team_list.search_teams_errored": "مشکلی پیش آمد. دوباره امتحان کنید", "admin.team_settings.team_row.archived": "(بایگانی شده)", "admin.team_settings.team_row.configure": "ویرایش کنید", "admin.team_settings.team_row.managementMethod.anyoneCanJoin": "هرکسی میتواند بپیوندد", "admin.team_settings.team_row.managementMethod.groupSync": "همگام سازی گروهی", "admin.team_settings.team_row.managementMethod.inviteOnly": "تنها دعوت", "admin.team_settings.title": "تیم ها", "admin.true": "درست است، واقعی", "admin.userManagement.userDetail.addTeam": "تیم را اضافه کنید", "admin.userManagement.userDetail.authenticationMethod": "روش احراز هویت", "admin.userManagement.userDetail.email": "پست الکترونیک", "admin.userManagement.userDetail.mfa": "وزارت امور خارجه", "admin.userManagement.userDetail.teamsSubtitle": "تیم هایی که این کاربر به آنها تعلق دارد", "admin.userManagement.userDetail.teamsTitle": "عضویت در تیم", "admin.userManagement.userDetail.userId": "شناسه کاربری:", "admin.userManagement.userDetail.username": "نام کاربری", "admin.user_grid.channel_admin": "ادمین کانال", "admin.user_grid.guest": "مه<PERSON>ان", "admin.user_grid.name": "نام", "admin.user_grid.new": "ج<PERSON><PERSON><PERSON>", "admin.user_grid.notFound": "هیچ کاربری یافت نشد.", "admin.user_grid.remove": "<PERSON><PERSON><PERSON>", "admin.user_grid.role": "نقش", "admin.user_grid.shared_member": "عضو مشترک", "admin.user_grid.system_admin": "مدیر سیستم", "admin.user_grid.team_admin": "مدیر تیم", "admin.user_item.guest": "میه<PERSON>ان", "admin.user_item.makeActive": "فعال کنید", "admin.user_item.makeMember": "عضو تیم شوید", "admin.user_item.makeTeamAdmin": "تیم را ادمین کنید", "admin.user_item.manageTeams": "تیم ها را مدیریت کنید", "admin.user_item.member": "عضو", "admin.user_item.menuAriaLabel": "منوی اقدامات کاربر", "admin.user_item.resetMfa": "MFA ر<PERSON> حذف کنید", "admin.user_item.resetPwd": "بازیابی رمز عبور", "admin.user_item.sysAdmin": "ادمین سیستم", "admin.user_item.teamAdmin": "ادمین تیم", "admin.user_item.teamMember": "عضو تیم", "admin.viewArchivedChannelsHelpText": "وقتی درست است، به کاربران اجازه می‌دهد محتوای کانال‌هایی را که بایگانی شده‌اند مشاهده، به اشتراک بگذارند و جستجو کنند. کاربران فقط می توانند محتوای کانال هایی را مشاهده کنند که قبل از بایگانی شدن کانال در آن عضویت داشته اند.", "admin.viewArchivedChannelsTitle": "به کاربران اجازه دهید کانال های آرشیو شده را مشاهده کنند:", "admin.webserverModeDisabled": "معلول", "admin.webserverModeDisabledDescription": "سرور Mattermost فایل های استاتیک را ارائه نمی دهد.", "admin.webserverModeGzip": "gzip", "admin.webserverModeGzipDescription": "سرور Mattermost فایل های استاتیک فشرده شده با gzip را ارائه می دهد.", "admin.webserverModeHelpText": "فشرده سازی gzip برای فایل های محتوای ثابت اعمال می شود. توصیه می‌شود gzip را برای بهبود عملکرد فعال کنید، مگر اینکه محیط شما دارای محدودیت‌های خاصی باشد، مانند یک پروکسی وب که فایل‌های gzip را ضعیف توزیع می‌کند.", "admin.webserverModeTitle": "حالت وب سرور:", "admin.webserverModeUncompressed": "فشرده نشده", "admin.webserverModeUncompressedDescription": "سرور Mattermost فایل های استاتیک را بدون فشرده سازی ارائه می دهد.", "alert_banner.tooltipCloseBtn": "بستن", "analytics.chart.loading": "بارگذاری...", "analytics.chart.meaningful": "داده های کافی برای نمایش معنادار وجود ندارد.", "analytics.system.activeUsers": "کاربران فعال با پست", "analytics.system.channelTypes": "انواع کانال", "analytics.system.dailyActiveUsers": "کاربران فعال روزانه", "analytics.system.info": "از داده ها فقط برای تیم انتخاب شده استفاده کنید. پست‌هایی در کانال‌های پیام مستقیم که به یک تیم وابسته نیستند را حذف کنید.", "analytics.system.monthlyActiveUsers": "کاربران فعال ماهانه", "analytics.system.postTypes": "پست ها، فایل ها و هشتگ ها", "analytics.system.privateGroups": "کانال های خصوصی", "analytics.system.publicChannels": "کانال های عمومی", "analytics.system.skippedIntensiveQueries": "برای به حداکثر رساندن عملکرد، برخی از آمارها غیرفعال هستند. می‌توانید <link>آنها را دوباره در config.json فعال کنید</link>.", "analytics.system.textPosts": "پست هایی با متن فقط", "analytics.system.title": "آمار سیستم", "analytics.system.totalBotPosts": "مجموع پست های ربات ها", "analytics.system.totalChannels": "مجموع کانال ها", "analytics.system.totalCommands": "کل دستورات", "analytics.system.totalFilePosts": "پست هایی با فایل", "analytics.system.totalHashtagPosts": "پست هایی با هشتگ", "analytics.system.totalIncomingWebhooks": "وب هوک های ورودی", "analytics.system.totalMasterDbConnections": "Master <PERSON>", "analytics.system.totalOutgoingWebhooks": "وب هوک های خروجی", "analytics.system.totalPosts": "مجموع پست ها", "analytics.system.totalReadDbConnections": "Replica DB Conns", "analytics.system.totalSessions": "مجموع جلسات", "analytics.system.totalTeams": "مجموع تیم ها", "analytics.system.totalWebsockets": "Conns WebSocket", "analytics.team.activeUsers": "کاربران فعال با پست", "analytics.team.newlyCreated": "کاربران تازه ایجاد شده", "analytics.team.noTeams": "این سرور هیچ تیمی برای مشاهده آمار ندارد.", "analytics.team.privateGroups": "کانال های خصوصی", "analytics.team.publicChannels": "کانال های عمومی", "analytics.team.recentUsers": "کاربران فعال اخیر", "analytics.team.title": "آمار تیم برای {team}", "analytics.team.totalPosts": "مجموع پست ها", "analytics.team.totalUsers": "مجموع کاربران فعال", "announcement_bar.error.email_verification_required": "صندوق ورودی ایمیل خود را بررسی کنید تا آدرس را تأیید کنید.", "announcement_bar.error.license_expired": "مجوز سازمانی منقضی شده است و برخی از ویژگی‌ها ممکن است غیرفعال شوند.", "announcement_bar.error.license_expiring": "مجوز شرکت در {date, date, long} منقضی می شود.", "announcement_bar.error.past_grace": "مجوز سازمانی منقضی شده است و برخی از ویژگی‌ها ممکن است غیرفعال شوند. لطفاً برای جزئیات بیشتر با مدیر سیستم خود تماس بگیرید.", "announcement_bar.error.preview_mode": "حالت پیش نمایش: اعلان های ایمیل پیکربندی نشده اند.", "announcement_bar.error.purchase_a_license_now": "هم اکنون مجوز خریداری کنید", "announcement_bar.error.site_url.full": "لطفاً [سایت URL] (https://docs.Telegram.com/administration/config-settings.html#site-url) خود را در [System Console] (/admin_console/environment/web_server) پیکربندی کنید.", "announcement_bar.error.site_url_gitlab.full": "لطفاً [site URL] (https://docs.sofa.com/administration/config-settings.html#site-url) خود را در [System Console](/admin_console/environment/web_server) یا اگر دوباره از GitLab Mattermost در gitlab.rb استفاده کنید.", "announcement_bar.error.trial_license_expiring": "{روز} روز از دوره آزمایشی رایگان شما باقی مانده است.", "announcement_bar.error.trial_license_expiring_last_day": "این آخرین روز آزمایش رایگان شماست. برای ادامه استفاده از ویژگی های Mattermost Professional و Enterprise هم اکنون مجوز خریداری کنید.", "announcement_bar.notification.email_verified": "ایمیل تأییده شده است", "announcement_bar.warn.renew_license_contact_sales": "تماس با بخش فروش", "api.channel.add_guest.added": "{addedUsername} به عنوان مهمان توسط {username} به کانال اضافه شد.", "api.channel.add_member.added": "{addedUsername} توسط {username} به کانال اضافه شد.", "api.channel.delete_channel.archived": "{username} کانال را بایگانی کرد.", "api.channel.guest_join_channel.post_and_forget": "{username} به عنوان مهمان به کانال پیوست.", "api.channel.join_channel.post_and_forget": "{username} به کانال پیوست.", "api.channel.leave.left": "{username} از کانال خارج شد.", "api.channel.post_convert_channel_to_private.updated_from": "{username} کانال را از عمومی به خصوصی تبدیل کرد", "api.channel.post_update_channel_displayname_message_and_forget.updated_from": "{username} نام نمایش کانال را از: {old} به: {new} به روز کرد", "api.channel.post_update_channel_header_message_and_forget.removed": "{username} سرصفحه کانال را حذف کرد (بود: {قدیمی})", "api.channel.post_update_channel_header_message_and_forget.updated_from": "{username} سرصفحه کانال را به روز کرد <br></br><strong>از:</strong> {old} <br></br><strong>به:</strong> {new}", "api.channel.post_update_channel_header_message_and_forget.updated_to": "{username} سرصفحه کانال را به روز کرد: {new}", "api.channel.remove_member.removed": "{removedUsername} از کانال حذف شد", "api.channel.restore_channel.unarchived": "{username} کانال را از بایگانی خارج کرد.", "api.team.add_member.added": "{addedUsername} توسط {username} به تیم اضافه شد", "api.team.join_team.post_and_forget": "{username} به تیم پیوست.", "api.team.leave.left": "{username} تیم را ترک کرد.", "api.team.remove_user_from_team.removed": "{removedUsername} از تیم حذف شد.", "app.channel.post_update_channel_purpose_message.removed": "{username} هد<PERSON> کانال را حذف کرد (بود: {قدیمی})", "app.channel.post_update_channel_purpose_message.updated_from": "{username} هد<PERSON> کانال را از: {old} به: {new} به‌روزرسانی کرد", "app.channel.post_update_channel_purpose_message.updated_to": "{username} هد<PERSON> کانال را به‌روزرسانی کرد: {new}", "apps.error": "خطا: {error}", "apps.error.command.field_missing": "فیلدهای لازم وجود ندارد: `{fieldName}`.", "apps.error.command.same_channel": "تکرار کانال برای فیلد «{fieldName}»: «{option}».", "apps.error.command.same_option": "گزینه تکرار شده برای فیلد «{fieldName}»: «{option}».", "apps.error.command.same_user": "تکرار کاربر برای فیلد «{fieldName}»: «{option}».", "apps.error.command.unknown_channel": "کانال ناشناس برای فیلد `{fieldName}`: `{option}`.", "apps.error.command.unknown_option": "گزینه ناشناخته برای فیلد `{fieldName}`: `{option}`.", "apps.error.command.unknown_user": "کاربر ناشناس برای فیلد`{fieldName}`: `{option}`.", "apps.error.form.no_form": "`form`تعریف نشده است.", "apps.error.form.refresh": "هنگام به‌روزرسانی مدال خطایی روی داد. با توسعه دهنده برنامه تماس بگیرید. جزئیات: {جزئیات}", "apps.error.form.refresh_no_refresh": "به نام refresh on no refresh field.", "apps.error.form.submit.pretext": "در ارسال modal خطایی رخ داده است. با توسعه دهنده برنامه تماس بگیرید. جزئیات: {details}", "apps.error.lookup.error_preparing_request": "خطا در تهیه درخواست جستجو: {errorMessage}", "apps.error.parser": "خطای تجزیه: {error}", "apps.error.parser.empty_value": "مق<PERSON><PERSON><PERSON> خالی مجاز نیستند", "apps.error.parser.execute_non_leaf": "شما باید یک دستور فرعی را انتخاب کنید.", "apps.error.parser.missing_field_value": "مقدار فیلد وجود ندارد.", "apps.error.parser.missing_list_end": "نشانه بسته شدن لیست مورد انتظار", "apps.error.parser.missing_quote": "پیش‌بینی می‌شود قبل از پایان ورودی، مظنه مضاعف مطابقت داشته باشد.", "apps.error.parser.missing_tick": "پیش‌بینی می‌شود قبل از پایان ورودی، نقل قول تطبیق داشته باشد.", "apps.error.parser.multiple_equal": "چند علامت `=` مجاز نیست.", "apps.error.parser.no_argument_pos_x": "قادر به شناسایی استدلال نیست.", "apps.error.parser.no_bindings": "بدون الزامات دستوری", "apps.error.parser.no_form": "هیچ فرمی پیدا نشد", "apps.error.parser.no_match": "`{command}`: هیچ فرمان منطبقی در این فضای کاری یافت نشد.", "apps.error.parser.no_slash_start": "دستور باید با `/` شروع شود.", "apps.error.parser.unexpected_character": "شخصیت غیرمنتظره", "apps.error.parser.unexpected_comma": "کامای غیرمنتظره", "apps.error.parser.unexpected_error": "خطای غیرمنتظره.", "apps.error.parser.unexpected_flag": "فرمان پرچم `{flagName}` را نمی‌پذیرد.", "apps.error.parser.unexpected_squared_bracket": "باز شدن غیرمنتظره لیست", "apps.error.parser.unexpected_state": "غیرقابل دسترس: حالت غیرمنتظره درmatchBinding: `{state}`.", "apps.error.parser.unexpected_whitespace": "غیر قابل دسترس: فضای خالی غیرمنتظره.", "apps.error.responses.form.no_form": "نوع پاسخ `form` است، اما هیچ فرمی در پاسخ گنجانده نشده است.", "apps.error.responses.navigate.no_url": "نوع پاسخ «پیمایش» است، اما هیچ نشانی اینترنتی در پاسخ گنجانده نشده است.", "apps.error.responses.unexpected_error": "یک خطای غیرمنتظره دریافت کرد.", "apps.error.responses.unexpected_type": "نوع پاسخ برنامه انتظار نمی رفت. نوع پاسخ: {type}", "apps.error.responses.unknown_field_error": "خطای یک فیلد ناشناخته دریافت کرد. نام فیلد: `{field}`. خطا:\n{خطا}", "apps.error.responses.unknown_type": "نوع پاسخ برنامه پشتیبانی نمی شود. نوع پاسخ: {type}.", "apps.error.unknown": "خطای ناشناخته رخ داد.", "apps.suggestion.dynamic.error": "خطای انتخاب پویا", "apps.suggestion.errors.parser_error": "خطای تجزیه", "apps.suggestion.no_dynamic": "هیچ داده ای برای پیشنهادات پویا بازگردانده نشد", "apps.suggestion.no_static": "هیچ گزینه منطبقی وجود ندارد.", "apps.suggestion.no_suggestion": "هیچ پیشنهاد منطبقی وجود ندارد.", "atmos/camo": "اتمس / camo", "audit_table.accountActive": "اکانت فعال شد", "audit_table.accountInactive": "اکانت غیرفعال شد", "audit_table.action": "<PERSON><PERSON><PERSON>", "audit_table.attemptedAllowOAuthAccess": "تلاش برای اجازه دسترسی به سرویس O<PERSON><PERSON> جدید", "audit_table.attemptedLicenseAdd": "تلاش برای اضافه کردن مجوز جدید", "audit_table.attemptedLogin": "تلاش برای ورود به سیستم", "audit_table.attemptedOAuthToken": "تلاش برای دریافت نشانه دسترسی OAuth", "audit_table.attemptedPassword": "تلاش برای تغییر رمز عبور", "audit_table.attemptedRegisterApp": "تلاش برای ثبت یک برنامه OAuth جدید با شناسه {id}", "audit_table.attemptedReset": "تلاش برای بازنشانی رمز عبور", "audit_table.attemptedWebhookCreate": "تلاش برای ایجاد یک وب هوک", "audit_table.attemptedWebhookDelete": "تلاش برای حذف یک وب هوک", "audit_table.authenticated": "با موفقیت احراز هویت شد", "audit_table.by": " توسط {username}", "audit_table.byAdmin": " توسط یک ادمین", "audit_table.channelCreated": "کانال {channelName} را ایجاد کرد", "audit_table.channelDeleted": "کانال را با URL {url} بایگانی کرد", "audit_table.establishedDM": "یک کانال پیام مستقیم با {username} ایج<PERSON> کرد", "audit_table.failedExpiredLicenseAdd": "مجوز جدید اضافه نشد زیرا منقضی شده یا هنوز راه اندازی نشده است", "audit_table.failedInvalidLicenseAdd": "مجوز نامعتبر اضافه نشد", "audit_table.failedLogin": "تلاش برای ورود ناموفق", "audit_table.failedOAuthAccess": "اجازه دسترسی به سرویس O<PERSON>uth جدید داده نشد - URI تغییر مسیر با پاسخ تماس قبلی ثبت شده مطابقت نداشت", "audit_table.failedPassword": "تغییر رمز عبور ناموفق بود - سعی شد رمز عبور کاربری را که از طریق OAuth وارد شده بود به روز کند", "audit_table.failedWebhookCreate": "ایجاد یک هوک ناموفق - مجوزهای کانال بد", "audit_table.failedWebhookDelete": "حذف یک هوک انجام نشد - شرایط نامناسب", "audit_table.headerUpdated": "سرصفحه کانال {channelName} را به روز کرد", "audit_table.ip": "آدرس آی پی", "audit_table.licenseRemoved": "یک مجوز با موفقیت حذف شد", "audit_table.loginAttempt": " (تلاش برای ورود به سیستم)", "audit_table.loginFailure": " (ورود ناموفق)", "audit_table.logout": "از ح<PERSON>ا<PERSON> خود خارج شدید", "audit_table.member": "عضو", "audit_table.nameUpdated": "نام کانال {channelName} را به روز کرد", "audit_table.oauthTokenFailed": "دریافت رمز دسترسی OAuth ناموفق - {token}", "audit_table.revokedAll": "تمام جلسات فعلی تیم لغو شد", "audit_table.sentEmail": "یک ایمیل به {email} ارسال کرد تا رمز عبور خود را بازنشانی کند", "audit_table.session": "شناسه جلسه", "audit_table.sessionRevoked": "جلسه با شناسه {sessionId} لغو شد", "audit_table.successfullLicenseAdd": "مجوز جدید با موفقیت اضافه شد", "audit_table.successfullLogin": "با موفقیت وارد سیستم شدید", "audit_table.successfullOAuthAccess": "با موفقیت به سرویس OAuth جدید دسترسی داد", "audit_table.successfullOAuthToken": "یک سرویس O<PERSON>uth جدید با موفقیت اضافه شد", "audit_table.successfullPassword": "رمز عبور با موفقیت تغییر کرد", "audit_table.successfullReset": "رمز عبور با موفقیت بازنشانی شد", "audit_table.successfullWebhookCreate": "یک وب هوک با موفقیت ایجاد شد", "audit_table.successfullWebhookDelete": "یک وب هوک با موفقیت حذف شد", "audit_table.timestamp": "مهر زمان", "audit_table.updateGeneral": "تنظیمات کلی حساب شما را به روز کرد", "audit_table.updateGlobalNotifications": "تنظیمات اعلان جهانی شما را به روز کرد", "audit_table.updatePicture": "عکس نمایه خود را به روز کرد", "audit_table.updatedRol": "نقش(های) کاربر به روز شد ", "audit_table.userAdded": "{username} به کانال {channelName} اضافه شد", "audit_table.userId": "شناسه کاربری", "audit_table.userRemoved": "{username} از کانال {channelName} حذف شد", "audit_table.verified": "آدرس ایمیل شما با موفقیت تایید شد", "authorize.allow": "اجازه", "authorize.deny": "انکار", "backstage_list.search": "جستجو کردن", "backstage_navbar.back": "بازگشت", "backstage_navbar.backToMattermost": "بازگشت به {siteName}", "backstage_sidebar.bots": "حساب های ربات", "backstage_sidebar.emoji": "ایموجی سفارشی", "backstage_sidebar.integrations": "ادغام ها", "backstage_sidebar.integrations.commands": "دستورات اسلش", "backstage_sidebar.integrations.incoming_webhooks": "وب هوک های ورودی", "backstage_sidebar.integrations.oauthApps": "برنامه های OAuth 2.0", "backstage_sidebar.integrations.outgoing_webhooks": "وب هوک های خروجی", "bot.add.description": "شرح", "bot.add.description.help": "(اختیاری) به دیگران اطلاع دهید که این ربات چه کار می کند.", "bot.add.display_name.help": "(اختیاری) می توانید نام کامل ربات خود را به جای نام کاربری آن نمایش دهید.", "bot.add.post_all": "پست: همه", "bot.add.post_all.enabled": "فعال شد", "bot.add.post_all.help": "ربات به همه کانال‌های Mattermost از جمله پیام‌های مستقیم دسترسی خواهد داشت.", "bot.add.post_channels": "پست: کانال ها", "bot.add.post_channels.enabled": "فعال شد", "bot.add.post_channels.help": "ربات به همه کانال‌های عمومی Mattermost دسترسی خواهد داشت.", "bot.add.role": "نقش", "bot.add.role.admin": "ادمین سیستم", "bot.add.role.help": "انتخاب کنید که ربات چه نقشی باید داشته باشد.", "bot.add.role.member": "عضو", "bot.add.username.help": "می توانید از حروف کوچک، اعداد، نقطه، خط تیره و زیرخط استفاده کنید.", "bot.create_failed": "ربات ایجاد نشد", "bot.create_token.close": "نزدیک", "bot.edit_failed": "ربات ویرایش نشد", "bot.manage.create_token": "توکن جدید ایجاد کنید", "bot.manage.disable": "غیر فعال کردن", "bot.manage.enable": "فعال کردن", "bot.remove_profile_picture": "نماد ربات را حذف کنید", "bot.token.default.description": "رمز پیش فرض", "bot.token.error.description": "لطفا یک توضیح وارد کنید", "bots.add.displayName": "نام نمایشی", "bots.add.icon": "نماد ربات", "bots.add.username": "نام کاربری", "bots.disabled": "معلول", "bots.image.upload": "آپلود تصویر", "bots.manage.add": "اضافه کردن حساب ربات", "bots.manage.add.cancel": "انصراف", "bots.manage.add.create": "ایجاد حساب ربات", "bots.manage.add.creating": "پدید آوردن...", "bots.manage.add.invalid_username": "نام کاربری باید با یک حرف کوچک شروع شود و 3 تا 22 کاراکتر باشد. می توانید از حروف کوچک، اعداد، نقطه، خط تیره و زیرخط استفاده کنید.", "bots.manage.bot_accounts": "حساب های ربات", "bots.manage.created.text": "حساب ربات شما **{botname}** با موفقیت ایجاد شد. لطفاً از نشانه دسترسی زیر برای اتصال به ربات استفاده کنید (برای جزئیات بیشتر به <link>اسناد</link> مراجعه کنید).", "bots.manage.description": "از حساب‌های ربات برای ادغام با Mattermost از طریق افزونه‌ها یا API استفاده کنید", "bots.manage.edit": "ویرایش کنید", "bots.manage.edit.editing": "در حال بروز رسانی...", "bots.manage.edit.title": "به روز رسانی", "bots.manage.empty": "هیچ حساب رباتی پیدا نشد", "bots.manage.header": "حساب های ربات", "bots.manage.help1": "از {botAccounts} برای ادغام با Mattermost از طریق افزونه ها یا API استفاده کنید. حساب های ربات برای همه در سرور شما در دسترس است. ", "bots.manage.search": "جستجو در حساب های ربات", "bots.managed_by": "مدیریت شده توسط ", "bots.token.confirm": "<PERSON><PERSON><PERSON>", "bots.token.confirm_text": "آیا مطمئن هستید که می خواهید رمز را حذف کنید؟", "bots.token.delete": "توکن را حذف کنید", "call_button.menuAriaLabel": "انتخابگر نوع تماس", "carousel.PreviousButton": "قب<PERSON>ی", "carousel.nextButton": "بعد", "center_panel.archived.closeChannel": "کانال را ببند", "center_panel.direct.closeDirectMessage": "بستن پیام دایرکت", "center_panel.direct.closeGroupMessage": "بستن پیام گروه", "change_url.endWithLetter": "URL ها باید با یک حرف کوچک یا عدد ختم شوند.", "change_url.helpText": "می توانید از حروف کوچک، اعداد، خط تیره و زیرخط استفاده کنید.", "change_url.invalidDirectMessage": "شناسه های کاربری در URL های کانال مجاز نیستند.", "change_url.invalidUrl": "URL نامعتبر است", "change_url.longer": "URL ها باید حداقل 2 کاراکتر داشته باشند.", "change_url.startAndEndWithLetter": "URL ها باید با یک حرف یا عدد کوچک شروع و ختم شوند.", "change_url.startWithLetter": "URL ها باید با یک حرف کوچک یا عدد شروع شوند.", "channelHeader.addToFavorites": "افزودن به علاقه مندی ها", "channelHeader.hideInfo": "بستن اطلاعات", "channelHeader.removeFromFavorites": "از علاقه مندی ها حذف شود", "channelHeader.unmute": "باصدا کردن", "channelHeader.viewInfo": "نمایش اطلاعات", "channelView.login.successfull": "ورود موفق", "channel_header.addChannelHeader": "توضیحات کانال اضافه کنید", "channel_header.channelFiles": "فایل های کانال", "channel_header.channelHasGuests": "این کانال مهمان دارد", "channel_header.channelMembers": "اعضا", "channel_header.closeChannelInfo": "بستن اطلاعات", "channel_header.convert": "تبدیل به کانال خصوصی", "channel_header.delete": "کانال آرشیو", "channel_header.directchannel.you": "{displayname} (شما) ", "channel_header.editLink": "ویرایش کنید", "channel_header.flagged": "پست های ذخیره شده", "channel_header.groupMessageHasGuests": "این پیام گروهی مهمان دارد", "channel_header.leave": "ترک کانال", "channel_header.manageMembers": "مدیریت اعضا", "channel_header.menuAriaLabel": "منوی کانال", "channel_header.mute": "نادیده گرفتن کانال", "channel_header.muteConversation": "صدای گفتگو را قطع کن", "channel_header.openChannelInfo": "نمایش اطلاعات", "channel_header.pinnedPosts": "پست های پین شده", "channel_header.recentMentions": "اشاره های اخیر", "channel_header.rename": "تغییر نام کانال", "channel_header.search": "جستجو کردن", "channel_header.setConversationHeader": "ویرایش سرصفحه گفتگو", "channel_header.setHeader": "هدر کانال را ویرایش کنید", "channel_header.setPurpose": "ویرایش هدف کانال", "channel_header.unarchive": "لغو آرشیو کانال", "channel_header.unmute": "لغو نادیده گرفتن کانال", "channel_header.unmuteConversation": "باصدا کردن مکالمه", "channel_header.userHelpGuide": "کمک", "channel_header.viewMembers": "مشاهده عضوها", "channel_info_rhs.about_area.channel_header.line_limiter.less": "کمتر", "channel_info_rhs.about_area.channel_header.line_limiter.more": "بیشتر", "channel_info_rhs.about_area.channel_purpose.line_limiter.less": "کمتر", "channel_info_rhs.about_area.channel_purpose.line_limiter.more": "بیشتر", "channel_info_rhs.about_area_id": "کد:", "channel_info_rhs.edit_link": "ویرایش", "channel_info_rhs.header.title": "اطلاعات", "channel_info_rhs.menu.files": "پرونده‌ها", "channel_info_rhs.menu.files.filter": "صافی", "channel_info_rhs.menu.members": "اعضا", "channel_info_rhs.menu.notification_preferences": "ترجیحات اعلان", "channel_info_rhs.menu.pinned": "پیام‌های سنجاق‌شده", "channel_info_rhs.top_buttons.add_people": "افزودن افراد", "channel_info_rhs.top_buttons.copied": "رونوشت شد", "channel_info_rhs.top_buttons.copy": "رونوشت پیوند", "channel_info_rhs.top_buttons.favorite": "برگزیده", "channel_info_rhs.top_buttons.favorited": "برگزیده شد", "channel_info_rhs.top_buttons.mute": "بی‌صدا", "channel_info_rhs.top_buttons.muted": "بی‌صدا شد", "channel_invite.addNewMembers": "افزودن افراد به {channel}", "channel_loader.posted": "ارسال شده", "channel_loader.postedImage": " یک تصویر ارسال کرد", "channel_loader.socketError": "لطفاً اتصال را بررسی کنید، Mattermost غیر قابل دسترس است. اگر مشکل همچنان ادامه داشت، از سرپرست بخواهید [درگاه WebSocket را بررسی کند](!https://docs.sofa.com/install/troubleshooting.html#please-check-connection-mattermost-unreachable-if-issue-persists-ask-administrator-to-check-websocket-port).", "channel_loader.someone": "کسی", "channel_loader.something": " کار جدیدی انجام داد", "channel_loader.unknown_error": "ما یک کد وضعیت غیرمنتظره از سرور دریافت کردیم.", "channel_loader.uploadedFile": " یک فایل آپلود کرد", "channel_loader.uploadedImage": " یک تصویر آپلود کرد", "channel_members_dropdown.channel_admin": "ادمین کانال", "channel_members_dropdown.channel_admins": "ادمین های کانال", "channel_members_dropdown.channel_guest": "م<PERSON><PERSON><PERSON> کانال", "channel_members_dropdown.channel_member": "عضو کانال", "channel_members_dropdown.channel_members": "اعضای کانال", "channel_members_dropdown.make_channel_admin": "کانال را ادمین کنید", "channel_members_dropdown.make_channel_admins": "ایج<PERSON> ادمین کانال", "channel_members_dropdown.make_channel_member": "عضو کانال شوید", "channel_members_dropdown.make_channel_members": "عضو کانال شوید", "channel_members_dropdown.menuAriaLabel": "نقش عضو کانال را تغییر دهید", "channel_members_dropdown.remove_from_channel": "حذ<PERSON> از کانال", "channel_members_modal.addNew": " اضافه کردن اعضا", "channel_members_modal.members": " اعضا", "channel_members_rhs.action_bar.add_button": "افزودن", "channel_members_rhs.action_bar.done_button": "انجام شد", "channel_members_rhs.action_bar.manage_button": "مدی<PERSON><PERSON>ت", "channel_members_rhs.action_bar.managing_title": "مدیریت اعضا", "channel_members_rhs.action_bar.members_count_title": "{members_count} عضو", "channel_members_rhs.header.title": "اعضا", "channel_members_rhs.list.channel_admin_title": "سرپرست‌های کانال", "channel_members_rhs.list.channel_members_title": "اعضا", "channel_members_rhs.member.select_role_channel_admin": "سرپرست", "channel_members_rhs.member.select_role_channel_member": "عضو", "channel_members_rhs.member.select_role_guest": "مه<PERSON>ان", "channel_members_rhs.member.send_message": "ارسال پیام", "channel_members_rhs.search_bar.placeholder": "جست‌وجوی اعضا", "channel_modal.cancel": "انصراف", "channel_modal.createNew": "ایج<PERSON> کانال", "channel_modal.modalTitle": "ک<PERSON><PERSON><PERSON> جدید", "channel_modal.name.label": "نام کانال", "channel_modal.type.private.title": "کانال خصوصی", "channel_modal.type.public.title": "کانال عمومی", "channel_notifications.levels.all": "همه", "channel_notifications.preferences": "تنظیمات برگزیده اعلان برای ", "channel_select.placeholder": "--- انت<PERSON>ا<PERSON> یک کانال ---", "channel_switch_modal.deactivated": "غیرفعال شد", "channel_toggle_button.private": "خصوصی", "channel_toggle_button.public": "عمومی", "claim.email_to_ldap.enterLdapPwd": "شناسه و رمز عبور حساب AD/LDAP خود را وارد کنید", "claim.email_to_ldap.enterPwd": "رمز عبور حساب ایمیل {site} خود را وارد کنید", "claim.email_to_ldap.ldapId": "شناسه AD/LDAP", "claim.email_to_ldap.ldapIdError": "لطفا شناسه AD/LDAP خود را وارد کنید.", "claim.email_to_ldap.ldapPasswordError": "لطفا رمز عبور AD/LDAP خود را وارد کنید.", "claim.email_to_ldap.ldapPwd": "رمز عبور AD/LDAP", "claim.email_to_ldap.pwd": "کلمه عبور", "claim.email_to_ldap.pwdError": "لطفا رمز عبور خود را وارد کنید.", "claim.email_to_ldap.ssoNote": "از قبل باید یک حساب AD/LDAP معتبر داشته باشید", "claim.email_to_ldap.ssoType": "پس از ادعای حساب خود، فقط می توانید با AD/LDAP وارد شوید", "claim.email_to_ldap.switchTo": "تغییر حساب به AD/LDAP", "claim.email_to_ldap.title": "حساب ایمیل/رمز عبور را به AD/LDAP تغییر دهید", "claim.email_to_oauth.enterPwd": "رمز عبور حساب {site} خود را وارد کنید", "claim.email_to_oauth.pwd": "کلمه عبور", "claim.email_to_oauth.pwdError": "لطفا رمز عبور خود را وارد کنید.", "claim.email_to_oauth.ssoNote": "از قبل باید یک حساب {type} معتبر داشته باشید", "claim.email_to_oauth.ssoType": "پس از ادعای حساب خود، فقط می توانید با {type} SSO وارد شوید", "claim.email_to_oauth.switchTo": "تغییر حساب به {uiType}", "claim.email_to_oauth.title": "تغییر حساب ایمیل/گذرواژه به {uiType}", "claim.ldap_to_email.confirm": "رمز عبور را تایید کنید", "claim.ldap_to_email.email": "پس از تغییر روش احراز هویت، از {email} برای ورود استفاده خواهید کرد. اعتبارنامه AD/LDAP شما دیگر اجازه دسترسی به Mattermost را نمی دهد.", "claim.ldap_to_email.enterLdapPwd": "{ldapPassword}:", "claim.ldap_to_email.enterPwd": "رمز ورود ایمیل جدید:", "claim.ldap_to_email.ldapPasswordError": "لطفا رمز عبور AD/LDAP خود را وارد کنید.", "claim.ldap_to_email.ldapPwd": "رمز عبور AD/LDAP", "claim.ldap_to_email.pwd": "کلمه عبور", "claim.ldap_to_email.pwdError": "لطفا رمز عبور خود را وارد کنید.", "claim.ldap_to_email.pwdNotMatch": "رمزهای ورود مطابقت ندارند.", "claim.ldap_to_email.switchTo": "تغییر حساب به ایمیل/رمز عبور", "claim.ldap_to_email.title": "حساب AD/LDAP را به ایمیل/رمز عبور تغییر دهید", "claim.oauth_to_email.confirm": "رمز عبور را تایید کنید", "claim.oauth_to_email.description": "با تغییر نوع حساب خود، فقط می توانید با ایمیل و رمز عبور وارد شوید.", "claim.oauth_to_email.enterNewPwd": "یک رمز عبور جدید برای حساب ایمیل {site} خود وارد کنید", "claim.oauth_to_email.enterPwd": "لطفا یک رمز عبور وارد کنید.", "claim.oauth_to_email.newPwd": "<PERSON><PERSON><PERSON> عبور جدید", "claim.oauth_to_email.pwdNotMatch": "رمزهای ورود مطابقت ندارند.", "claim.oauth_to_email.switchTo": "{type} را به ایمیل و رمز عبور تغییر دهید", "claim.oauth_to_email.title": "حساب {type} را به ایمیل تغییر دهید", "cloud.fetch_error.retry": "تلاش‌دوباره", "cloud_archived.error.title": "پیام بایگانی شد", "cloud_billing_history_modal.title": "فاکتور(ها)", "collapsed_reply_threads_modal.confirm": "فهمیدم", "collapsed_reply_threads_modal.skip_tour": "رد کردن معرفی", "collapsed_reply_threads_modal.take_the_tour": "دوره", "collapsed_reply_threads_modal.title": "شما در حال دسترسی به بتای اولیه رشته‌های پاسخ جمع‌شده هستید", "combined_system_message.added_to_channel.many_expanded": "{users} و {lastUser} توسط {actor} **به کانال** اضافه شدند.", "combined_system_message.added_to_channel.one": "{firstUser} **به کانال** توسط {actor} اضافه شد.", "combined_system_message.added_to_channel.one_you": "شما توسط {actor} **به کانال** اضافه شدید.", "combined_system_message.added_to_channel.two": "{firstUser} و {secondUser} **توسط {actor} به کانال اضافه شد**.", "combined_system_message.added_to_team.many_expanded": "{users} و {lastUser} توسط {actor} **به تیم اضافه شدند**.", "combined_system_message.added_to_team.one": "{firstUser} **توسط {actor} به تیم اضافه شد**.", "combined_system_message.added_to_team.one_you": "شما *توسط {actor} به تیم** اضافه شدید.", "combined_system_message.added_to_team.two": "{firstUser} و {secondUser} **توسط {actor} به تیم اضافه شد**.", "combined_system_message.joined_channel.many_expanded": "{users} و {lastUser} **به کانال پیوستند**.", "combined_system_message.joined_channel.one": "{firstUser} **به کانال پیوست**.", "combined_system_message.joined_channel.one_you": "شما **به کانال پیوستید**.", "combined_system_message.joined_channel.two": "{firstUser} و {secondUser} **به کانال پیوستند**.", "combined_system_message.joined_team.many_expanded": "{users} و {lastUser} **به تیم پیوستند**.", "combined_system_message.joined_team.one": "{firstUser} **به تیم پیوست**.", "combined_system_message.joined_team.one_you": "شما **به تیم پیوستید**.", "combined_system_message.joined_team.two": "{firstUser} و {secondUser} **به تیم پیوستند**.", "combined_system_message.left_channel.many_expanded": "{users} و {lastUser} **کانال را ترک کردند**.", "combined_system_message.left_channel.one": "{firstUser} **از کانال خارج شد**.", "combined_system_message.left_channel.one_you": "شما **کانال را ترک کردید**.", "combined_system_message.left_channel.two": "{firstUser} و {secondUser} **کانال را ترک کردند**.", "combined_system_message.left_team.many_expanded": "{users} و {lastUser} **تیم را ترک کردند**.", "combined_system_message.left_team.one": "{firstUser} **تیم را ترک کرد**.", "combined_system_message.left_team.one_you": "شما **تیم را ترک کردید**.", "combined_system_message.left_team.two": "{firstUser} و {secondUser} **تیم را ترک کردند**.", "combined_system_message.removed_from_channel.many_expanded": "{users} و {lastUser} **از کانال حذف شدند**.", "combined_system_message.removed_from_channel.one": "{firstUser} **از کانال حذف شد**.", "combined_system_message.removed_from_channel.one_you": "شما **از کانال حذف شدید**.", "combined_system_message.removed_from_channel.two": "{firstUser} و {secondUser} **از کانال حذف شدند**.", "combined_system_message.removed_from_team.many_expanded": "{users} و {lastUser} **از تیم حذف شدند**.", "combined_system_message.removed_from_team.one": "{firstUser} **از تیم حذف شد**.", "combined_system_message.removed_from_team.one_you": "شما **از تیم حذف شدید**.", "combined_system_message.removed_from_team.two": "{firstUser} و {secondUser} **از تیم حذف شدند**.", "combined_system_message.you": "شما", "commercial_support.download_support_packet": "بارگیری بسته پشتیبانی", "commercial_support.title": "پشتیبانی تجاری", "confirm_modal.cancel": "انصراف", "confirm_switch_to_yearly_modal.confirm": "تا<PERSON><PERSON>د", "confirm_switch_to_yearly_modal.contact_sales": "تماس با بخش فروش", "confirm_switch_to_yearly_modal.subtitle2": "برای اطلاعات بیشتر، لطفا با بخش فروش تماس بگیرید.", "convert_channel.cancel": "نه، لغو", "convert_channel.confirm": "بله تبدیل به کانال خصوصی", "convert_channel.question2": "تغییر دائمی است و قابل بازگشت نیست.", "convert_channel.title": "{display_name} به یک کانال خصوصی تبدیل شود؟", "copied.message": "رونوشت شد", "copy.code.message": "رونوشت کد", "copy.text.message": "رونوشت متن", "create_category_modal.create": "ایج<PERSON> کردن", "create_category_modal.createCategory": "ایج<PERSON> رده جدید", "create_comment.addComment": "پاسخ به این تاپیک...", "create_group_memberships_modal.cancel": "<PERSON><PERSON><PERSON>", "create_group_memberships_modal.create": "بله", "create_post.error_message": "پیام شما خیلی طولانی است تعداد کاراکتر: {length}/{limit}", "create_post.fileProcessing": "در حال پردازش...", "create_post.prewritten.custom": "یک پیام سفارشی بنویسید...", "create_post.prewritten.tip.dm_hello": "اوه سلام", "create_post.prewritten.tip.dm_hello_message": ":v: اوه سلام", "create_post.prewritten.tip.dm_hey": "هی", "create_post.prewritten.tip.dm_hey_message": ":wave: سلام @{username}", "create_post.prewritten.tip.self_note": "توجه به خود ...", "create_post.prewritten.tip.self_should": "فردا باید...", "create_post.prewritten.tip.team_excited": "از بودن در اینجا هیجان زده هستم!", "create_post.prewritten.tip.team_excited_message": ":raised_hands: از حضور در اینجا هیجان زده ام!", "create_post.prewritten.tip.team_hey": "هی همه!", "create_post.prewritten.tip.team_hey_message": ":smile: سلام به همه!", "create_post.prewritten.tip.team_hi": "سلام تیم!", "create_post.prewritten.tip.team_hi_message": ":wave: سلام تیم!", "create_post.read_only": "این کانال فقط خواندنی است. فقط اعضای دارای مجوز می توانند در اینجا پست بگذارند.", "create_post.shortcutsNotSupported": "میانبرهای صفحه کلید در دستگاه شما پشتیبانی نمی شود.", "create_post.write": "نوشتن در {channelDisplayName}", "create_team.createTeamRestricted.title": "ویژگی تخصصی", "create_team.display_name.charLength": "نام باید {min} یا بیشتر تا حداکثر {حداکثر} نویسه باشد. بعداً می‌توانید شرح تیم طولانی‌تری اضافه کنید.", "create_team.display_name.nameHelp": "تیم خود را به هر زبانی نام ببرید. نام تیم شما در منوها و سرفصل ها نشان داده می شود.", "create_team.display_name.next": "بعد", "create_team.display_name.required": "این فیلد الزامی است", "create_team.display_name.teamName": "نام گروه", "create_team.team_url.back": "بازگشت به مرحله قبل", "create_team.team_url.charLength": "نام باید {min} یا بیشتر تا حداکثر {max} نویسه باشد.", "create_team.team_url.creatingTeam": "ایجاد تیم ...", "create_team.team_url.finish": "پایان", "create_team.team_url.hint1": "کوتاه و به یاد ماندنی بهترین است", "create_team.team_url.hint2": "از حروف کوچک، اعداد و خط تیره استفاده کنید", "create_team.team_url.hint3": "باید با یک حرف شروع شود و نمی تواند به خط تیره ختم شود", "create_team.team_url.regex": "فقط از حروف کوچک، اعداد و خط تیره استفاده کنید. باید با یک حرف شروع شود و نمی تواند به خط تیره ختم شود.", "create_team.team_url.required": "این فیلد الزامی است", "create_team.team_url.taken": "این URL <link>با یک کلمه رزرو شده شروع می شود</link> یا در دسترس نیست. لطفا دیگری را امتحان کنید.", "create_team.team_url.teamUrl": "آدرس تیم", "create_team.team_url.unavailable": "این URL گرفته شده یا در دسترس نیست. لطفا دیگری را امتحان کنید.", "create_team.team_url.webAddress": "آدرس وب تیم جدید خود را انتخاب کنید:", "custom_emoji.header": "ایموجی سفارشی", "custom_status.expiry.time_picker.title": "زمان", "custom_status.expiry.until": "تا زمان", "custom_status.expiry_dropdown.choose_date_and_time": "تاریخ و زمان را انتخاب کنید", "custom_status.expiry_dropdown.clear_after": "بعد پاک کن", "custom_status.expiry_dropdown.date_and_time": "تاریخ و زمان سفارشی", "custom_status.expiry_dropdown.dont_clear": "پاک نکن", "custom_status.expiry_dropdown.four_hours": "4 ساعت", "custom_status.expiry_dropdown.one_hour": "1 ساعت", "custom_status.expiry_dropdown.thirty_minutes": "30 دقیقه", "custom_status.expiry_dropdown.this_week": "این هفته", "custom_status.expiry_dropdown.today": "امروز", "custom_status.modal_cancel": "پاکسازی وضعیت", "custom_status.modal_confirm": "تنظیم وضعیت", "custom_status.set_status": "یک وضعیت تنظیم کنید", "custom_status.suggestions.in_a_meeting": "در یک جلسه", "custom_status.suggestions.on_a_vacation": "در تعطیلات", "custom_status.suggestions.out_for_lunch": "بیرون برای ناهار", "custom_status.suggestions.out_sick": "بیرون مریض", "custom_status.suggestions.recent_title": "ا<PERSON><PERSON>ر", "custom_status.suggestions.title": "پیشنهادات", "custom_status.suggestions.working_from_home": "کار از خانه", "date_separator.today": "امروز", "date_separator.tomorrow": "فردا", "date_separator.yesterday": "دی<PERSON><PERSON><PERSON>", "datetime.today": "امروز", "datetime.yesterday": "دی<PERSON><PERSON><PERSON>", "deactivate_member_modal.deactivate": "از کار انداختن", "deactivate_member_modal.desc": "این عمل {username} را غیرفعال می‌کند. آنها از سیستم خارج می شوند و به هیچ تیم یا کانالی در این سیستم دسترسی نخواهند داشت.\n", "deactivate_member_modal.desc.confirm": " آیا مطمئنید که می خواهید {username} را غیرفعال کنید؟", "deactivate_member_modal.sso_warning": "همچنین باید این کاربر را در ارائه‌دهنده SSO غیرفعال کنید، در غیر این صورت با ورود یا همگام‌سازی بعدی دوباره فعال می‌شوند.", "deactivate_member_modal.title": "غیرفعال کردن {username}", "delete_category_modal.delete": "<PERSON><PERSON><PERSON>", "delete_category_modal.deleteCategory": "این دسته حذف شود؟", "delete_category_modal.helpText": "کانال‌های **{category_name}** به دسته‌های کانال‌ها و پیام‌های مستقیم برمی‌گردند. شما از هیچ کانالی حذف نشده اید.", "delete_channel.cancel": "انصراف", "delete_channel.confirm": "کانال آرشیو را تایید کنید", "delete_channel.del": "بایگانی", "delete_post.cancel": "انصراف", "delete_post.del": "<PERSON><PERSON><PERSON>", "delete_post.warning": "این پست دارای {count, number} {count, plural, one {comment} other {comments}} روی آن است.", "demote_to_user_modal.demote": "تنزل دادن", "demote_to_user_modal.desc": "این اقدام کاربر {username} را به یک مهمان تنزل می دهد. توانایی کاربر برای پیوستن به کانال‌های عمومی و تعامل با کاربران خارج از کانال‌هایی که در حال حاضر در آن عضو هستند را محدود می‌کند. آیا مطمئن هستید که می خواهید کاربر {username} را به مهمان تنزل دهید؟", "demote_to_user_modal.title": "کاربر {username} را به مهمان تنزل دهید", "device_icons.android": "نماد اندروید", "device_icons.apple": "نماد اپل", "device_icons.linux": "نماد لینوکس", "device_icons.windows": "آیکون ویندوز", "discard_changes_modal.leave": "بله، رد کنید", "discard_changes_modal.message": "شما تغییرات ذخیره نشده ای دارید، آیا مطمئن هستید که می خواهید آنها را نادیده بگیرید؟", "discard_changes_modal.title": "لغو تغییرات؟", "dnd_custom_time_picker_modal.date": "تاریخ", "dnd_custom_time_picker_modal.defaultMsg": "غیرفعال کردن اعلان ها تا زمانی که", "dnd_custom_time_picker_modal.submitButton": "غیر فعال کردن اعلانها", "dnd_custom_time_picker_modal.time": "زمان", "drafts.actions.delete": "حذف پیش‌نویس", "drafts.actions.edit": "ویرایش پیش‌نویس", "drafts.actions.send": "ارسال پیش‌نویس", "drafts.confirm.delete.button": "بله، حذف کن", "drafts.confirm.delete.title": "حذف پیش‌نویس", "drafts.draft_title.you": "(شما)", "drafts.heading": "پیش‌نویس‌ها", "drafts.sidebarLink": "پیش‌نویس‌ها", "drafts.tutorialTip.title": "پیش‌نویس‌ها", "drafts.tutorial_tip.notNow": "الان نه", "drafts.tutorial_tip.viewDrafts": "نمایش پیش‌نویس‌ها", "edit_category_modal.helpText": "کانال ها را به این دسته بکشید تا نوار کناری خود را سازماندهی کنید.", "edit_category_modal.placeholder": "دسته خود را نام ببرید", "edit_channel_header_modal.cancel": "انصراف", "edit_channel_header_modal.description": "متنی که در کنار نام کانال در هدر کانال ظاهر می شود را ویرایش کنید.", "edit_channel_header_modal.error": "متن وارد شده بیش از حد مجاز کاراکتر است. هدر کانال به نویسه {maxLength} محدود شده است.", "edit_channel_header_modal.save": "صرفه جویی", "edit_channel_header_modal.title": "ویرایش سرصفحه برای {channel}", "edit_channel_header_modal.title_dm": "ویرایش هدر", "edit_channel_private_purpose_modal.body": "این متن در حالت \"نمایش اطلاعات\" کانال خصوصی ظاهر می شود.", "edit_channel_purpose_modal.body": "نحوه استفاده از این کانال را توضیح دهید. این متن در فهرست کانال در منوی \"بیشتر...\" ظاهر می شود و به دیگران کمک می کند تصمیم بگیرند که آیا بپیوندند یا خیر.", "edit_channel_purpose_modal.cancel": "انصراف", "edit_channel_purpose_modal.save": "صرفه جویی", "edit_channel_purpose_modal.title1": "هد<PERSON> ویرایش", "edit_channel_purpose_modal.title2": "ویرایش هدف برای ", "edit_command.update": "به روز رسانی", "edit_command.updating": "در حال بروز رسانی...", "edit_post.action_buttons.cancel": "لغو", "edit_post.action_buttons.save": "ذخیره", "edit_post.editPost": "ویرایش پست...", "edit_post.time_limit_button.for_n_seconds": "برای {n} ثانیه", "edit_post.time_limit_button.no_limit": "هر زمان", "edit_post.time_limit_modal.invalid_time_limit": "محدودیت زمانی نامعتبر است", "edit_post.time_limit_modal.option_label_anytime": "هر زمان", "edit_post.time_limit_modal.option_label_time_limit.postinput": "چند ثانیه پس از ارسال", "edit_post.time_limit_modal.option_label_time_limit.preinput": "می تواند برای", "edit_post.time_limit_modal.save_button": "صرفه جویی در زمان ویرایش", "edit_post.time_limit_modal.subscript": "مدت زمانی را تنظیم کنید که کاربران پس از ارسال پیام های خود را ویرایش کنند.", "edit_post.time_limit_modal.title": "محدودیت زمانی پست جهانی ویرایش را پیکربندی کنید", "email_verify.almost": "تقریباً کار شما تمام شده!", "email_verify.failed": "ارسال ایمیل تأییدیه با خطا مواجه شد.", "email_verify.notVerifiedBody": "لطفا آدرس ایمیل خود را تایید کنید. صندوق ورودی خود را برای دریافت ایمیل بررسی کنید.", "email_verify.resend": "ایمیل را دوباره ارسال کن", "email_verify.sending": "در حال ارسال رایانامه…", "email_verify.sent": "ایمیل تاییدیه ارسال شد.", "emoji_list.actions": "اقدامات", "emoji_list.add": "ایموجی سفارشی اضافه کنید", "emoji_list.creator": "ایج<PERSON> کننده", "emoji_list.delete": "<PERSON><PERSON><PERSON>", "emoji_list.delete.confirm.button": "<PERSON><PERSON><PERSON>", "emoji_list.delete.confirm.msg": "این عمل ایموجی سفارشی را برای همیشه حذف می کند. آیا مطمئن هستید که می خواهید آن را حذف کنید؟", "emoji_list.delete.confirm.title": "ایموجی سفارشی را حذف کنید", "emoji_list.empty": "ایموجی سفارشی یافت نشد", "emoji_list.header": "ایموجی سفارشی", "emoji_list.help": "ایموجی های سفارشی برای همه در سرور شما در دسترس است. برای نمایش منوی انتخاب ایموجی، «:» را تایپ کنید و به دنبال آن دو کاراکتر را در کادر پیام وارد کنید.", "emoji_list.help2": "نکته: اگر #، ##، یا ### را به عنوان اولین کاراکتر در یک خط جدید حاوی شکلک اضافه کنید، می‌توانید از شکلک‌هایی با اندازه بزرگتر استفاده کنید. برای امتحان کردن، پیامی مانند: '# :smile:' ارسال کنید.", "emoji_list.image": "تصویر", "emoji_list.name": "نام", "emoji_list.search": "<PERSON><PERSON><PERSON> سفارشی را جستجو کنید", "emoji_picker.activities": "فعالیت ها", "emoji_picker.animals-nature": "حیوانات و طبیعت", "emoji_picker.close": "نزدیک", "emoji_picker.custom": "سفار<PERSON>ی", "emoji_picker.custom_emoji": "ایموجی سفارشی", "emoji_picker.flags": "پرچم ها", "emoji_picker.food-drink": "غذا و نوشیدنی", "emoji_picker.header": "انتخاب کننده شکلک", "emoji_picker.objects": "اشیاء", "emoji_picker.people-body": "مردم و بدن", "emoji_picker.recent": "اخیرا مورد استفاده قرار گرفته", "emoji_picker.search": "جستجوی ایموجی ها", "emoji_picker.searchResults": "نتایج جستجو", "emoji_picker.search_emoji": "جستجو برای ایموجی", "emoji_picker.skin_tone": "رنگ پوست", "emoji_picker.smileys-emotion": "شکلک ها و احساسات", "emoji_picker.symbols": "نمادها", "emoji_picker.travel-places": "مکان های سفر", "emoji_picker_item.emoji_aria_label": "شکلک {emojiName}", "emoji_skin.dark_skin_tone": "رنگ پوست تیره", "emoji_skin.default": "رنگ پوست پیش فرض", "emoji_skin.light_skin_tone": "رنگ پوست روشن", "emoji_skin.medium_dark_skin_tone": "رنگ پوست متوسط تیره", "emoji_skin.medium_light_skin_tone": "رنگ پوست روشن متوسط", "emoji_skin.medium_skin_tone": "رنگ پوست متوسط", "emoji_skin_item.emoji_aria_label": "ایموجی {skinName}", "error.channelNotFound.guest_link": "بازگشت", "error.channelNotFound.link": "بازگشت به {defaultChannelName}", "error.channel_not_found.message": "کانالی که درخواست می‌کنید خصوصی است یا وجود ندارد. لطفا برای اضافه شدن به کانال با یک مدیر تماس بگیرید.", "error.channel_not_found.message_guest": "حساب مهمان شما هیچ کانالی اختصاص داده نشده است. لطفا با یک مدیر تماس بگیرید.", "error.channel_not_found.title": "کانال پیدا نشد", "error.generic.link": "بازگشت به {siteName}", "error.generic.link_login": "بازگشت به صفحه ورود", "error.generic.message": "خطایی رخ داده است.", "error.generic.title": "خطا", "error.local_storage.help1": "فعال کردن کوکی ها", "error.local_storage.help2": "مرور خصوصی را خاموش کنید", "error.local_storage.help3": "از مرورگر پشتیبانی شده استفاده کنید (IE 11، Chrome 61+، Firefox 60+، Safari 12+، Edge 42+)", "error.local_storage.message": "Mattermost بارگیری نشد زیرا تنظیمی در مرورگر شما از استفاده از ویژگی‌های ذخیره‌سازی محلی آن جلوگیری می‌کند. برای اجازه دادن به Mattermost برای بارگیری، اقدامات زیر را امتحان کنید:", "error.local_storage.title": "بارگیری Mattermost امکان پذیر نیست", "error.not_found.message": "صفحه ای که می خواستید به آن برسید وجود ندارد", "error.not_found.title": "صفحه یافت نشد", "error.oauth_access_denied": "شما باید به Mattermost اجازه دهید تا با {service} وارد شوید.", "error.oauth_access_denied.title": "خطای مجوز", "error.oauth_invalid_param.title": "خطای پارامتر OAuth", "error.oauth_missing_code": "ارائه‌دهنده خدمات {service} کد مجوزی را در URL تغییر مسیر ارائه نکرده است.", "error.oauth_missing_code.forum": "اگر موارد بالا را مرور کردید و همچنان با پیکربندی مشکل دارید، می‌توانید در {link} ما پست کنید، جایی که ما خوشحال می‌شویم در مورد مشکلات در حین راه‌اندازی به شما کمک کنیم.", "error.oauth_missing_code.forum.link": "انجمن عیب یابی", "error.oauth_missing_code.gitlab": "برای {link} لطفاً مطمئن شوید که دستورالعمل‌های راه‌اندازی را دنبال کرده‌اید.", "error.oauth_missing_code.gitlab.link": "GitLab", "error.oauth_missing_code.google": "برای {link} مطمئن شوید که سرپرست شما Google+ API را فعال کرده است.", "error.oauth_missing_code.google.link": "Google Apps", "error.oauth_missing_code.office365": "برای {link} مطمئن شوید که سرپرست سازمان Microsoft شما برنامه Mattermost را فعال کرده است.", "error.oauth_missing_code.office365.link": "دفتر 365", "error.oauth_missing_code.title": "Mattermost به کمک شما نیاز دارد", "error.plugin_not_found.desc": "افزونه ای که به دنبال آن هستید وجود ندارد.", "error.plugin_not_found.title": "افزونه یافت نشد", "error.team_not_found.message": "تیمی که درخواست می‌کنید خصوصی است یا وجود ندارد. لطفاً برای دعوت نامه با مدیر خود تماس بگیرید.", "error.team_not_found.title": "تیم پیدا نشد", "error_modal.try_again": "تلاش دوباره", "feature_restricted_modal.button.notify": "اطلاع به سرپرست", "feature_restricted_modal.button.plans": "مشاهده طرح‌ها", "feedback.cancelButton.text": "لغو", "feedback.downgradeWorkspace.downgrade": "تنزل", "feedback.downgradeWorkspace.exploringOptions": "کاوش دیگر راهکارها", "feedback.downgradeWorkspace.feedbackTitle": "لطفا دلیل خود برای تنزل دادن طرح را بنویسید", "feedback.downgradeWorkspace.noLongerNeeded": "دیگر نیازی به ویژگی‌های حرفه‌ای ابری ندارم", "feedback.downgradeWorkspace.tellUsWhy": "لطفا دلیلتان برای تنزل طرح را به ما بگویید", "feedback.downgradeWorkspace.tooExpensive": "<PERSON>ی<PERSON>ی گران قیمت", "feedback.other": "سایر", "file_attachment.thumbnail": "تصویر کوچک فایل", "file_info_preview.size": "اندازه ", "file_info_preview.type": "نوع فایل ", "file_preview_modal_info.shared_in": "به اشتراک گذاشته شده در ~{name}", "file_preview_modal_main_actions.public_link-copied": "پیوند عمومی کپی شد", "file_preview_modal_main_nav.file": "{count, number} از {total, number}", "file_search_result_item.copy_link": "لینک را کپی کنید", "file_search_result_item.download": "د<PERSON><PERSON><PERSON>د", "file_search_result_item.more_actions": "اقدامات بیشتر", "file_search_result_item.open_in_channel": "در کانال باز کنید", "file_upload.disabled": "پیوست فایل غیرفعال است.", "file_upload.drag_folder": "پوشه ها را نمی توان آپلود کرد. لطفا همه فایل ها را جداگانه بکشید.", "file_upload.fileAbove": "فایل بالای {max} مگابایت بارگذاری نمی‌شود: {filename}", "file_upload.filesAbove": "فایل‌های بالاتر از {max} مگابایت را نمی‌توان آپلود کرد: {filnames}", "file_upload.generic_error": "مشکلی در آپلود فایل های شما وجود داشت.", "file_upload.limited": "آپلودها حداکثر به {count, number} فایل محدود می‌شوند. لطفا برای فایل های بیشتر از پست های اضافی استفاده کنید.", "file_upload.menuAriaLabel": "انتخابگر نوع آپلود", "file_upload.pasted": "تصویر جایگذاری شده در ", "file_upload.upload_files": "فایل ها را آپلود کنید", "file_upload.zeroBytesFile": "شما در حال آپلود یک فایل خالی هستید: {filename}", "file_upload.zeroBytesFiles": "شما در حال آپلود فایل های خالی هستید: {filnames}", "filtered_channels_list.search": "جستجو در کانال ها", "filtered_user_list.countTotal": "{count, number} {count, plural, one {member} other { Members}} از {total, number} کل", "filtered_user_list.countTotalPage": "{startCount, number} - {endCount, number} {count, plural, one {member} other {members}} از مجموع {total, number}", "filtered_user_list.next": "بعد", "filtered_user_list.prev": "قب<PERSON>ی", "filtered_user_list.search": "جستجوی کاربران", "flag_post.flag": "صرفه جویی", "flag_post.unflag": "حذ<PERSON> از ذخیره شده", "forward_post_button.label": "جلو", "forward_post_modal.button.cancel": "لغو", "forward_post_modal.button.forward": "جلو", "forward_post_modal.preview.title": "پیش‌نمایش پیام", "forward_post_modal.title": "بازارسال پیام", "free.professional_feature.back": "<PERSON><PERSON><PERSON>", "free.professional_feature.professional": "ویژگی حرفه‌ای", "free.professional_feature.upgrade": "ارتقا", "full_screen_modal.back": "بازگشت", "full_screen_modal.close": "نزدیک", "general_button.close": "نزدیک", "general_button.esc": "خروج", "general_tab.AllowedDomainsExample": "corp.mattermost.com، mattermost.com", "general_tab.AllowedDomainsInfo": "کاربران تنها در صورتی می توانند به تیم ملحق شوند که ایمیل آنها با یک دامنه خاص (مانند \"mattermost.com\") یا فهرست دامنه های جدا شده با کاما (مانند \"corp.mattermost.com، mattermost.com\") مطابقت داشته باشد.", "general_tab.allowedDomains": "فقط به کاربران با دامنه ایمیل خاص اجازه دهید به این تیم بپیوندند", "general_tab.allowedDomains.ariaLabel": "دامنه های مجاز", "general_tab.codeLongDesc": "کد دعوت بخشی از پیوند دعوت تیم منحصر به فرد است که برای اعضایی که شما به این تیم دعوت می کنید ارسال می شود. ایجاد مجدد کد یک پیوند دعوت جدید ایجاد می کند و پیوند قبلی را باطل می کند.", "general_tab.codeTitle": "کد دعوت", "general_tab.openInviteDesc": "در صورت مجاز بودن، پیوندی به این تیم در صفحه فرود گنجانده می شود که به هر کسی که حساب کاربری دارد اجازه می دهد به این تیم بپیوندد. تغییر از \"بله\" به \"خیر\" باعث ایجاد مجدد کد دعوت، ایجاد پیوند دعوت جدید و بی اعتبار شدن پیوند قبلی می شود.", "general_tab.openInviteTitle": "به هر کاربری با حساب کاربری در این سرور اجازه دهید به این تیم بپیوندد", "general_tab.regenerate": "بازسازی کنید", "general_tab.required": "این فیلد الزامی است", "general_tab.teamDescription": "توضیحات تیم", "general_tab.teamDescriptionInfo": "توضیحات تیم اطلاعات بیشتری را برای کمک به کاربران در انتخاب تیم مناسب ارائه می دهد. حداکثر 50 کاراکتر", "general_tab.teamIconError": "هنگام انتخاب تصویر خطایی روی داد.", "general_tab.teamIconInvalidFileType": "فقط می توان از تصاویر BMP، JPG یا PNG برای نمادهای تیم استفاده کرد", "general_tab.teamIconTooLarge": "نماد تیم بارگذاری نمی شود. پرونده خیلی بزرگ است.", "general_tab.teamName": "نام گروه", "general_tab.teamNameInfo": "نام تیم را همانطور که در صفحه ورود به سیستم و در بالای نوار کناری سمت چپ نشان داده می شود، تنظیم کنید.", "general_tab.teamNameRestrictions": "نام تیم باید {min} یا بیشتر تا حداکثر {حداکثر} نویسه باشد. می‌توانید توضیحات تیمی طولانی‌تری اضافه کنید.", "generic.done": "انجام شده", "generic.next": "بعد", "generic.previous": "قب<PERSON>ی", "generic_icons.add": "اضافه کردن نماد", "generic_icons.add-mail": "اضافه کردن نماد ایمیل", "generic_icons.add-reaction": "اضافه کردن نماد واکنش", "generic_icons.adminOnlyIcon": "نماد فقط مشاهده مدیر", "generic_icons.alert": "نماد هشدار", "generic_icons.archive": "نماد آرشیو", "generic_icons.arrow.down": "نماد فلش رو به پایین", "generic_icons.attach": "نماد پیوست", "generic_icons.back": "نماد برگشت", "generic_icons.breadcrumb": "نماد خرده نان", "generic_icons.call": "تصویرک تماس", "generic_icons.channel.private": "نماد کانال خصوصی", "generic_icons.channel.public": "نماد کانال عمومی", "generic_icons.close": "بستن نماد", "generic_icons.collapse": "نماد جمع کردن", "generic_icons.dropdown": "نماد کشویی", "generic_icons.edit": "ویرایش نماد", "generic_icons.elipsisHorizontalIcon": "نماد افقی بیضی", "generic_icons.expand": "نماد را گسترش دهید", "generic_icons.flag": "نماد ذخیره", "generic_icons.flagged": "نماد ذخیره شده", "generic_icons.info": "نماد اطلاعات", "generic_icons.loading": "نماد بارگیری", "generic_icons.login.gitlab": "تصویرک Gitlab", "generic_icons.login.google": "تصویرک Google", "generic_icons.login.openid": "تصویرک OpenID", "generic_icons.logout": "نماد خروج", "generic_icons.mail": "نماد ایمیل", "generic_icons.mattermost": "نشان‌واره Mattermost", "generic_icons.member": "نماد عضو", "generic_icons.mention": "ذکر نماد", "generic_icons.menu": "نماد منو", "generic_icons.muted": "نماد بی‌صدا", "generic_icons.next": "نماد بعدی", "generic_icons.pin": "نماد پین", "generic_icons.plugin": "نماد پلاگین", "generic_icons.plugins": "پلاگین ها", "generic_icons.preview": "تصویرک چشم", "generic_icons.previous": "نم<PERSON> قبلی", "generic_icons.reload": "نماد بارگیری مجدد", "generic_icons.reply": "نماد پاسخ", "generic_icons.search": "نماد جستجو", "generic_icons.success": "نماد موفقیت", "generic_icons.upgradeBadge": "نشان ارتقا", "generic_icons.upload": "نماد آپلود", "generic_icons.userGuide": "کمک", "generic_icons.warning": "نماد هشدار", "generic_modal.cancel": "انصراف", "generic_modal.confirm": "تا<PERSON><PERSON>د", "get_app.continueToBrowser": "مشاهده در مرورگر", "get_app.dontHaveTheDesktopApp": "برنامه دسکتاپ ندارید؟", "get_app.dontHaveTheMobileApp": "اپلیکیشن موبایل را ندارید؟", "get_app.downloadTheAppNow": "هم اکنون برنامه را دانلود کنید.", "get_app.ifNothingPrompts": "می‌توانید {siteName} را در برنامه دسک‌تاپ مشاهده کنید یا در مرورگر وب خود ادامه دهید.", "get_app.ifNothingPromptsMobile": "می‌توانید {siteName} را در برنامه تلفن همراه مشاهده کنید یا در مرورگر وب خود ادامه دهید.", "get_app.launching": "دوست دارید این را کجا ببینید؟", "get_app.openingLink": "باز کردن لینک در Mattermost...", "get_app.openingLinkWhiteLabel": "در حال باز کردن پیوند در {appName}...", "get_app.redirectedInMoments": "چند لحظه دیگر هدایت می شوید.", "get_app.rememberMyPreference": "ترجیح من را به خاطر بسپار", "get_app.systemDialogMessage": "در برنامه دسکتاپ مشاهده کنید", "get_app.systemDialogMessageMobile": "مشاهده در برنامه", "get_link.clipboard": " لینک کپی شد", "get_link.close": "نزدیک", "get_link.copy": "لینک را کپی کنید", "get_public_link_modal.help": "لینک زیر به هر کسی اجازه می دهد تا بدون ثبت نام در این سرور، این فایل را ببیند.", "get_public_link_modal.title": "لینک عمومی را کپی کنید", "globalThreads.heading": "موضوعات دنبال شده", "globalThreads.noThreads.subtitle": "هر رشته ای که در آن نام برده شده یا در آن شرکت کرده اید همراه با هر رشته ای که دنبال کرده اید در اینجا نشان داده می شود.", "globalThreads.noThreads.title": "هنوز هیچ موضوعی دنبال نشده است", "globalThreads.sidebarLink": "موضوعات", "globalThreads.threadList.noUnreadThreads": "بدون رشته خوانده نشده", "globalThreads.threadPane.unreadMessageLink": "شما {numUnread, plural, =0 {بدون رشته خوانده نشده} =1 {<link>{numUnread} رشته</link>} {<link>{numUnread} موضوع</link>}} {numUnread, plural, = دارید 0 {} دیگر {با پیام های خوانده نشده}}", "globalThreads.threadPane.unselectedTitle": "{numUnread, plural, =0 {به نظر می‌رسد همه شما درگیر هستید} دیگر {Catch up on your threads}}", "globalThreads.title": "{prefix}موضوعات - {displayName} {siteName}", "global_header.productSettings": "تنظیمات", "group_list_modal.addGroupButton": "افزودن گروه ها", "group_list_modal.removeGroupButton": "حذ<PERSON> گروه", "group_member_list.retryLoadButton": "تلاش دوباره", "group_member_list.sendMessageTooltip": "ارسال پیام", "inProduct_notices.adminOnlyMessage": "فقط برای مدیران قابل مشاهده است", "input.clear": "پاک کردن", "installed_command.header": "دستورات اسلش", "installed_commands.add": "دستور اسلش را اضافه کنید", "installed_commands.delete.confirm": "این عمل دستور اسلش را برای همیشه حذف می کند و هر ادغام با استفاده از آن را می شکند. آیا مطمئن هستید که می خواهید آن را حذف کنید؟", "installed_commands.empty": "هیچ فرمانی یافت نشد", "installed_commands.header": "دستورات اسلش", "installed_commands.help": "از دستورات اسلش برای اتصال ابزارهای خارجی به Mattermost استفاده کنید. {buildYourOwn} یا از {appDirectory} دیدن کنید تا برنامه‌ها و ادغام‌های خود میزبان، شخص ثالث را بیابید.", "installed_commands.help.appDirectory": "فهرست برنامه ها", "installed_commands.help.buildYourOwn": "خود<PERSON>ان را بسازید", "installed_commands.search": "جستجوی دستورات اسلش", "installed_commands.unnamed_command": "دستور اسلش بدون نام", "installed_incoming_webhooks.add": "اضافه کردن وب هوک ورودی", "installed_incoming_webhooks.delete.confirm": "این عمل به طور دائم وب هوک ورودی را حذف می کند و هرگونه ادغام با استفاده از آن را خراب می کند. آیا مطمئن هستید که می خواهید آن را حذف کنید؟", "installed_incoming_webhooks.empty": "هیچ وب هوک ورودی پیدا نشد", "installed_incoming_webhooks.emptySearch": "هیچ وب‌قلاب ورودی مطابقت ندارد **{searchTerm}**", "installed_incoming_webhooks.header": "وب هوک های ورودی", "installed_incoming_webhooks.help": "از وب هوک های ورودی برای اتصال ابزارهای خارجی به Mattermost استفاده کنید. {buildYourOwn} یا از {appDirectory} دیدن کنید تا برنامه‌ها و ادغام‌های خود میزبان، شخص ثالث را بیابید.", "installed_incoming_webhooks.help.appDirectory": "فهرست برنامه ها", "installed_incoming_webhooks.help.buildYourOwn": "خود<PERSON>ان را بسازید", "installed_incoming_webhooks.search": "جستجوی وب هوک های ورودی", "installed_incoming_webhooks.unknown_channel": "یک وب هوک خصوصی", "installed_integrations.callback_urls": "نشانی‌های اینترنتی پاسخ به تماس: {urls}", "installed_integrations.content_type": "نوع محتوا: {contentType}", "installed_integrations.creation": "ایجاد شده توسط {creator} در {createAt, date, full}", "installed_integrations.delete": "<PERSON><PERSON><PERSON>", "installed_integrations.edit": "ویرایش کنید", "installed_integrations.fromApp": "مدیریت شده توسط Apps Framework", "installed_integrations.hideSecret": "پنهان کردن راز", "installed_integrations.regenSecret": "راز را بازسازی کنید", "installed_integrations.regenToken": "Regenerate Token", "installed_integrations.showSecret": "نمایش راز", "installed_integrations.token": "نشانه: {token}", "installed_integrations.triggerWhen": "Trigger When: {trigger<PERSON>hen}", "installed_integrations.triggerWords": "کلمات محرک: {triggerWords}", "installed_integrations.unnamed_oauth_app": "برنا<PERSON>ه OAuth 2.0 بدون نام", "installed_integrations.url": "URL: {url}", "installed_oauth_apps.add": "برنا<PERSON>ه OAuth 2.0 را اضافه کنید", "installed_oauth_apps.callbackUrls": "URL های برگشت به تماس (یکی در هر خط)", "installed_oauth_apps.cancel": "انصراف", "installed_oauth_apps.delete.confirm": "این عمل به طور دائم برنامه OAuth 2.0 را حذف می کند و هرگونه ادغام با استفاده از آن را خراب می کند. آیا مطمئن هستید که می خواهید آن را حذف کنید؟", "installed_oauth_apps.description": "شرح", "installed_oauth_apps.empty": "هیچ برنامه OAuth 2.0 یافت نشد", "installed_oauth_apps.emptySearch": "هیچ برنامه OAuth 2.0 مطابقت ندارد **{searchTerm}**", "installed_oauth_apps.header": "برنامه های OAuth 2.0", "installed_oauth_apps.help": "برای ادغام ایمن ربات ها و برنامه های شخص ثالث با Mattermost، {oauthApplications} را ایجاد کنید. از {appDirectory} دیدن کنید تا برنامه های خود میزبانی شده را پیدا کنید.", "installed_oauth_apps.help.appDirectory": "فهرست برنامه ها", "installed_oauth_apps.help.oauthApplications": "برنامه های OAuth 2.0", "installed_oauth_apps.homepage": "صفحه نخست", "installed_oauth_apps.iconUrl": "نشانی وب نماد", "installed_oauth_apps.name": "نام نمایشی", "installed_oauth_apps.save": "صرفه جویی", "installed_oauth_apps.saving": "صرفه جویی در...", "installed_oauth_apps.search": "برنامه های OAuth 2.0 را جستجو کنید", "installed_oauth_apps.trusted": "مورد اعتماد است", "installed_oauth_apps.trusted.no": "<PERSON><PERSON><PERSON>", "installed_oauth_apps.trusted.yes": "آره", "installed_outgoing_webhooks.add": "اضافه کردن وب هوک خروجی", "installed_outgoing_webhooks.delete.confirm": "این عمل به طور دائم وب هوک خروجی را حذف می کند و هرگونه ادغام با استفاده از آن را خراب می کند. آیا مطمئن هستید که می خواهید آن را حذف کنید؟", "installed_outgoing_webhooks.empty": "هیچ وب‌قلاب خروجی یافت نشد", "installed_outgoing_webhooks.header": "وب هوک های خروجی", "installed_outgoing_webhooks.help": "از وب هوک های خروجی برای اتصال ابزارهای خارجی به Mattermost استفاده کنید. {buildYourOwn} یا از {appDirectory} دیدن کنید تا برنامه‌ها و ادغام‌های خود میزبان، شخص ثالث را بیابید.", "installed_outgoing_webhooks.help.appDirectory": "فهرست برنامه ها", "installed_outgoing_webhooks.help.buildYourOwn": "خود<PERSON>ان را بسازید", "installed_outgoing_webhooks.search": "جستجوی وب هوک های خروجی", "installed_outgoing_webhooks.unknown_channel": "یک وب هوک خصوصی", "integrations.add": "اضافه کردن", "integrations.command.description": "دستورات اسلش رویدادها را به ادغام های خارجی ارسال می کند", "integrations.command.title": "دستورات اسلش", "integrations.delete.confirm.button": "<PERSON><PERSON><PERSON>", "integrations.delete.confirm.title": "ادغام را حذف کنید", "integrations.done": "انجام شده", "integrations.edit": "ویرایش کنید", "integrations.header": "ادغام ها", "integrations.help": "از {appDirectory} دیدن کنید تا برنامه‌ها و برنامه‌های شخص ثالث خود میزبانی شده و ادغام‌های Mattermost را بیابید.", "integrations.help.appDirectory": "فهرست برنامه ها", "integrations.incomingWebhook.description": "وب هوک های ورودی به ادغام های خارجی امکان ارسال پیام را می دهند", "integrations.incomingWebhook.title": "وب هوک های ورودی", "integrations.oauthApps.description": "OAuth 2.0 به برنامه های خارجی اجازه می دهد تا درخواست های مجاز را به Mattermost API ارسال کنند", "integrations.oauthApps.title": "برنامه های OAuth 2.0", "integrations.outgoingWebhook.description": "وب هوک های خروجی به ادغام های خارجی امکان دریافت و پاسخ به پیام ها را می دهند", "integrations.outgoingWebhook.title": "وب هوک های خروجی", "integrations.successful": "راه اندازی با موفقیت انجام شد", "interactive_dialog.cancel": "انصراف", "interactive_dialog.element.optional": "(اختیاری)", "interactive_dialog.submit": "ارسال", "interactive_dialog.submitting": "در حال ارسال...", "intro_messages.DM": "این شروع تاریخچه پیام مستقیم شما با {teammate} است.\nپیام‌های مستقیم و فایل‌های اشتراک‌گذاری شده در اینجا به افراد خارج از این منطقه نشان داده نمی‌شوند.", "intro_messages.addGroupsToTeam": "گروه های دیگر را به این تیم اضافه کنید", "intro_messages.anyMember": " هر عضوی می تواند به این کانال بپیوندد و بخواند.", "intro_messages.creator": "این شروع کانال {name} است که توسط {creator} در {date} ایجاد شده است.", "intro_messages.creatorPrivate": "این شروع کانال خصوصی {name} است که توسط {creator} در {date} ایجاد شده است.", "intro_messages.default": "**به {display_name} خوش آمدید!**\n \nپیام‌هایی را در اینجا پست کنید که می‌خواهید همه آن‌ها را ببینند. همه افراد با پیوستن به تیم به طور خودکار عضو دائمی این کانال می شوند.", "intro_messages.group_message": "این شروع تاریخچه پیام های گروهی شما با این هم تیمی ها است. پیام ها و فایل های به اشتراک گذاشته شده در اینجا به افراد خارج از این منطقه نشان داده نمی شود.", "intro_messages.inviteGropusToChannel.button": "گروه ها را به این کانال خصوصی اضافه کنید", "intro_messages.inviteMembersToChannel.button": "اضافه کردن اعضا به این کانال", "intro_messages.inviteOthers": "دیگران را به این تیم دعوت کنید", "intro_messages.inviteOthersToWorkspace.button": "دیگران را به فضای کاری دعوت کنید", "intro_messages.noCreator": "این شروع کانال {name} است که در {date} ایجاد شده است.", "intro_messages.noCreatorPrivate": "این شروع کانال خصوصی {name} است که در {date} ایجاد شده است.", "intro_messages.offTopic": "این شروع {display_name} است، کانالی برای مکالمات غیر مرتبط با کار.", "intro_messages.onlyInvited": " فقط اعضای دعوت شده می توانند این کانال خصوصی را ببینند.", "intro_messages.purpose": " هدف این کانال این است: {purpose}", "intro_messages.readonly.default": "**به {display_name} خوش آمدید!**\n \nپیام ها فقط توسط مدیران سیستم ارسال می شود. همه افراد با پیوستن به تیم به طور خودکار عضو دائمی این کانال می شوند.", "intro_messages.setHeader": "یک هدر تنظیم کنید", "intro_messages.teammate": "این شروع تاریخچه پیام مستقیم شما با این هم تیمی است. پیام‌های مستقیم و فایل‌های اشتراک‌گذاری شده در اینجا به افراد خارج از این منطقه نشان داده نمی‌شوند.", "invitation-modal.confirm.details-header": "جزئیات", "invitation-modal.confirm.not-valid-channel": "با نام کانال معتبر مطابقت ندارد.", "invitation-modal.confirm.not-valid-user-or-email": "با کاربر یا ایمیل معتبر مطابقت ندارد.", "invitation-modal.confirm.people-header": "مردم", "invitation_modal.confirm.done": "انجام شده", "invitation_modal.confirm.not-sent-header": "دعوت نامه ها ارسال نشد", "invitation_modal.confirm.sent-header": "دعوت های موفق", "invitation_modal.guests.add_channels.title": "جستجو و افزودن کانال", "invitation_modal.guests.custom-message.link": "یک پیام سفارشی تنظیم کنید", "invitation_modal.guests.custom-message.title": "پیام سفارشی", "invitation_modal.guests.users_emails_input.valid_email": "**{email}** را به عنوان مهمان دعوت کنید", "invitation_modal.invite.more": "افراد بیشتری را دعوت کنید", "invitation_modal.invite_members.exceeded_max_add_members_batch": "بیش از **{text}** نمی‌توان همزمان دعوت کرد", "invitation_modal.members.search-and-add.placeholder-email-disabled": "اضافه کردن اعضا", "invitation_modal.members.search_and_add.title": "افراد را اضافه یا دعوت کنید", "invitation_modal.members.users_emails_input.no_user_found_matching": "هیچکس مطابق **{text}** پیدا نشد. برای دعوت از آنها ایمیل آنها را وارد کنید.", "invitation_modal.members.users_emails_input.no_user_found_matching-email-disabled": "هیچکس مطابق **{text}** پیدا نشد", "invitation_modal.members.users_emails_input.valid_email": "**{email}** را به عنوان عضو تیم دعوت کنید", "invite.guests.added-to-channel": "یک ایمیل دعوت نامه ارسال شده است.", "invite.guests.already-all-channels-member": "این شخص قبلاً در همه کانال ها عضو است.", "invite.guests.already-some-channels-member": "این شخص قبلاً عضو برخی از کانال ها است.", "invite.guests.new-member": "این مهمان به تیم و {count, plural, one {channel} other {channels}} اضافه شده است.", "invite.guests.unable-to-add-the-user-to-the-channels": "نمی‌توان مهمان را به کانال‌ها اضافه کرد.", "invite.members.added-to-team": "این عضو به تیم اضافه شد", "invite.members.already-member": "این شخص قبلاً یکی از اعضای تیم است.", "invite.members.invite-sent": "یک ایمیل دعوت نامه ارسال شده است.", "invite.members.unable-to-add-the-user-to-the-team": "امکان افزودن کاربر به تیم وجود ندارد.", "invite.members.user-is-guest": "برای عضویت کامل این مهمان با ادمین خود تماس بگیرید.", "invite.members.user-is-not-guest": "این شخص قبلاً عضو است.", "invite.rate-limit-exceeded": "از محدودیت نرخ ایمیل دعوت فراتر رفت.", "invite_modal.add_channels_title_a": "به کانال ها اضافه کنید", "invite_modal.add_invites": "یک نام یا آدرس ایمیل وارد کنید", "invite_modal.as": "دعوت به عنوان", "invite_modal.choose_guest_a": "مه<PERSON>ان", "invite_modal.choose_guest_b": "محدود به کانال ها و تیم های منتخب است", "invite_modal.choose_member": "عضو", "invite_modal.copied": "کپی شد", "invite_modal.copy_link": "پیوند دعوت را کپی کنید", "invite_modal.example_channel": "به عنوان مثال، {نام کانال}", "invite_modal.guests": "میه<PERSON>انان", "invite_modal.invite": "دعوت", "invite_modal.invited": "{inviteType} به {team_name} دعوت شد", "invite_modal.invited_guests": "میه<PERSON>انان", "invite_modal.invited_members": "اعضا", "invite_modal.no_permissions.description": "شما مجوز اضافه کردن کاربر یا مهمان را ندارید. اگر این خطا به نظر می رسد، لطفاً با سرپرست سیستم خود تماس بگیرید.", "invite_modal.no_permissions.title": "نمی‌توان ادامه داد", "invite_modal.people": "افراد", "invite_modal.title": "دعوت از {inviteType} به {team_name}", "invite_modal.to": "به:", "joinChannel.JoinButton": "پیوستن", "joinChannel.joiningButton": "در حال پیوستن...", "join_team_group_constrained_denied": "برای پیوستن به این تیم باید عضو یک گروه مرتبط باشید.", "katex.error": "کد لاتکس شما کامپایل نشد. لطفاً نحو را بررسی کرده و دوباره امتحان کنید.", "last_users_message.added_to_channel.type": "توسط {actor}**به کانال** اضافه شد.", "last_users_message.added_to_team.type": "توسط {actor} **به تیم** اضافه شدند.", "last_users_message.first": "{firstUser} و ", "last_users_message.joined_channel.type": "**به کانال پیوست**.", "last_users_message.joined_team.type": "**به تیم پیوست**.", "last_users_message.left_channel.type": "**از کانال خارج شد**.", "last_users_message.left_team.type": "**تیم را ترک کرد**.", "last_users_message.others": "{numOthers} دیگر ", "last_users_message.removed_from_channel.type": "**از کانال حذف شدند**.", "last_users_message.removed_from_team.type": "**از تیم حذف شدند**.", "leave_private_channel_modal.leave": "بله کانال رو ترک کن", "leave_private_channel_modal.message": "آیا مطمئنید که می خواهید از کانال خصوصی {channel} خارج شوید؟ برای پیوستن مجدد به این کانال در آینده باید مجدداً دعوت شوید.", "leave_private_channel_modal.title": "خروج از کانال خصوصی {channel}", "leave_team_modal.no": "<PERSON><PERSON><PERSON>", "leave_team_modal.title": "ترک تیم؟", "leave_team_modal.yes": "آره", "licensingPage.infoBanner.startTrialTitle": "30 روز آزمایشی رایگان!", "licensingPage.overageUsersBanner.cta": "تماس با فروشندگان", "list_modal.paginatorCount": "{startCount, number} - {endCount, number} از مجموع {total, number}", "loading_screen.loading": "بارگذاری", "local": "<PERSON><PERSON><PERSON><PERSON>", "login.cardtitle": "ورود", "login.changed": " روش ورود به سیستم با موفقیت تغییر کرد", "login.contact_admin.title": "تماس با سرپرست سازمان خود", "login.createTeam": "یک تیم ایجاد کنید", "login.email": "ایمیل", "login.forgot": "فراموشی رمز عبور؟", "login.get_terms_error": "بارگیری شرایط استفاده ممکن نیست. اگر این مشکل ادامه داشت، با مدیر سیستم خود تماس بگیرید.", "login.gitlab": "GitLab", "login.google": "Google", "login.invalidPassword": "رمز عبور شما نادرست است.", "login.ldapCreate": " نام کاربری و رمز عبور AD/LDAP خود را برای ایجاد یک حساب کاربری وارد کنید.", "login.ldapUsername": "نام کاربری AD/LDAP", "login.ldapUsernameLower": "نام کاربری AD/LDAP", "login.logIn": "ورود", "login.logingIn": "در حال ورود…", "login.noAccount": "حساب کاربری ندارید؟", "login.noEmail": "لطفا ایمیل خود را وارد کنید", "login.noEmailLdapUsername": "لطفاً ایمیل یا {ldapUsername} خود را وارد کنید", "login.noEmailUsername": "لطفا ایمیل یا نام کاربری خود را وارد کنید", "login.noEmailUsernameLdapUsername": "لطفا ایمیل، نام کاربری یا {ldapUsername} خود را وارد کنید", "login.noLdapUsername": "لطفاً {ldapUsername} خود را وارد کنید", "login.noPassword": "لطفا رمز عبور خود را وارد کنید", "login.noUsername": "لطفا نام کاربری خود را وارد کنید", "login.noUsernameLdapUsername": "لطفا نام کاربری یا {ldapUsername} خود را وارد کنید", "login.office365": "دفتر 365", "login.openid": "Open ID", "login.or": "یا", "login.passwordChanged": " رمز عبور با موفقیت به روز شد", "login.placeholderOr": " یا ", "login.saml": "SAML", "login.session_expired": "جلسه شما تمام شده است. لطفا دوباره وارد شوید.", "login.session_expired.notification": "جلسه منقضی شد: لطفاً برای ادامه دریافت اعلان‌ها وارد شوید.", "login.session_expired.title": "* {siteName} - جلسه منقضی شده است", "login.terms_rejected": "قبل از دسترسی به {siteName} باید با شرایط استفاده موافقت کنید. لطفاً برای جزئیات بیشتر با مدیر سیستم خود تماس بگیرید.", "login.userNotFound": "ما نتوانستیم حسابی مطابق با اعتبار ورود شما پیدا کنیم.", "login.username": "نام کاربری", "login.verified": " ایمیل تأییده شده است", "login_mfa.saving": "در حال ورود…", "login_mfa.submit": "ارسال", "login_mfa.token": "توکن MFA", "manage_channel_groups_modal.search_placeholder": "جستجو در گروه ها", "manage_team_groups_modal.search_placeholder": "جستجو در گروه ها", "mark_all_threads_as_read_modal.cancel": "لغو", "marketplace_modal.install_plugins": "پلاگین ها را نصب کنید", "marketplace_modal.installing": "در حال نصب...", "marketplace_modal.list.configure": "پیکربندی کنید", "marketplace_modal.list.install": "نصب", "marketplace_modal.list.installed": "نصب شده است", "marketplace_modal.list.try_again": "دوباره امتحان کنید", "marketplace_modal.list.update": "به روز رسانی", "marketplace_modal.list.update_available": "به روز رسانی موجود است:", "marketplace_modal.list.update_confirmation.confirm_button": "به روز رسانی", "marketplace_modal.list.update_confirmation.message.current": "شما در حال حاضر {installedVersion} را نصب کرده اید.", "marketplace_modal.list.update_confirmation.message.current_with_release_notes": "شما در حال حاضر {installedVersion} را نصب کرده اید. [یادداشت‌های انتشار](!{releaseNotesUrl}) را مشاهده کنید تا از تغییرات موجود در این به‌روزرسانی مطلع شوید.", "marketplace_modal.list.update_confirmation.message.intro": "آیا مطمئن هستید که می خواهید افزونه {name} را به {version} به روز کنید؟", "marketplace_modal.list.update_confirmation.message.warning_major_version": "این به روز رسانی ممکن است حاوی تغییرات شکسته باشد.", "marketplace_modal.list.update_confirmation.message.warning_major_version_with_release_notes": "این به‌روزرسانی ممکن است حاوی تغییرات قطعی باشد. قبل از ارتقا، با [releaseNotes](!{releaseNotesUrl}) مشورت کنید.", "marketplace_modal.list.update_confirmation.title": "تایید به روز رسانی پلاگین", "marketplace_modal.no_plugins": "در حال حاضر هیچ پلاگینی موجود نیست.", "marketplace_modal.no_plugins_installed": "شما هیچ پلاگینی نصب نکرده اید.", "marketplace_modal.search": "جستجو در بازار", "marketplace_modal.tabs.all_listing": "همه", "marketplace_modal.tabs.installed_listing": "نصب شده است", "marketplace_modal.title": "بازار", "menu.cloudFree.enterpriseTrialTitle": "دوره آزمایشی تجاری", "message_submit_error.invalidCommand": "فرمان با ماشه ''{command}'' پیدا نشد. ", "message_submit_error.sendAsMessageLink": "برای ارسال به عنوان پیام اینجا را کلیک کنید.", "mfa.confirm.complete": "**تنظیم کامل شد!**", "mfa.confirm.okay": "باشه", "mfa.confirm.secure": "حساب شما اکنون امن است. دفعه بعد که وارد سیستم می شوید، از شما خواسته می شود کدی را از برنامه Google Authenticator در تلفن خود وارد کنید.", "mfa.setup.badCode": "کد نامعتبر. اگر این مشکل ادامه داشت، با مدیر سیستم خود تماس بگیرید.", "mfa.setup.code": "کد وزارت خارجه", "mfa.setup.codeError": "لطفا کد را از Google Authenticator وارد کنید.", "mfa.setup.save": "صرفه جویی", "mfa.setup.secret": "راز: {مخفی}", "mfa.setup.step1": "**مرحله 1: **در تلفن خود، Google Authenticator را از <linkiTunes>iTunes</linkiTunes> یا <linkGooglePlay>Google Play</linkGooglePlay> بارگیری کنید.", "mfa.setupTitle": "تنظیم احراز هویت چند عاملی", "mobile.set_status.away.icon": "نماد دور", "mobile.set_status.dnd.icon": "نماد مزاحم نشوید", "mobile.set_status.offline.icon": "نماد آفلاین", "mobile.set_status.online.icon": "آیکون آنلاین", "modal.manual_status.ask": "دوباره ازم نپرس", "modal.manual_status.auto_responder.message_away": "آیا می‌خواهید وضعیت خود را به «دور» تغییر دهید و پاسخ‌های خودکار را غیرفعال کنید؟", "modal.manual_status.auto_responder.message_dnd": "آیا می‌خواهید وضعیت خود را به «مزاحم نشوید» تغییر دهید و پاسخ‌های خودکار را غیرفعال کنید؟", "modal.manual_status.auto_responder.message_offline": "آیا می خواهید وضعیت خود را به \"آفلاین\" تغییر دهید و پاسخ های خودکار را غیرفعال کنید؟", "modal.manual_status.auto_responder.message_online": "آیا می خواهید وضعیت خود را به \"آنلاین\" تغییر دهید و پاسخ های خودکار را غیرفعال کنید؟", "modal.manual_status.button_away": "بله، وضعیت من را روی \"دور\" تنظیم کنید", "modal.manual_status.button_dnd": "بله، وضعیت من را روی \"مزاحم نشوید\" تنظیم کنید", "modal.manual_status.button_offline": "بله، وضعیت من را روی \"آفلاین\" تنظیم کنید", "modal.manual_status.button_online": "بله، وضعیت من را روی \"آنلاین\" تنظیم کنید", "modal.manual_status.cancel_away": "نه، آن را به عنوان \"دور\" نگه دارید", "modal.manual_status.cancel_dnd": "نه، آن را به عنوان «مزاحم نشوید» نگه دارید", "modal.manual_status.cancel_offline": "نه، آن را به عنوان \"آفلاین\" نگه دارید", "modal.manual_status.cancel_ooo": "نه، آن را به عنوان \"خارج از دفتر\" نگه دارید", "modal.manual_status.message_away": "آیا می خواهید وضعیت خود را به \"دور\" تغییر دهید؟", "modal.manual_status.message_dnd": "آیا می خواهید وضعیت خود را به \"مزاحم نشوید\" تغییر دهید؟", "modal.manual_status.message_offline": "آیا می خواهید وضعیت خود را به \"آفلاین\" تغییر دهید؟", "modal.manual_status.message_online": "آیا می خواهید وضعیت خود را به \"آنلاین\" تغییر دهید؟", "modal.manual_status.title_away": "وضعیت شما روی \"دور\" تنظیم شده است", "modal.manual_status.title_dnd": "وضعیت شما روی \"مزاحم نشوید\" تنظیم شده است", "modal.manual_status.title_offline": "وضعیت شما روی \"آفلاین\" تنظیم شده است", "modal.manual_status.title_ooo": "وضعیت شما روی \"خارج از دفتر\" تنظیم شده است", "more.details": "جزئیات بیشتر", "more_channels.create": "ایج<PERSON> کانال", "more_channels.next": "بعد", "more_channels.noMore": "کانال دیگری برای پیوستن وجود ندارد", "more_channels.prev": "قب<PERSON>ی", "more_channels.show_archived_channels": "نمایش: کانال های آرشیو شده", "more_channels.show_public_channels": "نمایش: کانال های عمومی", "more_channels.title": "کانال های بیشتر", "more_channels.view": "چشم انداز", "more_direct_channels.directchannel.deactivated": "{displayname} - غ<PERSON>ر<PERSON>عال شده است", "more_direct_channels.directchannel.you": "{displayname} (شما)", "more_direct_channels.new_convo_note": "این یک مکالمه جدید را آغاز می کند. اگر افراد زیادی را اضافه می کنید، به جای آن یک کانال خصوصی ایجاد کنید.", "more_direct_channels.new_convo_note.full": "شما به حداکثر تعداد افراد برای این مکالمه رسیده اید. به جای آن یک کانال خصوصی ایجاد کنید.", "more_direct_channels.title": "پیام مستقیم", "msg_typing.areTyping": "{users} و {last} در حال تایپ هستند...", "msg_typing.isTyping": "{user} در حال تایپ است...", "multiselect.add": "اضافه کردن", "multiselect.addChannelsPlaceholder": "جستجو و افزودن کانال", "multiselect.addGroupsPlaceholder": "جستجو و اضافه کردن گروه", "multiselect.addPeopleToGroup": "افزودن افراد", "multiselect.addTeamsPlaceholder": "جستجو و اضافه کردن تیم ها", "multiselect.adding": "اضافه كردن...", "multiselect.backButton": "<PERSON><PERSON><PERSON>", "multiselect.cancel": "لغو", "multiselect.cancelButton": "لغو", "multiselect.createGroup": "ایجاد گروه", "multiselect.creating": "در حال ایجاد...", "multiselect.go": "برو", "multiselect.list.notFound": "هیچ نتیجه ای مطابق با **{searchQuery}** یافت نشد", "multiselect.loading": "بارگذاری...", "multiselect.numGroupsRemaining": "از ↑↓ برای مرور، ↵ برای انتخاب استفاده کنید. می توانید {num, number} {num, plural, one {group} other {groups}} را اضافه کنید. ", "multiselect.numMembers": "{memberOptions, number} از {totalCount, number} اعضا", "multiselect.numPeopleRemaining": "از ↑↓ برای مرور، ↵ برای انتخاب استفاده کنید. می توانید {num, number} {num, plural, one {person} other {people}} را اضافه کنید. ", "multiselect.numRemaining": "حداکثر تا {حداکثر، تعداد} را می توان در هر زمان اضافه کرد. شما {num, number} باقی مانده است.", "multiselect.placeholder": "جستجو برای افراد", "multiselect.saveDetailsButton": "ذخ<PERSON>ره جزئیات", "multiselect.savingDetailsButton": "در حال ذخیره...", "multiselect.selectChannels": "از ↑↓ برای مرور، ↵ برای انتخاب استفاده کنید.", "multiselect.selectTeams": "از ↑↓ برای مرور، ↵ برای انتخاب استفاده کنید.", "navbar.addGroups": "افزودن گروه ها", "navbar.addMembers": "اضافه کردن عضو", "navbar.preferences": "تنظیمات اعلان", "navbar.toggle2": "نوار کناری را تغییر دهید", "navbar.viewPinnedPosts": "مشاهده پست های پین شده", "navbar_dropdown.about": "درباره {appTitle}", "navbar_dropdown.accountSettings": "تنظیمات", "navbar_dropdown.addGroupsToTeam": "گروه ها را به تیم اضافه کنید", "navbar_dropdown.console": "کنسول سیستم", "navbar_dropdown.create": "یک تیم ایجاد کنید", "navbar_dropdown.help": "کمک", "navbar_dropdown.integrations": "ادغام ها", "navbar_dropdown.invitePeople": "مردم را دعوت کنید", "navbar_dropdown.invitePeopleExtraText": "افراد را به تیم اضافه کنید", "navbar_dropdown.join": "به تیم دیگری بپیوندید", "navbar_dropdown.leave": "ترک تیم", "navbar_dropdown.leave.icon": "نماد تیم را ترک کنید", "navbar_dropdown.logout": "خروج", "navbar_dropdown.manageGroups": "مدیریت گروه ها", "navbar_dropdown.manageMembers": "مدیریت اعضا", "navbar_dropdown.marketplace": "بازار", "navbar_dropdown.menuAriaLabel": "منوی اصلی", "navbar_dropdown.nativeApps": "دان<PERSON>ود برنامه ها", "navbar_dropdown.profileSettings": "مشخصات", "navbar_dropdown.report": "گزارش یک مشکل", "navbar_dropdown.switchTo": "تغییر به ", "navbar_dropdown.teamSettings": "تنظیمات تیم", "navbar_dropdown.userGroups": "گروه‌های کاربر", "navbar_dropdown.viewMembers": "مشاهده عضوها", "next_steps_view.welcomeToMattermost": "به Mattermost خوش آمدید", "no_results.channel_files.subtitle": "فایل های ارسال شده در این کانال در اینجا نمایش داده می شوند.", "no_results.channel_files.title": "هنوز فایلی وجود ندارد", "no_results.channel_files_filtered.subtitle": "این کانال حاوی هیچ فایلی با فرمت فایل انتخابی نیست.", "no_results.channel_files_filtered.title": "فایلی پیدا نشد", "no_results.channel_search.subtitle": "املا را بررسی کنید یا جستجوی دیگری را امتحان کنید.", "no_results.channel_search.title": "هیچ نتیجه ای برای {channelName} وجود ندارد", "no_results.flagged_posts.subtitle": "پیام های ذخیره شده فقط برای شما قابل مشاهده است. پیام‌ها را برای پیگیری علامت‌گذاری کنید یا چیزی را برای بعداً با کلیک کردن روی {نماد} ذخیره کنید تا در اینجا ذخیره شوند.", "no_results.flagged_posts.title": "هنوز پست ذخیره شده ای وجود ندارد", "no_results.mentions.subtitle": "پیام‌هایی که در آن شخصی از شما نام می‌برد یا کلمات محرک شما را شامل می‌شود، در اینجا ذخیره می‌شوند.", "no_results.mentions.title": "هنوز اشاره ای نشده است", "no_results.user_group_members.title": "هنوز عضوی نیست", "no_results.user_groups.title": "هنوز گروهی نیست", "notification.crt": "پاسخ در {title}", "notification.dm": "پیام مستقیم", "notify_all.confirm": "تا<PERSON><PERSON>د", "notify_all.title.confirm": "ارسال اعلان‌ها به کل کانال را تأیید کنید", "notify_all.title.confirm_groups": "ارسال اعلان ها به گروه ها را تایید کنید", "notify_here.question": "با استفاده از **@here** می‌خواهید اعلان‌هایی را برای حداکثر **{totalMembers} نفر** ارسال کنید. آیا مطمئنید که می خواهید این کار را انجام دهید؟", "numMembers": "{تعداد، عدد} {تعداد، جمع، یک {عضو} سایر {اعضا}}", "onboardingTask.checklist.video_title": "مشاهده نمای کلی", "onboardingTask.completeYourProfileTour.title": "نمایه‌ی خود را ویرایش کنید", "onboardingTour.customizeYourExperience.title": "تجربه‌ی خود را سفارشی کنید", "onboardingTour.sendMessage.title": "ارسال پیام‌ها", "onboarding_wizard.next": "ادامه", "onboarding_wizard.plugins.github": "GitHub", "onboarding_wizard.plugins.gitlab": "GitLab", "onboarding_wizard.plugins.jira": "<PERSON><PERSON>", "onboarding_wizard.plugins.zoom": "Zoom", "passwordRequirements": "الزامات رمز عبور:", "password_form.change": "رمز عبورم را عوض کن", "password_form.enter": "یک رمز عبور جدید برای حساب {siteName} خود وارد کنید.", "password_form.pwd": "کلمه عبور", "password_form.title": "تنظیم مجدد رمز عبور", "password_send.checkInbox": "لطفا صندوق ورودی خود را بررسی کنید.", "password_send.description": "برای بازنشانی رمز عبور، آدرس ایمیلی را که برای ثبت نام استفاده کرده اید وارد کنید", "password_send.email": "پست الکترونیک", "password_send.error": "لطفا یک آدرس ایمیل معتبر وارد کنید.", "password_send.link": "در صورت وجود حساب، یک ایمیل بازنشانی رمز عبور به آدرس زیر ارسال می شود:", "password_send.reset": "رمز عبور من تنظیم مجدد", "password_send.title": "تنظیم مجدد رمز عبور", "payment_form.address": "نشانی", "payment_form.address_2": "آدرس 2", "payment_form.city": "شهر", "payment_form.country": "کشور", "payment_form.zipcode": "کد پستی / کد پستی", "pending_post_actions.cancel": "انصراف", "pending_post_actions.retry": "دوباره امتحان کنید", "permalink.error.access": "پیوند ثابت متعلق به یک پیام حذف شده یا کانالی است که شما به آن دسترسی ندارید.", "permalink.error.title": "پیام یافت نشد", "permalink.show_dialog_warn.join": "<PERSON><PERSON><PERSON><PERSON> شوید", "permalink.show_dialog_warn.title": "به کانال خصوصی ملحق شوید", "plan.cloud": "ابری", "plan.self_serve": "خود-م<PERSON><PERSON><PERSON><PERSON>ی", "pluggable.errorRefresh": "تازه‌سازی؟", "post.ariaLabel.attachment": ", 1 پیوست", "post.ariaLabel.attachmentMultiple": ", {attachmentCount} پیوست", "post.ariaLabel.message": "در {time} {date}، {authorName} نوشت، {message}", "post.ariaLabel.messageIsFlagged": "، پیام ذخیره می شود", "post.ariaLabel.messageIsFlaggedAndPinned": "، پیام ذخیره و پین می شود", "post.ariaLabel.messageIsPinned": "، پیام پین شده است", "post.ariaLabel.reaction": "، 1 واکنش", "post.ariaLabel.reactionMultiple": ", {reactionCount} واکنش", "post.ariaLabel.replyMessage": "در {time} {date}، {authorName} پاسخ داد، {message}", "post_body.check_for_out_of_channel_groups_mentions.message": "با این ذکر اطلاعی دریافت نکردند زیرا در کانال نیستند. آنها را نمی توان به کانال اضافه کرد زیرا آنها عضو گروه های پیوند شده نیستند. برای افزودن آنها به این کانال، باید به گروه های مرتبط اضافه شوند.", "post_body.check_for_out_of_channel_mentions.link.and": " و ", "post_body.check_for_out_of_channel_mentions.link.private": "آنها را به این کانال خصوصی اضافه کنید", "post_body.check_for_out_of_channel_mentions.link.public": "آنها را به کانال اضافه کنید", "post_body.check_for_out_of_channel_mentions.message.multiple": "با این ذکر اطلاعی دریافت نکردند زیرا در کانال نیستند. آیا شما می خواهید ", "post_body.check_for_out_of_channel_mentions.message.one": "با این ذکر اطلاعی دریافت نکردند زیرا در کانال نیستند. آیا شما می خواهید ", "post_body.check_for_out_of_channel_mentions.message_last": "? آنها به تمام تاریخچه پیام دسترسی خواهند داشت.", "post_body.check_for_out_of_channel_mentions.others": "{numOthers} دیگر", "post_body.commentedOn": "در مورد پیام {name} نظر داد: ", "post_body.deleted": "(پیغام پاک شد)", "post_body.plusMore": " به علاوه {count, number} other {count, plural, one {file} other {files}}", "post_delete.notPosted": "نظر نمی تواند ارسال شود", "post_delete.okay": "باشه", "post_delete.someone": "شخصی پیامی را که شما سعی داشتید روی آن نظر ارسال کنید حذف کرد.", "post_header.update_status": "به‌روزرسانی وضعیت خود", "post_info.actions.tooltip.actions": "عملیات", "post_info.auto_responder": "پا<PERSON><PERSON> خودکار", "post_info.comment_icon.tooltip.reply": "پا<PERSON><PERSON>", "post_info.copy": "رونوشت متن", "post_info.del": "<PERSON><PERSON><PERSON>", "post_info.edit": "ویرایش کنید", "post_info.edit.current_version": "نسخه جاری", "post_info.edit.undo": "واگرد", "post_info.info.view_additional_info": "مشاهده اطلاعات اضافی", "post_info.marketplace": "بازارچه برنامه", "post_info.menuAriaLabel": "گزینه های اضافی را ارسال کنید", "post_info.message.show_less": "کمتر نشان دادن", "post_info.message.show_more": "بیشتر نشان بده، اطلاعات بیشتر", "post_info.message.visible": "(فقط برای شما قابل مشاهده است)", "post_info.message.visible.compact": " (فقط برای شما قابل مشاهده است)", "post_info.permalink": "لینک را کپی کنید", "post_info.pin": "پین کردن به کانال", "post_info.post_reminder.menu": "یادآوری", "post_info.post_reminder.sub_menu.custom": "سفار<PERSON>ی", "post_info.post_reminder.sub_menu.one_hour": "1 ساعت", "post_info.post_reminder.sub_menu.thirty_minutes": "30 دقیقه", "post_info.post_reminder.sub_menu.tomorrow": "فردا", "post_info.post_reminder.sub_menu.two_hours": "2 ساعت", "post_info.reply": "پا<PERSON><PERSON>", "post_info.submenu.icon": "نماد منوی فرعی", "post_info.submenu.mobile": "زیر منوی موبایل", "post_info.system": "سیستم", "post_info.tooltip.actions": "عملیات پیام", "post_info.tooltip.add_reactions": "Reaction را اضافه کنید", "post_info.unpin": "پین را از کانال بردارید", "post_info.unread": "به عنوان \"خوانده نشده\" علامت گذاری کن", "post_message_preview.channel": "فقط برای کاربران در ~{channel} قابل مشاهده است", "post_message_view.edited": "ویرایش شده", "post_pre_header.flagged": "ذخیره", "post_pre_header.pinned": "پین شده", "post_priority.acknowledgements.title": "تصدیق‌ها", "post_priority.button.acknowledge": "تصدیق‌کردن", "post_priority.picker.apply": "اعمال‌کردن", "post_priority.picker.cancel": "لغو", "post_priority.picker.header": "اولویت پیام", "post_priority.priority.important": "مهم", "post_priority.priority.standard": "<PERSON><PERSON><PERSON>", "post_priority.priority.urgent": "فوری", "post_priority.request_acknowledgement": "درخواست تصدیق", "post_priority.request_acknowledgement.tooltip": "تصدیق درخواست می‌شود", "post_priority.requested_ack.description": "یک دکمه تصدیق همراه پیام شما ظاهر می‌شود", "post_priority.requested_ack.text": "درخواست تصدیق", "post_priority.you.acknowledge": "(شما)", "post_reminder.custom_time_picker_modal.submit_button": "تنظیم یادآور", "postlist.toast.history": "مشاهده تاریخچه پیام", "postlist.toast.newMessages": "{count, number} new {count, plural, one {message} other {messages}}", "postlist.toast.newMessagesSince": "{count, number} new {count, plural, one {message} other {messages}} {isToday, select, true {} other { from}} {date}", "postlist.toast.scrollToBottom": "پرش به موارد اخیر", "postlist.toast.scrollToLatest": "پرش به پیام های جدید", "postlist.toast.searchHint": "نکته: برای جستجوی این کانال، {searchShortcut} را امتحان کنید", "posts_view.loadMore": "بارگیری پیام های بیشتر", "posts_view.newMsg": "<PERSON><PERSON>ا<PERSON> جدید", "pricing_modal.addons.dedicatedDB": "پایگاه داده اختصاصی", "pricing_modal.addons.missionCritical": "ماموریت حساس ۷×۲۴", "pricing_modal.addons.premiumSupport": "پشتیبانی اعلا", "pricing_modal.addons.title": "افزونه‌های موجود", "pricing_modal.briefing.professional.unLimitedTeams": "تیم‌های نامحدود", "pricing_modal.briefing.title": "برترین ویژگی‌ها", "pricing_modal.btn.contactSales": "تماس با بخش فروش", "pricing_modal.btn.contactSalesForQuote": "تماس با بخش فروش", "pricing_modal.btn.downgrade": "تنزل", "pricing_modal.btn.upgrade": "ارتقا", "pricing_modal.btn.viewPlans": "مشاهده طرح‌ها", "pricing_modal.noitfy_cta.request_success": "درخواست ارسال شد", "pricing_modal.or": "یا", "pricing_modal.planLabel.currentPlan": "طرح فعلی", "promote_to_user_modal.desc": "این اقدام، مهمان {username} را به عضوی ارتقا می‌دهد. این به کاربر امکان می دهد به کانال های عمومی بپیوندد و با کاربران خارج از کانال هایی که در حال حاضر در آن عضو هستند تعامل داشته باشد. آیا مطمئن هستید که می خواهید مهمان {username} را به عضو ارتقا دهید؟", "promote_to_user_modal.promote": "ترویج", "promote_to_user_modal.title": "مهمان {username} را به عضو ارتقا دهید", "public_private_selector.private.title": "خصوصی", "public_private_selector.public.description": "همه‌افراد", "public_private_selector.public.title": "عمومی", "quick_switch_modal.help_mobile": "برای پیدا کردن کانال تایپ کنید", "quick_switch_modal.input": "جابجایی سریع ورودی", "quick_switch_modal.switchChannels": "کانال ها را پیدا کنید", "reaction.add.ariaLabel": "یک واکنش اضافه کنید", "reaction.clickToAdd": "(برای افزودن کلیک کنید)", "reaction.clickToRemove": "(برای حذف کلیک کنید)", "reaction.container.ariaLabel": "واکنش ها", "reaction.othersReacted": "{otherUsers, number} {otherUsers, plural, one {user} other {users}}", "reaction.reactWidth.ariaLabel": "با", "reaction.reacted": "{users} {reactionVerb} با {emoji}", "reaction.reactionVerb.user": "واکنش نشان داد", "reaction.reactionVerb.users": "واکنش نشان داد", "reaction.reactionVerb.you": "واکنش نشان داد", "reaction.reactionVerb.youAndUsers": "واکنش نشان داد", "reaction.removeReact.ariaLabel": "حذف واکنش", "reaction.usersAndOthersReacted": "{users} and {otherUsers, number} other {otherUsers, plural, one {user} other {users}}", "reaction.usersReacted": "{users} و {lastUser}", "reaction.you": "شما", "reaction_list.addReactionTooltip": "یک واکنش اضافه کنید", "remove_group_confirm_button": "بله، حذف گروه و {memberCount, plural, one {Member} سایر {Members}}", "remove_group_confirm_message": "{memberCount, number} {memberCount, plural, one {member} {members}} دیگر مرتبط با این گروه از تیم حذف خواهد شد. آیا مطمئن هستید که می خواهید این گروه و {memberCount} {memberCount, plural, one {member} other {members}} را حذف کنید؟", "remove_group_confirm_title": "حذف گروه و {memberCount, number} {memberCount, plural, one {Member} سایر {Members}}", "removed_channel.channelName": "کانال", "removed_channel.from": "حذ<PERSON> شده از ", "removed_channel.okay": "باشه", "removed_channel.remover": "{remover} شما را از {channel} حذف کرد", "removed_channel.someone": "کسی", "rename_category_modal.rename": "تغییر نام دهید", "rename_category_modal.renameCategory": "تغییر نام دسته", "rename_channel.cancel": "انصراف", "rename_channel.defaultError": " - برای کانال پیش فرض قابل تغییر نیست", "rename_channel.displayName": "نام نمایشی", "rename_channel.displayNameHolder": "نام نمایشی را وارد کنید", "rename_channel.maxLength": "این فیلد باید کمتر از {maxLength, number} کاراکتر باشد", "rename_channel.minLength": "نام نمایشی باید حداقل دارای {minLength, number} کاراکتر باشد.", "rename_channel.save": "صرفه جویی", "rename_channel.title": "تغییر نام کانال", "rename_channel.url": "URL", "restricted_indicator.tooltip.title": "ویژگی {minimumPlanRequiredForFeature}", "revoke_user_sessions_modal.desc": "این عمل همه جلسات را برای {username} لغو می‌کند. آنها از همه دستگاه ها خارج خواهند شد. آیا مطمئن هستید که می خواهید همه جلسات را برای {username} لغو کنید؟", "revoke_user_sessions_modal.revoke": "لغو", "revoke_user_sessions_modal.title": "لغو جلسات برای {username}", "rhs_card.jump": "پرش کنید", "rhs_card.message_by": "پیام {avatar} {user}", "rhs_header.back.icon": "تصویرک دکمه عقب", "rhs_header.backToFlaggedTooltip": "بازگشت به پست های ذخیره شده", "rhs_header.backToPinnedTooltip": "بازگشت به پست های پین شده", "rhs_header.backToResultsTooltip": "بازگشت به نتایج جستجو", "rhs_header.closeSidebarTooltip": "نزدیک", "rhs_header.closeTooltip.icon": "بستن نماد نوار کناری", "rhs_header.collapseSidebarTooltip": "کوچک کردن نوار کناری", "rhs_header.collapseSidebarTooltip.icon": "بستن نماد نوار کناری", "rhs_header.details": "نخ", "rhs_header.expandSidebarTooltip": "نوار کناری را باز کنید", "rhs_header.expandSidebarTooltip.icon": "نماد نوار کناری را باز کنید", "rhs_root.mobile.add_reaction": "Reaction را اضافه کنید", "rhs_root.mobile.flag": "صرفه جویی", "rhs_root.mobile.unflag": "حذ<PERSON> از ذخیره شده", "rhs_thread.rootPostDeletedMessage.body": "بخشی از این رشته به دلیل سیاست حفظ داده حذف شده است. شما دیگر نمی توانید به این تاپیک پاسخ دهید.", "rhs_thread.toast.newReplies": "پاسخ های جدید", "save_button.save": "صرفه جویی", "save_button.saving": "صرفه جویی در", "search_bar.files_tab": "فایل ها", "search_bar.messages_tab": "پیام ها", "search_bar.search": "جستجو کردن", "search_bar.searchGroupMembers": "جست‌وجوی اعضای گروه", "search_bar.search_types.files": "فایل ها", "search_bar.search_types.messages": "پیام ها", "search_bar.usage.search_type_files": "فایل ها", "search_bar.usage.search_type_messages": "پیام ها", "search_bar.usage.search_type_question": "دنبال چی میگردی؟", "search_bar.usage.title": "گزینه های جستجو", "search_bar.usage.title_files": "گزینه های جستجوی فایل", "search_bar.usage.title_messages": "گزینه های جستجوی پیام", "search_files_list_option.after": "فایل های بعد از تاریخ", "search_files_list_option.before": "فایل های قبل از تاریخ", "search_files_list_option.exclude": "عبارات جستجو را حذف کنید", "search_files_list_option.ext": "فایل های با پسوند", "search_files_list_option.from": "فایل های یک کاربر", "search_files_list_option.in": "فایل ها در یک کانال", "search_files_list_option.on": "فایل ها در تاریخ", "search_files_list_option.phrases": "فایل هایی با عبارات", "search_header.channelFiles": "پرونده‌ها", "search_header.loading": "جستجوکردن...", "search_header.results": "نتایج جست‌وجو", "search_header.search": "جست‌وجو", "search_header.title2": "اشاره‌های اخیر", "search_header.title3": "نوشته‌های ذخیره‌شده", "search_header.title5": "اطلاعات اضافی", "search_header.title_edit.history": "ویرایش تاریخچه", "search_item.channelArchived": "بایگانی شد", "search_item.direct": "پیام مستقیم (با {username})", "search_item.file_tag.direct_message": "پیام شخصی", "search_item.file_tag.group_message": "پیام گروهی", "search_item.jump": "پرش کنید", "search_list_option.after": "پیام های بعد از تاریخ", "search_list_option.before": "پیام های قبل از تاریخ", "search_list_option.exclude": "عبارات جستجو را حذف کنید", "search_list_option.from": "پیام های یک کاربر", "search_list_option.in": "پیام ها در یک کانال", "search_list_option.on": "پیام ها در تاریخ", "search_list_option.phrases": "پیام هایی با عبارات", "search_results.channel-files-header": "فایل های اخیر", "select_team.icon": "نماد تیم را انتخاب کنید", "select_team.join.icon": "نماد تیم بپیوندید", "select_team.private.icon": "تیم خصوصی", "self_hosted_signup.close": "بستن", "self_hosted_signup.total": "جمع", "setting_item_max.cancel": "انصراف", "setting_item_min.edit": "ویرایش کنید", "setting_picture.cancel": "انصراف", "setting_picture.help.profile": "تصویری را با فرمت BMP، JPG، JPEG یا PNG آپلود کنید. حداکثر اندازه فایل: {max}", "setting_picture.remove": "این نماد را حذف کنید", "setting_picture.remove_profile_picture": "حذف تصویر نمایه", "setting_picture.save": "صرفه جویی", "setting_picture.select": "انتخاب کنید", "setting_picture.uploading": "در حال آپلود...", "shared_channel_indicator.tooltip": "با سازمان های مورد اعتماد به اشتراک گذاشته شده است", "shared_user_indicator.tooltip": "از یک سازمان مورد اعتماد", "shortcuts.browser.channel_next": "رو به جلو در تاریخ: Alt|Right", "shortcuts.browser.channel_next.mac": "پیشرو در تاریخ: ⌘|]", "shortcuts.browser.channel_prev": "بازگشت به تاریخ: Alt|Left", "shortcuts.browser.channel_prev.mac": "بازگشت به تاریخ: ⌘|[", "shortcuts.browser.font_decrease": "بزرگنمایی: Ctrl|-", "shortcuts.browser.font_decrease.mac": "بزرگنمایی: ⌘|-", "shortcuts.browser.font_increase": "بزرگنمایی: Ctrl|+", "shortcuts.browser.font_increase.mac": "بزرگنمایی: ⌘|+", "shortcuts.browser.header": "دستورات داخلی مرورگر", "shortcuts.browser.highlight_next": "متن را به خط بعدی برجسته کنید: Shift|Down", "shortcuts.browser.highlight_prev": "متن را به خط قبلی برجسته کنید: Shift|Up", "shortcuts.browser.input.header": "در داخل یک فیلد ورودی کار می کند", "shortcuts.browser.newline": "ی<PERSON> خط جدید ایجاد کنید: Shift|Enter", "shortcuts.calls.global.header": "سراسری", "shortcuts.calls.header": "تماس‌ها", "shortcuts.calls.widget.header": "ابزارک تماس", "shortcuts.files.header": "فایل ها", "shortcuts.files.upload": "آپلود فایل ها: Ctrl|U", "shortcuts.files.upload.mac": "آپلود فایل: ⌘|U", "shortcuts.header": "میانبرهای صفحه کلید Ctrl|/", "shortcuts.header.mac": "میانبرهای صفحه کلید ⌘|/", "shortcuts.info": "یک پیام با / برای لیستی از تمام دستورات در اختیارتان شروع کنید.", "shortcuts.msgs.comp.channel": "کانال: ~|[a-z]|تب", "shortcuts.msgs.comp.emoji": "ایموجی: :|[a-z]|تب", "shortcuts.msgs.comp.header": "تکمیل خودکار", "shortcuts.msgs.comp.last_reaction": "واکنش به آخرین پیام: Ctrl|Shift|⧵", "shortcuts.msgs.comp.last_reaction.mac": "واکنش به آخرین پیام: ⌘|Shift|⧵", "shortcuts.msgs.comp.username": "نام کاربری: @|[a-z]|تب", "shortcuts.msgs.edit": "ویرایش آخرین پیام در کانال: بالا", "shortcuts.msgs.formatting_bar.post_priority": "اولویت پیام", "shortcuts.msgs.header": "پیام ها", "shortcuts.msgs.input.header": "در یک فیلد ورودی خالی کار می کند", "shortcuts.msgs.markdown.bold": "توپُر:\tCtrl|B", "shortcuts.msgs.markdown.bold.mac": "توپُر:\t⌘|B", "shortcuts.msgs.markdown.code": "کد:\tCtrl|Alt|C", "shortcuts.msgs.markdown.code.mac": "کد:\t⌘|⌥|C", "shortcuts.msgs.markdown.h3": "سربرگ", "shortcuts.msgs.markdown.h3.mac": "سربرگ", "shortcuts.msgs.markdown.header": "قالب", "shortcuts.msgs.markdown.italic": "کج:\tCtrl|I", "shortcuts.msgs.markdown.italic.mac": "کج:\t⌘|I", "shortcuts.msgs.markdown.link": "ایجاد پیوند:\tCtrl|Alt|K", "shortcuts.msgs.markdown.link.mac": "ایجاد پیوند:\t⌘|⌥|K", "shortcuts.msgs.markdown.quote": "نقل‌قول:\tShift|Alt|9", "shortcuts.msgs.markdown.quote.mac": "نقل‌قول:\tShift|⌥|9", "shortcuts.msgs.markdown.strike": "خط‌زدن:\tShift|Alt|X", "shortcuts.msgs.markdown.strike.mac": "خط‌زدن:\tShift|⌥|X", "shortcuts.msgs.markdown.unordered": "فهرست نشانه‌دار\tShift|Alt|8", "shortcuts.msgs.markdown.unordered.mac": "فهرست نشانه‌دار:\tShift|⌥|8", "shortcuts.msgs.reply": "پاسخ به آخرین پیام در کانال: Shift|Up", "shortcuts.msgs.reprint_next": "پیام بعدی را دوباره چاپ کنید: Ctrl|Down", "shortcuts.msgs.reprint_next.mac": "پیام بعدی را دوباره چاپ کنید: ⌘|پایین", "shortcuts.msgs.reprint_prev": "چاپ مجدد پیام قبلی: Ctrl|Up", "shortcuts.msgs.reprint_prev.mac": "چاپ مجدد پیام قبلی: ⌘|بالا", "shortcuts.msgs.search.header": "در حال جست‌وجو", "shortcuts.nav.direct_messages_menu": "منوی پیام های مستقیم: Ctrl|Shift|K", "shortcuts.nav.direct_messages_menu.mac": "منوی پیام های مستقیم: ⌘|Shift|K", "shortcuts.nav.focus_center": "تنظیم فوکوس روی فیلد ورودی: Ctrl|Shift|L", "shortcuts.nav.focus_center.mac": "تنظیم فوکوس روی فیلد ورودی: ⌘|Shift|L", "shortcuts.nav.header": "<PERSON><PERSON><PERSON> یابی", "shortcuts.nav.next": "کانال بعدی: Alt|Down", "shortcuts.nav.next.mac": "کانال بعدی: ⌥|پایین", "shortcuts.nav.open_close_sidebar": "نوار کناری سمت راست Ctrl| را باز یا ببندید.", "shortcuts.nav.open_close_sidebar.mac": "نوار کناری سمت راست ⌘| را باز یا ببندید.", "shortcuts.nav.prev": "کانال قبلی: Alt|Up", "shortcuts.nav.prev.mac": "کانال قبلی: ⌥|بالا", "shortcuts.nav.recent_mentions": "اشاره های اخیر: Ctrl|Shift|M", "shortcuts.nav.recent_mentions.mac": "اشاره های اخیر: ⌘|Shift|M", "shortcuts.nav.settings": "تنظیمات: Ctrl|Shift|A", "shortcuts.nav.settings.mac": "تنظیمات: ⌘|Shift|A", "shortcuts.nav.switcher": "ناوبری سریع کانال: Ctrl|K", "shortcuts.nav.switcher.mac": "پیمایش سریع کانال: ⌘|K", "shortcuts.nav.unread_next": "کانال خوانده نشده بعدی: Alt|Shift|Down", "shortcuts.nav.unread_next.mac": "کانال خوانده نشده بعدی: ⌥|Shift|پایین", "shortcuts.nav.unread_prev": "کانال خوانده نشده قبلی: Alt|Shift|Up", "shortcuts.nav.unread_prev.mac": "کانال خوانده نشده قبلی: ⌥|Shift|Up", "shortcuts.team_nav.next": "تیم بعدی: Ctrl|Alt|Down", "shortcuts.team_nav.next.mac": "تیم بعدی: ⌘|⌥|پایین", "shortcuts.team_nav.prev": "تیم قبلی: Ctrl|Alt|Up", "shortcuts.team_nav.prev.mac": "تیم قبلی: ⌘|⌥|بالا", "shortcuts.team_nav.switcher": "به یک تیم خاص بروید: Ctrl|Alt|[1-9]", "shortcuts.team_nav.switcher.mac": "به یک تیم خاص پیمایش کنید: ⌘|⌥|[1-9]", "sidebar.createDirectMessage": "پیام مستقیم بنویسید", "sidebar.directchannel.you": "{displayname} (شما)", "sidebar.menu.item.notSelected": "انتخاب‌نشده", "sidebar.menu.item.selected": "انتخاب‌شده", "sidebar.show": "نمایش", "sidebar.sort": "ترتیب", "sidebar.sortedByRecencyLabel": "فعالیت اخیر", "sidebar.sortedManually": "دستی", "sidebar.team_menu.button.plusIcon": "تصویرک مثبت", "sidebar.team_menu.menuAriaLabel": "منوی تیم", "sidebar.team_select": "{siteName} - به یک تیم بپیوندید", "sidebar.types.channels": "کانال ها", "sidebar.types.direct_messages": "پیام مستقیم", "sidebar.types.favorites": "موارد دلخواه", "sidebar.types.unreads": "خوانده نشده ها", "sidebar.unreads": "خوانده نشده های بیشتر", "sidebar_left.add_channel_dropdown.browseChannels": "کانال ها را مرور کنید", "sidebar_left.add_channel_dropdown.browseOrCreateChannels": "کانال ها را مرور یا ایجاد کنید", "sidebar_left.add_channel_dropdown.createCategory": "ایج<PERSON> رده جدید", "sidebar_left.add_channel_dropdown.createNewChannel": "ایج<PERSON> کانال جدید", "sidebar_left.add_channel_dropdown.dropdownAriaLabel": "افزودن کشویی کانال", "sidebar_left.add_channel_dropdown.invitePeople": "مردم را دعوت کنید", "sidebar_left.add_channel_dropdown.invitePeopleExtraText": "افراد را به تیم اضافه کنید", "sidebar_left.channel_filter.filterByUnread": "فیلتر بر اساس خوانده نشده", "sidebar_left.channel_filter.filterUnreadAria": "صافی خوانده‌نشده‌ها", "sidebar_left.channel_filter.showAllChannels": "نمایش همه کانال ها", "sidebar_left.channel_navigator.channelSwitcherLabel": "کانال ها را پیدا کنید", "sidebar_left.channel_navigator.goBackLabel": "بازگشت", "sidebar_left.channel_navigator.goForwardLabel": "رو به جلو", "sidebar_left.channel_navigator.jumpTo": "کانال را پیدا کنید", "sidebar_left.inviteMembers": "دعوت از اعضا", "sidebar_left.sidebar_category.newDropBoxLabel": "کانال ها را به اینجا بکشید...", "sidebar_left.sidebar_category.newLabel": "ج<PERSON><PERSON><PERSON>", "sidebar_left.sidebar_category_menu.createCategory": "ایج<PERSON> رده جدید", "sidebar_left.sidebar_category_menu.deleteCategory": "حذف دسته", "sidebar_left.sidebar_category_menu.dropdownAriaLabel": "ویرایش منوی دسته", "sidebar_left.sidebar_category_menu.editCategory": "گزینه های دسته بندی", "sidebar_left.sidebar_category_menu.muteCategory": "بی‌صدا کردن دسته", "sidebar_left.sidebar_category_menu.renameCategory": "تغییر نام دسته", "sidebar_left.sidebar_category_menu.sort.dropdownAriaLabel": "مرتب‌سازی زیرفهرست", "sidebar_left.sidebar_category_menu.unmuteCategory": "صدادار کردن دسته", "sidebar_left.sidebar_channel.selectedCount": "{count} انتخاب‌شده", "sidebar_left.sidebar_channel_menu.addMembers": "اضافه کردن عضو", "sidebar_left.sidebar_channel_menu.channels": "کانال‌ها", "sidebar_left.sidebar_channel_menu.copyLink": "لینک را کپی کنید", "sidebar_left.sidebar_channel_menu.dropdownAriaLabel": "ویرایش منوی کانال", "sidebar_left.sidebar_channel_menu.editChannel": "گزینه های کانال", "sidebar_left.sidebar_channel_menu.favoriteChannel": "مو<PERSON><PERSON> علاقه", "sidebar_left.sidebar_channel_menu.favorites": "برگزیده‌ها", "sidebar_left.sidebar_channel_menu.leaveChannel": "ترک کانال", "sidebar_left.sidebar_channel_menu.leaveConversation": "بستن مکالمه", "sidebar_left.sidebar_channel_menu.markAsRead": "به عنوان خوانده شده علامت بزن", "sidebar_left.sidebar_channel_menu.moveTo": "حرکت به...", "sidebar_left.sidebar_channel_menu.moveToNewCategory": "دسته بندی جدید", "sidebar_left.sidebar_channel_menu.muteChannel": "نادیده گرفتن کانال", "sidebar_left.sidebar_channel_menu.muteConversation": "صدای گفتگو را قطع کن", "sidebar_left.sidebar_channel_menu.unfavoriteChannel": "نامطلوب", "sidebar_left.sidebar_channel_menu.unmuteChannel": "لغو نادیده گرفتن کانال", "sidebar_left.sidebar_channel_menu.unmuteConversation": "باصدا کردن مکالمه", "sidebar_left.sidebar_channel_navigator.inviteUsers": "دعوت از کاربران", "sidebar_right_menu.console": "کنسول سیستم", "sidebar_right_menu.flagged": "پست های ذخیره شده", "sidebar_right_menu.recentMentions": "اشاره های اخیر", "signup.ldap": "اعتبارنامه AD/LDAP", "signup_team.guest_without_channels": "حساب مهمان شما هیچ کانالی اختصاص داده نشده است. لطفا با یک مدیر تماس بگیرید.", "signup_team.join_open": "تیم هایی که می توانید به آنها ملحق شوید: ", "signup_team.no_open_teams": "هیچ تیمی برای پیوستن در دسترس نیست. لطفاً از سرپرست خود دعوت بخواهید.", "signup_team.no_open_teams_canCreate": "هیچ تیمی برای پیوستن در دسترس نیست. لطفاً یک تیم جدید ایجاد کنید یا از سرپرست خود دعوت بخواهید.", "signup_team_system_console": "به کنسول سیستم بروید", "signup_user_completed.chooseUser": "نام کاربری خود را انتخاب کنید", "signup_user_completed.create": "ایج<PERSON> حساب کاربری", "signup_user_completed.emailIs": "آدرس ایمیل شما **{email}** است. از این آدرس برای ورود به {siteName} استفاده خواهید کرد.", "signup_user_completed.emailLabel": "آدرس رایانامه", "signup_user_completed.failed_update_user_state": "لطفاً کش خود را پاک کنید و سعی کنید وارد شوید.", "signup_user_completed.haveAccount": "از قبل حساب کاربری دارید؟", "signup_user_completed.required": "این فیلد الزامی است", "signup_user_completed.reserved": "این نام کاربری رزرو شده است، لطفا نام کاربری جدید را انتخاب کنید.", "signup_user_completed.saving": "در حال ایجاد حساب‌کاربری…", "signup_user_completed.signIn": "برای ورود اینجا را کلیک کنید.", "signup_user_completed.userHelp": "می توانید از حروف کوچک، اعداد، نقطه، خط تیره و زیرخط استفاده کنید.", "signup_user_completed.usernameLength": "نام کاربری باید با یک حرف کوچک شروع شود و طول آن از {min}-{max} کاراکتر باشد. می توانید از حروف کوچک، اعداد، نقطه، خط تیره و زیرخط استفاده کنید.", "signup_user_completed.validEmail": "لطفا یک آدرس ایمیل معتبر وارد کنید", "single_image_view.copied_link_tooltip": "رونوشت شد", "single_image_view.copy_link_tooltip": "رونوشت پیوند", "single_image_view.download_tooltip": "بارگیری", "someting.string": "defaultString", "start_cloud_trial.modal.enter_trial_email.input.placeholder": "<EMAIL>", "start_trial.modal.failed": "ناموفق", "start_trial.modal.gettingTrial": "در حال دریافت دوره آزمایشی...", "start_trial.modal.loaded": "لود شده!", "start_trial.modal.loading": "بارگذاری...", "status_dropdown.dnd_sub_menu_header": "غیرفعال کردن اعلان ها تا زمانی که:", "status_dropdown.dnd_sub_menu_item.custom": "سفار<PERSON>ی", "status_dropdown.dnd_sub_menu_item.one_hour": "1 ساعت", "status_dropdown.dnd_sub_menu_item.thirty_minutes": "30 دقیقه", "status_dropdown.dnd_sub_menu_item.tomorrow": "فردا", "status_dropdown.dnd_sub_menu_item.two_hours": "2 ساعت", "status_dropdown.menuAriaLabel": "وضعیت را تنظیم کنید", "status_dropdown.set_away": "دور", "status_dropdown.set_custom_text": "تنظیم متن وضعیت سفارشی...", "status_dropdown.set_dnd": "مزاحم نشوید", "status_dropdown.set_dnd.extra": "همه اعلان ها را غیرفعال می کند", "status_dropdown.set_offline": "آف<PERSON><PERSON><PERSON>ن", "status_dropdown.set_online": "برخط", "status_dropdown.set_ooo": "بیرون از دفتر", "status_dropdown.set_ooo.extra": "پاسخ های خودکار فعال هستند", "suggestion.archive": "کانال های آرشیو شده", "suggestion.commands": "دستورات", "suggestion.emoji": "شکلک", "suggestion.mention.all": "به همه در این کانال اطلاع رسانی می کند", "suggestion.mention.channel": "به همه در این کانال اطلاع رسانی می کند", "suggestion.mention.channels": "کانال های من", "suggestion.mention.here": "به اطلاع همه افراد آنلاین در این کانال می رساند", "suggestion.mention.members": "اعضای کانال", "suggestion.mention.morechannels": "کانال های دیگر", "suggestion.mention.moremembers": "سایر اعضا", "suggestion.mention.nonmembers": "در کانال نیست", "suggestion.mention.private.channels": "کانال های خصوصی", "suggestion.mention.recent.channels": "ا<PERSON><PERSON>ر", "suggestion.mention.special": "اشاره های ویژه", "suggestion.mention.unread": "خوانده‌نشده", "suggestion.search.direct": "پیام مستقیم", "suggestion.search.group": "پیام های گروهی", "suggestion.search.private": "کانال های خصوصی", "suggestion.search.public": "کانال های عمومی", "suggestion.user.isCurrent": "(شما)", "system_notice.adminVisible": "فقط برای مدیران سیستم قابل مشاهده است", "system_notice.adminVisible.icon": "فقط برای نماد مدیران سیستم قابل مشاهده است", "system_notice.body.api3": "اگر در دو سال گذشته ادغام هایی ایجاد یا نصب کرده اید، دریابید که چگونه <link>تغییرات اخیر</link>  چه تاثیری بر آنها داشته است.", "system_notice.body.ee_upgrade_advice": "نسخه Enterprise برای اطمینان از عملکرد بهینه و قابلیت اطمینان توصیه می شود. <link>بیشتر بیاموزید</link>.", "system_notice.body.ie11_deprecation": "مرورگر شما، IE11، دیگر در نسخه آینده پشتیبانی نخواهد شد. <link>در یک مرحله ساده نحوه انتقال به مرورگر دیگر را بیابید</link>.", "system_notice.body.permissions": "برخی از تنظیمات کنسول سیستم خط‌مشی و مجوز با انتشار <link>مجوزهای پیشرفته</link>  به Mattermost Starter و Professional منتقل شده‌اند.", "system_notice.dont_show": "دوباره نمایش داده نشود", "system_notice.remind_me": "بعدا به من یادآوری کن", "system_notice.title": "**اطلاع**\nاز Mattermost", "tag.default.beta": "آزمایشی", "tag.default.bot": "بات", "tag.default.guest": "مه<PERSON>ان", "tag.default.new": "ج<PERSON><PERSON><PERSON>", "team.button.ariaLabel": "تیم {teamName}", "team.button.mentions.ariaLabel": "تیم {teamName}، {mentionCount} ذکر شده است", "team.button.name_undefined": "این تیم نامی ندارد", "team.button.unread.ariaLabel": "تیم {teamName} خوانده نشده است", "team_channel_settings.group.group_user_row.numberOfGroups": "{مقدار، عدد} {مقدار، جمع، یک {گروه} دیگر {گروه‌ها}}", "team_member_modal.invitePeople": "مردم را دعوت کنید", "team_member_modal.members": "اعضا {team}", "team_members_dropdown.confirmDemoteDescription": "اگر خود را از نقش مدیر سیستم تنزل دهید و کاربر دیگری با امتیازات مدیریت سیستم وجود نداشته باشد، باید با دسترسی به سرور Mattermost از طریق ترمینال و اجرای دستور زیر، یک مدیر سیستم را مجدداً اختصاص دهید.", "team_members_dropdown.confirmDemoteRoleTitle": "تنزل را از نقش مدیر سیستم تأیید کنید", "team_members_dropdown.confirmDemotion": "تنزل رتبه را تأیید کنید", "team_members_dropdown.confirmDemotionCmd": "نقش های پلت فرم system_admin {username}", "team_members_dropdown.guest": "مه<PERSON>ان", "team_members_dropdown.inactive": "غیر فعال", "team_members_dropdown.leave_team": "حذ<PERSON> از تیم", "team_members_dropdown.makeAdmin": "تیم را ادمین کنید", "team_members_dropdown.makeMember": "عضو تیم شوید", "team_members_dropdown.makeTeamAdmins": "ادمین های تیم بسازید", "team_members_dropdown.makeTeamMembers": "اعضای تیم بسازید", "team_members_dropdown.member": "عضو", "team_members_dropdown.menuAriaLabel": "نقش یکی از اعضای تیم را تغییر دهید", "team_members_dropdown.systemAdmin": "ادمین سیستم", "team_members_dropdown.teamAdmin": "ادمین تیم", "team_members_dropdown.teamAdmins": "ادمین های تیم", "team_members_dropdown.teamMembers": "اعضای تیم", "team_settings.openInviteDescription.groupConstrained": "خیر، اعضای این تیم توسط گروه های مرتبط اضافه و حذف می شوند. <link>بیشتر بیاموزید</link>", "team_settings_modal.title": "تنظیمات تیم", "team_sidebar.join": "تیم های دیگری که می توانید به آنها ملحق شوید", "terms_of_service.agreeButton": "موا<PERSON>قم", "terms_of_service.api_error": "قادر به تکمیل درخواست نیست. اگر این مشکل ادامه داشت، با مدیر سیستم خود تماس بگیرید.", "terms_of_service.disagreeButton": "من مخالفم", "textbox.bold": "**پررنگ**", "textbox.edit": "ویرایش پیام", "textbox.help": "کمک", "textbox.inlinecode": "\"کد درون خطی\".", "textbox.italic": "*مورب*", "textbox.preformatted": "``پیش فرمت شده``", "textbox.preview": "پیش نمایش", "textbox.quote": "> نقل قول", "textbox.strike": "ضربه", "threading.filters.unreads": "خوانده نشده ها", "threading.following": "پیروی", "threading.footer.lastReplyAt": "آخرین پاسخ {formatted}", "threading.header.heading": "نخ", "threading.notFollowing": "دنبال‌کردن", "threading.threadHeader.menu": "اقدامات بیشتر", "threading.threadItem.menu": "اقدامات", "threading.threadList.markRead": "همه را به عنوان خوانده شده علامت بزن", "threading.threadMenu.copy": "رونوشت از پیوند", "threading.threadMenu.follow": "دنبال کردن گفت‌وگو", "threading.threadMenu.followMessage": "پیام را دنبال کنید", "threading.threadMenu.markRead": "علامت زدن به عنوان خوانده‌شده", "threading.threadMenu.markUnread": "علامت زدن به عنوان خوانده‌نشده", "threading.threadMenu.openInChannel": "باز کردن در کانال", "threading.threadMenu.save": "ذخیره", "threading.threadMenu.unfollow": "رها کردن گفت‌وگو", "threading.threadMenu.unfollowMessage": "لغو دنبال کردن پیام", "threading.threadMenu.unsave": "عد<PERSON> ذخیره", "three_days_left_trial.modal.limitsTitle": "محدودیت‌ها", "three_days_left_trial_modal.learnMore": "مطالعه بیشتر", "time_dropdown.choose_time": "زمانی را انتخاب کنید", "timestamp.datetime": "{relativeOrDate} در {time}", "trial_benefits.modal.onlyVisibleToAdmins": "فقط برای مدیران قابل مشاهده است", "trial_benefits.modal.playbooksButton": "بازکردن اجرانامه‌ها", "trial_benefits.modal.playbooksTitle": "قدرت دست اجرانامه‌ها است", "trial_benefits_modal.trial_just_started.buttons.close": "بستن", "trial_benefits_modal.trial_just_started.buttons.invitePeople": "دعوت افراد", "tutorial_threads.list.Down": "پایین", "tutorial_threads.list.Up": "بالا", "tutorial_threads.list.title": "فهرست گفت‌وگوها", "tutorial_threads.unread.title": "گفت‌وگوهای خوانده‌نشده", "tutorial_tip.done": "انجام‌شد", "tutorial_tip.got_it": "فهمیدم", "tutorial_tip.ok": "بعد", "tutorial_tip.out": "از این نکات انصراف دهید.", "tutorial_tip.seen": "این را قبلا دیده اید؟ ", "unarchive_channel.cancel": "انصراف", "unarchive_channel.confirm": "کانال UNARCHIVE را تایید کنید", "unarchive_channel.del": "لغو بایگانی", "update_command.confirm": "دستور ویرایش اسلش", "update_command.question": "تغییرات شما ممکن است دستور اسلش موجود را خراب کند. آیا مطمئن هستید که می خواهید آن را به روز کنید؟", "update_command.update": "به روز رسانی", "update_incoming_webhook.update": "به روز رسانی", "update_incoming_webhook.updating": "در حال بروز رسانی...", "update_oauth_app.confirm": "برن<PERSON><PERSON>ه OAuth 2.0 را ویرایش کنید", "update_oauth_app.question": "تغییرات شما ممکن است برنامه موجود OAuth 2.0 را خراب کند. آیا مطمئن هستید که می خواهید آن را به روز کنید؟", "update_outgoing_webhook.confirm": "وب هوک خروجی را ویرایش کنید", "update_outgoing_webhook.question": "تغییرات شما ممکن است وب هوک خروجی موجود را خراب کند. آیا مطمئن هستید که می خواهید آن را به روز کنید؟", "update_outgoing_webhook.update": "به روز رسانی", "upgradeLink.warn.upgrade_now": "الان ارتقا بده", "upload_overlay.info": "یک فایل را رها کنید تا آپلود شود.", "url_input.buttonLabel.done": "انجام شد", "url_input.buttonLabel.edit": "ویرایش", "url_input.label.url": "آدرس: ", "user.settings.advance.confirmDeactivateAccountTitle": "غیرفعال سازی را تایید کنید", "user.settings.advance.confirmDeactivateDesc": "آیا مطمئن هستید که می خواهید حساب خود را غیرفعال کنید؟ این فقط توسط مدیر سیستم شما قابل برگشت است.", "user.settings.advance.deactivateAccountTitle": "غیرفعال کردن اکانت", "user.settings.advance.deactivateDesc": "غیرفعال کردن اکانت شما امکان ورود به این سرور را از بین می برد و تمام اعلان های ایمیل و موبایل را غیرفعال می کند. برای فعال سازی مجدد حساب خود، با مدیر سیستم خود تماس بگیرید.", "user.settings.advance.deactivateDescShort": "برای غیرفعال کردن حساب خود روی \"ویرایش\" کلیک کنید", "user.settings.advance.deactivate_member_modal.deactivateButton": "بله، اکانت من را غیرفعال کنید", "user.settings.advance.formattingDesc": "اگر فعال باشد، پست‌ها برای ایجاد پیوند، نمایش شکلک‌ها، استایل دادن به متن و اضافه کردن خطوط شکست قالب‌بندی می‌شوند. به طور پیش فرض، این تنظیم فعال است.", "user.settings.advance.formattingTitle": "قالب بندی پست را فعال کنید", "user.settings.advance.icon": "نماد تنظیمات پیشرفته", "user.settings.advance.joinLeaveDesc": "وقتی «روشن» باشد، پیام‌های سیستمی که می‌گویند کاربر به کانالی پیوسته یا آن را ترک کرده است، قابل مشاهده خواهد بود. هنگامی که \"خاموش\" است، پیام های سیستم در مورد پیوستن یا ترک یک کانال پنهان می شود. وقتی به کانال اضافه می‌شوید، همچنان پیامی نمایش داده می‌شود، بنابراین می‌توانید اعلان دریافت کنید.", "user.settings.advance.joinLeaveTitle": "Join/Leave Messages را فعال کنید", "user.settings.advance.off": "خاموش", "user.settings.advance.on": "بر", "user.settings.advance.onForAllMessages": "برای همه پیام ها روشن است", "user.settings.advance.onForCode": "فقط برای بلوک‌های کدی که با «`» شروع می‌شوند روشن است", "user.settings.advance.performance.title": "عی<PERSON> یابی کارایی", "user.settings.advance.sendDesc": "هنگامی که فعال باشد، CTRL + ENTER پیام را ارسال می کند و ENTER یک خط جدید درج می کند.", "user.settings.advance.sendDesc.mac": "وقتی فعال باشد، ⌘ + ENTER پیام را ارسال می کند و ENTER یک خط جدید درج می کند.", "user.settings.advance.sendTitle": "با CTRL+ENTER پیام ارسال کنید", "user.settings.advance.sendTitle.mac": "پیام‌ها را در ⌘+ENTER ارسال کنید", "user.settings.advance.title": "تنظیمات پیشرفته", "user.settings.custom_theme.awayIndicator": "نشانگر دور", "user.settings.custom_theme.buttonBg": "دکمه BG", "user.settings.custom_theme.buttonColor": "متن دکمه", "user.settings.custom_theme.centerChannelBg": "کانال مرکز BG", "user.settings.custom_theme.centerChannelColor": "متن کانال وسط", "user.settings.custom_theme.centerChannelTitle": "سبک های کانال مرکزی", "user.settings.custom_theme.codeTheme": "تم کد", "user.settings.custom_theme.copied": "✔ کپی شده", "user.settings.custom_theme.copyPaste": "برای اشتراک‌گذاری رنگ‌های زمینه، کپی و جای‌گذاری کنید:", "user.settings.custom_theme.copyThemeColors": "کپی رنگ های تم", "user.settings.custom_theme.dndIndicator": "نشانگر مزاحم نشوید", "user.settings.custom_theme.errorTextColor": "خطای رنگ متن", "user.settings.custom_theme.linkButtonTitle": "سبک های پیوند و دکمه", "user.settings.custom_theme.linkColor": "رنگ پیوند", "user.settings.custom_theme.mentionBg": "Jewel BG را ذکر کنید", "user.settings.custom_theme.mentionColor": "متن جواهر را ذکر کنید", "user.settings.custom_theme.mentionHighlightBg": "هایلایت BG را ذکر کنید", "user.settings.custom_theme.mentionHighlightLink": "لینک هایلایت را ذکر کنید", "user.settings.custom_theme.newMessageSeparator": "جداکننده پیام جدید", "user.settings.custom_theme.onlineIndicator": "نشانگر آنلاین", "user.settings.custom_theme.sidebarBg": "نوار کناری BG", "user.settings.custom_theme.sidebarHeaderBg": "هدر نوار کناری BG", "user.settings.custom_theme.sidebarHeaderTextColor": "متن سرصفحه نوار کناری", "user.settings.custom_theme.sidebarText": "متن نوار کناری", "user.settings.custom_theme.sidebarTextActiveBorder": "حاشیه فعال متن نوار کناری", "user.settings.custom_theme.sidebarTextActiveColor": "رنگ فعال متن نوار کناری", "user.settings.custom_theme.sidebarTextHoverBg": "نوار کناری متن شناور BG", "user.settings.custom_theme.sidebarTitle": "سبک های نوار کناری", "user.settings.custom_theme.sidebarUnreadText": "نوار کناری متن خوانده نشده", "user.settings.display.availabilityStatusOnPostsDescription": "هنگامی که فعال باشد، در دسترس بودن آنلاین در تصاویر نمایه در لیست پیام نمایش داده می شود.", "user.settings.display.availabilityStatusOnPostsTitle": "نمایش در دسترس بودن آنلاین در تصاویر نمایه", "user.settings.display.channelDisplayTitle": "نمایش کانال", "user.settings.display.channeldisplaymode": "عرض کانال مرکزی را انتخاب کنید.", "user.settings.display.clickToReply": "برای باز کردن موضوعات کلیک کنید", "user.settings.display.clickToReplyDescription": "وقتی فعال شد، روی هر نقطه از پیام کلیک کنید تا رشته پاسخ باز شود", "user.settings.display.clockDisplay": "نمایشگر ساعت", "user.settings.display.collapseDesc": "تنظیم کنید که آیا پیش‌نمایش پیوندهای تصویر و ریز عکس‌های پیوست تصویر به‌طور پیش‌فرض به‌صورت بازشده یا جمع‌شده نشان داده شوند. این تنظیم را می توان با استفاده از دستورات اسلش /expand و /collapse نیز کنترل کرد.", "user.settings.display.collapseDisplay": "ظاهر پیش‌فرض پیش‌نمایش‌های تصویر", "user.settings.display.collapseOff": "فرو ریخت", "user.settings.display.collapseOn": "من<PERSON><PERSON><PERSON>", "user.settings.display.collapsedReplyThreadsDescription": "وقتی فعال باشد، پاسخ پیام‌ها در کانال نشان داده نمی‌شوند و بحث‌هایی که دنبال می‌کنید در نمای «مباحثات» به شما اطلاع داده می‌شود.", "user.settings.display.collapsedReplyThreadsOff": "خاموش", "user.settings.display.collapsedReplyThreadsOn": "بر", "user.settings.display.collapsedReplyThreadsTitle": "موضوعات پاسخ جمع شده (بتا)", "user.settings.display.colorize": "رنگی کردن نام‌های کاربری", "user.settings.display.fixedWidthCentered": "عرض ثابت، در مرکز", "user.settings.display.fullScreen": "تمام عرض", "user.settings.display.icon": "نماد تنظیمات نمایشگر", "user.settings.display.language": "زبان", "user.settings.display.lastActiveOff": "خاموش", "user.settings.display.lastActiveOn": "روشن", "user.settings.display.linkPreviewDesc": "در صورت موجود بودن، اولین پیوند وب در یک پیام، پیش نمایشی از محتوای وب سایت را در زیر پیام نشان می دهد.", "user.settings.display.linkPreviewDisplay": "پیش نمایش لینک وب سایت", "user.settings.display.linkPreviewOff": "خاموش", "user.settings.display.linkPreviewOn": "بر", "user.settings.display.messageDisplayClean": "استاندارد", "user.settings.display.messageDisplayCleanDes": "اسکن و خواندن آسان.", "user.settings.display.messageDisplayCompact": "فشرده - جمع و جور", "user.settings.display.messageDisplayCompactDes": "تا جایی که می‌توانیم پیام‌های زیادی را روی صفحه قرار دهیم.", "user.settings.display.messageDisplayDescription": "نحوه نمایش پیام های یک کانال را انتخاب کنید.", "user.settings.display.messageDisplayTitle": "نمایش پیام", "user.settings.display.militaryClock": "ساعت 24 ساعته (مثال: 16:00)", "user.settings.display.normalClock": "ساعت 12 ساعته (مثال: 4:00 بعد از ظهر)", "user.settings.display.oneClickReactionsOnPostsDescription": "هنگامی که فعال است، می‌توانید به سرعت با واکنش‌هایی که اخیراً استفاده شده‌اند، هنگام قرار دادن ماوس روی پیام واکنش نشان دهید.", "user.settings.display.oneClickReactionsOnPostsTitle": "واکنش سریع به پیام ها", "user.settings.display.preferTime": "نحوه نمایش زمان را ترجیح می دهید.", "user.settings.display.teammateNameDisplay": "این فیلد از طریق مدیر سیستم شما مدیریت می شود. اگر می خواهید آن را تغییر دهید، باید این کار را از طریق مدیر سیستم خود انجام دهید.", "user.settings.display.teammateNameDisplayDescription": "نحوه نمایش نام سایر کاربران در پست ها و لیست پیام های مستقیم را تنظیم کنید.", "user.settings.display.teammateNameDisplayFullname": "نمایش نام و نام خانوادگی", "user.settings.display.teammateNameDisplayNicknameFullname": "در صورت وجود نام مستعار را نشان دهید، در غیر این صورت نام و نام خانوادگی را نشان دهید", "user.settings.display.teammateNameDisplayTitle": "نمایش نام هم تیمی", "user.settings.display.teammateNameDisplayUsername": "نمایش نام کاربری", "user.settings.display.theme.applyToAllTeams": "تم جدید را برای همه تیم های من اعمال کنید", "user.settings.display.theme.customTheme": "تم سفارشی", "user.settings.display.theme.describe": "برای مدیریت طرح زمینه خود باز کنید", "user.settings.display.theme.otherThemes": "تم های دیگر را ببینید", "user.settings.display.theme.themeColors": "رنگ های تم", "user.settings.display.theme.title": "موضوع", "user.settings.display.timezone": "منط<PERSON>ه زمانی", "user.settings.display.title": "تنظیمات نمایشگر", "user.settings.general.close": "نزدیک", "user.settings.general.confirmEmail": "ایمیل تایید", "user.settings.general.currentEmail": "ایمیل فعلی", "user.settings.general.currentPassword": "ر<PERSON>ز عبور فعلی", "user.settings.general.email": "پست الکترونیک", "user.settings.general.emailGitlabCantUpdate": "ورود از طریق GitLab انجام می شود. ایمیل را نمی توان به روز کرد. آدرس ایمیل استفاده شده برای اعلان ها {email} است.", "user.settings.general.emailGoogleCantUpdate": "ورود از طریق گوگل انجام می شود. ایمیل را نمی توان به روز کرد. آدرس ایمیل استفاده شده برای اعلان ها {email} است.", "user.settings.general.emailHelp1": "ایمیل برای ورود به سیستم، اعلان ها و بازنشانی رمز عبور استفاده می شود. ایمیل در صورت تغییر نیاز به تأیید دارد.", "user.settings.general.emailHelp3": "ایمیل برای ورود به سیستم، اعلان ها و بازنشانی رمز عبور استفاده می شود.", "user.settings.general.emailLdapCantUpdate": "ورود از طریق AD/LDAP انجام می شود. ایمیل را نمی توان به روز کرد. آدرس ایمیل استفاده شده برای اعلان ها {email} است.", "user.settings.general.emailMatch": "ایمیل های جدیدی که وارد کرده اید مطابقت ندارند.", "user.settings.general.emailOffice365CantUpdate": "ورود از طریق Office 365 انجام می شود. ایمیل را نمی توان به روز کرد. آدرس ایمیل استفاده شده برای اعلان ها {email} است.", "user.settings.general.emailSamlCantUpdate": "ورود از طریق SAML انجام می شود. ایمیل را نمی توان به روز کرد. آدرس ایمیل استفاده شده برای اعلان ها {email} است.", "user.settings.general.emptyName": "برای افزودن نام کامل خود روی «ویرایش» کلیک کنید", "user.settings.general.emptyNickname": "برای افزودن نام مستعار روی «ویرایش» کلیک کنید", "user.settings.general.emptyPassword": "لطفا رمز عبور فعلی خود را وارد کنید.", "user.settings.general.emptyPosition": "برای افزودن عنوان / موقعیت شغلی خود روی 'ویرایش' کلیک کنید", "user.settings.general.field_handled_externally": "این فیلد از طریق ارائه دهنده ورود به سیستم شما مدیریت می شود. اگر می خواهید آن را تغییر دهید، باید این کار را از طریق ارائه دهنده ورود خود انجام دهید.", "user.settings.general.firstName": "نام کوچک", "user.settings.general.fullName": "نام و نام خانوادگی", "user.settings.general.imageTooLarge": "بارگذاری تصویر نمایه امکان پذیر نیست. پرونده خیلی بزرگ است.", "user.settings.general.imageUpdated": "آخرین به روز رسانی تصویر {date}", "user.settings.general.incorrectPassword": "رمز عبور شما نادرست است", "user.settings.general.lastName": "نام خانوادگی", "user.settings.general.loginGitlab": "ورود از طریق GitLab انجام شد ({email})", "user.settings.general.loginGoogle": "ورود از طریق Google انجام شد ({email})", "user.settings.general.loginLdap": "ورود از طریق AD/LDAP انجام شد ({email})", "user.settings.general.loginOffice365": "ورود از طریق Office 365 انجام شد ({email})", "user.settings.general.loginSaml": "ورود از طریق SAML ({email}) انجام شد", "user.settings.general.mobile.emptyName": "برای افزودن نام کامل خود کلیک کنید", "user.settings.general.mobile.emptyNickname": "برای افزودن نام مستعار کلیک کنید", "user.settings.general.mobile.emptyPosition": "برای افزودن عنوان / موقعیت شغلی خود کلیک کنید", "user.settings.general.mobile.uploadImage": "برای آپلود تصویر کلیک کنید", "user.settings.general.newEmail": "ایمی<PERSON> جدید", "user.settings.general.nickname": "کنیه", "user.settings.general.nicknameExtra": "از نام مستعار برای نامی که ممکن است با نام کاربری و نام کاربری شما متفاوت باشد استفاده کنید. این بیشتر زمانی استفاده می‌شود که دو یا چند نفر نام‌ها و نام‌های کاربری مشابه دارند.", "user.settings.general.notificationsExtra": "به‌طور پیش‌فرض، زمانی که شخصی نام کوچک شما را تایپ می‌کند، اعلان‌های ذکر را دریافت خواهید کرد. برای تغییر این پیش‌فرض به تنظیمات {notify} بروید.", "user.settings.general.notificationsLink": "اطلاعیه", "user.settings.general.position": "موقعیت", "user.settings.general.positionExtra": "از Position برای نقش یا عنوان شغلی خود استفاده کنید. این در popover نمایه شما نشان داده خواهد شد.", "user.settings.general.profilePicture": "عکس پروفایل", "user.settings.general.sendAgain": "دوباره بفرست", "user.settings.general.sending": "در حال ارسال", "user.settings.general.uploadImage": "برای آپلود تصویر روی \"ویرایش\" کلی<PERSON> کنید.", "user.settings.general.username": "نام کاربری", "user.settings.general.usernameInfo": "چیزی را انتخاب کنید که به راحتی هم تیمی ها تشخیص دهند و به خاطر بیاورند.", "user.settings.general.usernameReserved": "این نام کاربری رزرو شده است، لطفا نام کاربری جدید را انتخاب کنید.", "user.settings.general.usernameRestrictions": "نام کاربری باید با یک حرف شروع شود و بین {min} تا {max} کاراکتر کوچک متشکل از اعداد، حروف و نمادهای '.'، '-' و '_' باشد.", "user.settings.general.validEmail": "لطفا یک آدرس ایمیل معتبر وارد کنید", "user.settings.general.validImage": "فقط می توان از تصاویر BMP، JPG یا PNG برای تصاویر نمایه استفاده کرد", "user.settings.languages.change": "تغییر زبان رابط", "user.settings.mfa.add": "MFA را به حساب اضافه کنید", "user.settings.mfa.addHelp": "افزودن احراز هویت چند عاملی با درخواست کد از تلفن همراه هر بار که وارد سیستم می شوید، حساب شما را ایمن تر می کند.", "user.settings.mfa.remove": "MFA ر<PERSON> از حساب حذف کنید", "user.settings.mfa.removeHelp": "حذف احراز هویت چند عاملی به این معنی است که دیگر برای ورود به حساب خود نیازی به رمز عبور مبتنی بر تلفن ندارید.", "user.settings.mfa.requiredHelp": "احراز هویت چند عاملی در این سرور مورد نیاز است. بازنشانی تنها زمانی توصیه می‌شود که بخواهید تولید کد را به یک دستگاه تلفن همراه جدید تغییر دهید. از شما خواسته می شود که بلافاصله آن را دوباره تنظیم کنید.", "user.settings.mfa.reset": "بازنشانی MFA در حساب", "user.settings.mfa.title": "احراز هویت چند عاملی", "user.settings.modal.advanced": "پیشرفته", "user.settings.modal.confirmBtns": "بله، رد کنید", "user.settings.modal.confirmMsg": "شما تغییرات ذخیره نشده ای دارید، آیا مطمئن هستید که می خواهید آنها را نادیده بگیرید؟", "user.settings.modal.confirmTitle": "لغو تغییرات؟", "user.settings.modal.display": "نمایش دادن", "user.settings.modal.notifications": "اطلاعیه", "user.settings.modal.profile": "تنظیمات پروفایل", "user.settings.modal.security": "امنیت", "user.settings.modal.sidebar": "نوار کناری", "user.settings.modal.title": "مشخصات", "user.settings.notifications.autoResponder": "پاس<PERSON> خودکار پیام مستقیم", "user.settings.notifications.autoResponderDefault": "سلام من خارج از دفتر هستم و نمی توانم به پیام ها پاسخ دهم.", "user.settings.notifications.autoResponderDisabled": "معلول", "user.settings.notifications.autoResponderEnabled": "فعال شد", "user.settings.notifications.autoResponderHint": "یک پیام سفارشی تنظیم کنید که به طور خودکار در پاسخ به پیام های مستقیم ارسال شود. ذکر در کانال های عمومی و خصوصی باعث پاسخ خودکار نمی شود. فعال کردن پاسخ‌های خودکار وضعیت شما را در حالت خارج از دفتر تنظیم می‌کند و اعلان‌های ایمیل و فشار را غیرفعال می‌کند.", "user.settings.notifications.autoResponderPlaceholder": "پیام", "user.settings.notifications.channelWide": "در سراسر کانال، \"@channel\"، \"@all\"، \"@here\" را ذکر می‌کند", "user.settings.notifications.comments": "اعلان های پاسخ", "user.settings.notifications.commentsAny": "اعلان‌ها را در پیام‌هایی در رشته‌های پاسخی که من شروع می‌کنم یا در آن شرکت می‌کنم، فعال کنید", "user.settings.notifications.commentsInfo": "علاوه بر اعلان‌ها برای زمانی که از شما نام برده می‌شود، انتخاب کنید که آیا می‌خواهید اعلان‌هایی در رشته‌های پاسخ دریافت کنید.", "user.settings.notifications.commentsNever": "اعلان‌ها را در پیام‌های موجود در رشته‌های پاسخ فعال نکنید، مگر اینکه از من نام برده شود", "user.settings.notifications.commentsRoot": "اعلان‌ها را در پیام‌هایی در رشته‌هایی که شروع می‌کنم فعال کنید", "user.settings.notifications.email.disabled": "اعلان های ایمیل فعال نیستند", "user.settings.notifications.email.disabled_long": "اعلان‌های ایمیل توسط سرپرست سیستم شما فعال نشده است.", "user.settings.notifications.email.everyHour": "هر ساعت", "user.settings.notifications.email.everyXMinutes": "هر {count, plural, one {minute} other {{count, number} دقیقه}}", "user.settings.notifications.email.immediately": "بلافاصله. مستقیما", "user.settings.notifications.email.never": "هر<PERSON><PERSON>", "user.settings.notifications.email.send": "ارسال اعلان های ایمیل", "user.settings.notifications.emailBatchingInfo": "اعلان های دریافت شده در بازه زمانی انتخاب شده ترکیب شده و در یک ایمیل ارسال می شود.", "user.settings.notifications.emailInfo": "زمانی که بیش از 5 دقیقه آفلاین هستید یا دور هستید، اعلان‌های ایمیل برای اشاره و پیام‌های مستقیم ارسال می‌شوند.", "user.settings.notifications.emailNotifications": "اعلان های ایمیل", "user.settings.notifications.header": "اطلاعیه", "user.settings.notifications.icon": "نماد تنظیمات اعلان", "user.settings.notifications.sensitiveName": "نام کوچک شما که به حروف کوچک و بزرگ حساس است \"{first_name}\"", "user.settings.notifications.sensitiveUsername": "نام کاربری غیر حساس شما \"{username}\"", "user.settings.notifications.title": "تنظیمات اعلان", "user.settings.profile.icon": "نماد تنظیمات نمایه", "user.settings.security.active": "فعال", "user.settings.security.close": "نزدیک", "user.settings.security.currentPassword": "ر<PERSON>ز عبور فعلی", "user.settings.security.currentPasswordError": "لطفا رمز عبور فعلی خود را وارد کنید.", "user.settings.security.deauthorize": "لغو مجوز", "user.settings.security.emailPwd": "ایمیل و رمز عبور", "user.settings.security.gitlab": "GitLab", "user.settings.security.google": "گوگل", "user.settings.security.icon": "نماد تنظیمات امنیتی", "user.settings.security.inactive": "غیر فعال", "user.settings.security.lastUpdated": "آخرین به روز رسانی {date} در {time}", "user.settings.security.ldap": "AD/LDAP", "user.settings.security.loginGitlab": "ورود از طریق GitLab انجام شد", "user.settings.security.loginGoogle": "ورود از طریق Google Apps انجام شد", "user.settings.security.loginLdap": "ورود از طریق AD/LDAP انجام شد", "user.settings.security.loginOffice365": "ورود از طریق Office 365 انجام شد", "user.settings.security.loginSaml": "ورود از طریق SAML انجام شد", "user.settings.security.logoutActiveSessions": "مشاهده و خروج از Active Sessions", "user.settings.security.logoutActiveSessions.icon": "نماد جلسات فعال", "user.settings.security.method": "روش ورود به سیستم", "user.settings.security.newPassword": "<PERSON><PERSON><PERSON> عبور جدید", "user.settings.security.noApps": "هیچ برنامه OAuth 2.0 مجاز نیست.", "user.settings.security.oauthApps": "برنامه های OAuth 2.0", "user.settings.security.oauthAppsDescription": "برای مدیریت برنامه‌های OAuth 2.0 روی «ویرایش» کلیک کنید", "user.settings.security.oauthAppsHelp": "برنامه ها از طرف شما برای دسترسی به داده های شما بر اساس مجوزهایی که به آنها می دهید عمل می کنند.", "user.settings.security.office365": "دفتر 365", "user.settings.security.oneSignin": "ممکن است هر بار فقط یک روش ورود به سیستم داشته باشید. تغییر روش ورود به سیستم، ایمیلی را ارسال می کند که در صورت موفقیت آمیز بودن تغییر به شما اطلاع می دهد.", "user.settings.security.openid": "OpenID", "user.settings.security.password": "کلمه عبور", "user.settings.security.passwordError": "رمز عبور شما باید بین {min} و {max} کاراکتر باشد.", "user.settings.security.passwordErrorLowercase": "رمز عبور شما باید بین {min} و {max} نویسه باشد که حداقل از یک حرف کوچک تشکیل شده باشد.", "user.settings.security.passwordErrorLowercaseNumber": "رمز عبور شما باید بین {min} تا {max} نویسه باشد که حداقل از یک حرف کوچک و حداقل یک عدد تشکیل شده باشد.", "user.settings.security.passwordErrorLowercaseNumberSymbol": "گذرواژه شما باید دارای بین {min} و {max} نویسه باشد که حداقل از یک حرف کوچک، حداقل یک عدد و حداقل یک نماد تشکیل شده باشد (به عنوان مثال \"~!@#$%^&*()\").", "user.settings.security.passwordErrorLowercaseSymbol": "گذرواژه شما باید بین {min} و {max} نویسه باشد که حداقل از یک حرف کوچک و حداقل یک نماد تشکیل شده باشد (به عنوان مثال \"~!@#$%^&*()\").", "user.settings.security.passwordErrorLowercaseUppercase": "رمز عبور شما باید بین {min} و {max} نویسه باشد که حداقل از یک حرف کوچک و حداقل یک حرف بزرگ تشکیل شده باشد.", "user.settings.security.passwordErrorLowercaseUppercaseNumber": "رمز عبور شما باید بین {min} و {max} نویسه باشد که حداقل از یک حرف کوچک، حداقل یک حرف بزرگ و حداقل یک عدد تشکیل شده باشد.", "user.settings.security.passwordErrorLowercaseUppercaseNumberSymbol": "رمز عبور شما باید بین {min} و {max} کاراکتر متشکل از حداقل یک حرف کوچک، حداقل یک حرف بزرگ، حداقل یک عدد و حداقل یک علامت (به عنوان مثال \"~!@#$%^&* باشد) باشد. ()\").", "user.settings.security.passwordErrorLowercaseUppercaseSymbol": "گذرواژه شما باید دارای بین {min} و {max} نویسه باشد که حداقل از یک حرف کوچک، حداقل یک حرف بزرگ و حداقل یک نماد تشکیل شده باشد (به عنوان مثال \"~!@#$%^&*()\").", "user.settings.security.passwordErrorNumber": "رمز عبور شما باید بین {min} تا {max} نویسه باشد که حداقل از یک عدد تشکیل شده باشد.", "user.settings.security.passwordErrorNumberSymbol": "گذرواژه شما باید بین {min} و {max} کاراکتر باشد که حداقل از یک عدد و حداقل یک نماد تشکیل شده باشد (به عنوان مثال \"~!@#$%^&*()\").", "user.settings.security.passwordErrorSymbol": "رمز عبور شما باید بین {min} و {max} کاراکتر باشد که حداقل از یک نماد تشکیل شده است (به عنوان مثال \"~!@#$%^&*()\").", "user.settings.security.passwordErrorUppercase": "رمز عبور شما باید بین {min} تا {max} نویسه باشد که حداقل از یک حرف بزرگ تشکیل شده باشد.", "user.settings.security.passwordErrorUppercaseNumber": "رمز عبور شما باید بین {min} و {max} نویسه باشد که حداقل از یک حرف بزرگ و حداقل یک عدد تشکیل شده باشد.", "user.settings.security.passwordErrorUppercaseNumberSymbol": "گذرواژه شما باید بین {min} و {max} نویسه متشکل از حداقل یک حرف بزرگ، حداقل یک عدد و حداقل یک نماد باشد (به عنوان مثال \"~!@#$%^&*()\").", "user.settings.security.passwordErrorUppercaseSymbol": "رمز عبور شما باید بین {min} و {max} کاراکتر باشد که حداقل از یک حرف بزرگ و حداقل یک نماد تشکیل شده باشد (به عنوان مثال \"~!@#$%^&*()\").", "user.settings.security.passwordGitlabCantUpdate": "ورود از طریق GitLab انجام می شود. رمز عبور را نمی توان به روز کرد.", "user.settings.security.passwordGoogleCantUpdate": "ورود از طریق Google Apps انجام می شود. رمز عبور را نمی توان به روز کرد.", "user.settings.security.passwordLdapCantUpdate": "ورود از طریق AD/LDAP انجام می شود. رمز عبور را نمی توان به روز کرد.", "user.settings.security.passwordMatchError": "رمزهای عبور جدیدی که وارد کردید مطابقت ندارند.", "user.settings.security.passwordMinLength": "حداقل طول نامعتبر است، نمی توان پیش نمایش را نشان داد.", "user.settings.security.passwordOffice365CantUpdate": "ورود از طریق Office 365 انجام می شود. رمز عبور را نمی توان به روز کرد.", "user.settings.security.passwordSamlCantUpdate": "این فیلد از طریق ارائه دهنده ورود به سیستم شما مدیریت می شود. اگر می خواهید آن را تغییر دهید، باید این کار را از طریق ارائه دهنده ورود خود انجام دهید.", "user.settings.security.retypePassword": "رمز عبور جدید را دوباره تایپ کنید", "user.settings.security.saml": "SAML", "user.settings.security.switchEmail": "به استفاده از ایمیل و رمز عبور بروید", "user.settings.security.switchGitlab": "به استفاده از GitLab SSO بروید", "user.settings.security.switchGoogle": "به استفاده از Google SSO بروید", "user.settings.security.switchLdap": "به استفاده از AD/LDAP بروید", "user.settings.security.switchOffice365": "به استفاده از Office 365 SSO بروید", "user.settings.security.switchSaml": "به استفاده از SAML SSO بروید", "user.settings.security.title": "تنظیمات امنیتی", "user.settings.security.viewHistory": "مشاهده تاریخچه دسترسی", "user.settings.security.viewHistory.icon": "نماد تاریخچه دسترسی", "user.settings.sidebar.icon": "نماد تنظیمات نوار کناری", "user.settings.sidebar.off": "خاموش", "user.settings.sidebar.on": "بر", "user.settings.sidebar.recent": "فعالیت اخیر", "user.settings.sidebar.sortAlpha": "بر اساس حروف الفبا", "user.settings.sidebar.title": "تنظیمات نوار کناری", "user.settings.timezones.automatic": "<PERSON>و<PERSON><PERSON>ار", "user.settings.timezones.promote": "منطقه زمانی مورد استفاده برای مهرهای زمانی در رابط کاربری و اعلان‌های ایمیل را انتخاب کنید.", "user.settings.tokens.activate": "فعال کردن", "user.settings.tokens.cancel": "انصراف", "user.settings.tokens.clickToEdit": "برای مدیریت نشانه های دسترسی شخصی خود، روی «ویرایش» کلیک کنید", "user.settings.tokens.confirmCopyButton": "بله، توکن را کپی کردم", "user.settings.tokens.confirmCopyMessage": "مطمئن شوید که رمز دسترسی زیر را کپی و ذخیره کرده اید. دیگر نمی توانید آن را ببینید!", "user.settings.tokens.confirmCopyTitle": "توکن خود را کپی کردید؟", "user.settings.tokens.confirmCreateButton": "بله، ایجاد کنید", "user.settings.tokens.confirmCreateMessage": "شما در حال ایجاد یک نشانه دسترسی شخصی با مجوزهای مدیریت سیستم هستید. آیا مطمئن هستید که می خواهید این توکن را ایجاد کنید؟", "user.settings.tokens.confirmCreateTitle": "رمز دسترسی شخصی ادمین سیستم را ایجاد کنید", "user.settings.tokens.confirmDeleteButton": "بله حذف کنید", "user.settings.tokens.confirmDeleteTitle": "رمز حذف شود؟", "user.settings.tokens.copy": "لطفا رمز دسترسی زیر را کپی کنید. دیگر نمی توانید آن را ببینید!", "user.settings.tokens.create": "توکن ایجاد کنید", "user.settings.tokens.deactivate": "غیر فعال کردن", "user.settings.tokens.deactivatedWarning": "(معلول)", "user.settings.tokens.delete": "<PERSON><PERSON><PERSON>", "user.settings.tokens.description": "<linkTokens>توکن‌های دسترسی شخصی</linkTokens> مشابه توکن‌های جلسه عمل می‌کنند و می‌توانند توسط ادغام‌ها برای <linkAPI>تأیید هویت در برابر REST API</linkAPI> استفاده شوند .", "user.settings.tokens.description_mobile": "<linkTokens>توکن‌های دسترسی شخصی</linkTokens> مشابه توکن‌های جلسه عمل می‌کنند و می‌توانند توسط ادغام‌ها برای <linkAPI>تأیید هویت در برابر REST API</linkAPI> استفاده شوند . توکن های جدید روی دسکتاپ خود ایجاد کنید.", "user.settings.tokens.id": "شناسه رمز: ", "user.settings.tokens.name": "توضیحات توکن: ", "user.settings.tokens.nameHelp": "توضیحی برای توکن خود وارد کنید تا به خاطر بیاورید که چه کاری انجام می دهد.", "user.settings.tokens.nameRequired": "لطفا یک توضیح وارد کنید", "user.settings.tokens.save": "صرفه جویی", "user.settings.tokens.title": "توکن های دسترسی شخصی", "user.settings.tokens.token": "نشانه دسترسی: ", "user.settings.tokens.tokenDesc": "توضیحات توکن: ", "user.settings.tokens.tokenId": "شناسه رمز: ", "user.settings.tokens.tokenLoading": "بارگذاری...", "user.settings.tokens.userAccessTokensNone": "بدون نشانه دسترسی شخصی", "userGuideHelp.askTheCommunity": "از جامعه بپرسید", "userGuideHelp.helpResources": "منابع کمکی", "userGuideHelp.keyboardShortcuts": "میانبرهای صفحه کلید", "userGuideHelp.reportAProblem": "گزارش یک مشکل", "user_group_popover.close": "بستن", "user_group_popover.searchGroupMembers": "جست‌وجوی اعضا", "user_groups_modal.addPeople": "افزودن افراد", "user_groups_modal.allGroups": "همه گروه‌ها", "user_groups_modal.archiveGroup": "بایگانی گروه", "user_groups_modal.createNew": "افزودن گروه", "user_groups_modal.createTitle": "افزودن گروه", "user_groups_modal.editDetails": "ویرایش جزئیات", "user_groups_modal.editGroupTitle": "ویرایش جزئیات گروه", "user_groups_modal.filterAriaLabel": "فهرست صافی‌های گروه‌ها", "user_groups_modal.goBackLabel": "<PERSON><PERSON><PERSON>", "user_groups_modal.joinGroup": "پیوستن به گروه", "user_groups_modal.leaveGroup": "ترک گروه", "user_groups_modal.mention": "اشاره", "user_groups_modal.myGroups": "گروه‌های من", "user_groups_modal.name": "نام", "user_groups_modal.searchGroups": "جست‌وجوی گروه‌ها", "user_groups_modal.showAllGroups": "نمایش: همه‌ی گروه‌ها", "user_groups_modal.showMyGroups": "نمایش: گروه‌های من", "user_groups_modal.title": "کاربر گروه‌ها", "user_groups_modal.viewGroup": "نمایش گروه", "user_list.notFound": "کاربری پیدا نشد", "user_profile.account.editProfile": "ویرایش نمایه", "user_profile.account.localTime": "<PERSON><PERSON><PERSON> محلی", "user_profile.account.post_was_created": "این پست با ادغام از ایجاد شده است", "user_profile.add_user_to_channel": "به یک کانال اضافه کنید", "user_profile.custom_status": "وضعیت", "user_profile.custom_status.set_status": "تنظیم یک وضعیت", "user_profile.send.dm": "پیام", "version_bar.new": "نسخه جدید Mattermost در دسترس است.", "version_bar.refresh": "اکنون برنامه را رفرش کنید", "view_image.loading": "بارگذاری ", "view_image.zoom_in": "بزرگ‌نمایی", "view_image.zoom_out": "کوچک‌نمایی", "view_image.zoom_reset": "تنظیم‌دوباره بزرگ‌نمایی", "view_image_popover.download": "د<PERSON><PERSON><PERSON>د", "view_image_popover.publicLink": "یک لینک عمومی دریافت کنید", "view_user_group_modal.ldapSynced": "AD/LDAD همگام شد", "web.footer.about": "در باره", "web.footer.help": "کمک", "web.footer.privacy": "<PERSON>ری<PERSON> خصوصی", "web.footer.terms": "مقررات", "web.header.back": "بازگشت", "web.header.logout": "خروج", "web.root.signup_info": "همه ارتباطات تیمی در یک مکان، قابل جستجو و در دسترس در هر نقطه", "webapp.mattermost.feature.guest_accounts": "حساب‌کاربری‌های مهمان", "webapp.mattermost.feature.playbooks_retro": "مروری بر اجرانامه‌ها", "webapp.mattermost.feature.start_call": "شروع تماس", "webapp.mattermost.feature.unlimited_messages": "پیام‌های نامحدود", "webapp.mattermost.feature.upgrade_downgraded_workspace": "بازگردانی محیط کار به طرح پرداخت‌شده", "widget.passwordInput.password": "<PERSON><PERSON><PERSON>", "widgets.channels_input.empty": "کانالی پیدا نشد", "widgets.channels_input.loading": "بارگذاری", "widgets.users_emails_input.loading": "بارگذاری", "widgets.users_emails_input.no_user_found_matching": "هیچکس مطابق **{text}** پیدا نشد. برای دعوت از آنها ایمیل آنها را وارد کنید.", "widgets.users_emails_input.valid_email": "افزودن **{email}**", "workspace_limits.archived_file.archived_compact": "(بایگانی‌شده)", "workspace_limits.file_storage": "مخزن پرونده", "workspace_limits.file_storage.short": "پرونده‌ها", "workspace_limits.menu_limit.messages": "جمع پیام‌ها", "workspace_limits.menu_limit.notify_admin": "اطلاع به سرپرست", "workspace_limits.menu_limit.view_plans": "مشاهده ط", "workspace_limits.message_history": "تاریخچه پیام", "workspace_limits.message_history.locked.cta.admin": "الان ارتقا بده", "workspace_limits.message_history.locked.cta.end_user": "اطلاع به سرپرست", "workspace_limits.message_history.short": "پیام‌ها", "workspace_limits.modals.close": "بستن", "workspace_limits.modals.informational.title": "محدودیت‌های {planName}", "workspace_limits.modals.limits_reached.title.message_history": "تاریخچه پیام", "workspace_limits.modals.view_plans": "نمایش طرح‌ها", "workspace_limits.search_limit.upgrade_now": "الان ارتقا بده", "workspace_limits.search_limit.view_plans": "مشاهده طرح‌ها", "yourcomputer": "کامپیوتر شما"}