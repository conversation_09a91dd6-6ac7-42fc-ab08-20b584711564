.UploadLicenseModal {
    .modal-header {
        border-bottom: none !important;
        background: none !important;

        button.close {
            span {
                color: rgba(63, 67, 80, 0.75) !important;
            }
        }
    }

    .modal-body {
        padding-top: 0 !important;
        padding-bottom: 24px !important;
    }

    .content-body {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .svg-image {
            margin-top: -20px;
        }

        .hands-svg {
            margin-top: 0;
            margin-bottom: 15px;
        }

        .title {
            margin: 10px 0;
            color: #3f4350;
            font-family: Metropolis;
            font-size: 22px;
            font-weight: 700;
            line-height: 28px;

            html:lang(ar) & {
                font-family: 'GraphikArabic', sans-serif;
            }
        }

        .subtitle {
            width: 90%;
            color: #3f4350;
            font-size: 14px;
            font-style: normal;
            font-weight: normal;
            line-height: 20px;
            text-align: center;
        }

        .file-upload {
            width: 90%;
            padding: 12px;
            border: 1px solid rgba(63, 67, 80, 0.32);
            border-radius: 4px;
            margin-top: 15px;

            &__titleSection {
                color: rgba(63, 67, 80, 0.75);
                font-size: 12px;
                font-weight: bold;
                line-height: 16px;
            }

            &__inputSection {
                display: flex;
                justify-content: space-between;
                margin-top: 10px;

                .file-name-section {
                    display: flex;

                    svg {
                        margin-right: 5px;
                    }

                    .file-size {
                        margin-left: 20px;
                    }
                }

                .file__upload {
                    position: relative;
                    display: flex;
                    align-self: flex-end;
                    html[dir="rtl"] & {
                        margin-right: auto;
                    }
                
                    html[dir="ltr"] & {
                        margin-left: auto;
                    }
                    a {
                        color: var(--sofa-color);
                        font-weight: 600;
                    }

                    input {
                        position: absolute;
                        z-index: 5;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        padding-left: 100%;
                        cursor: pointer;

                        &[disabled] {
                            cursor: not-allowed;
                        }
                    }
                }
            }
        }

        .serverError {
            display: flex;
            width: 90%;
            margin-top: 5px;
            color: var(--error-text);
            float: left;

            i {
                display: inline;
                margin-right: 5px;
            }

            span.server-error-text {
                display: inline-flex;
            }
        }
    }

    .content-footer {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-top: 20px;

        button {
            height: 48px;
            padding: 5px 24px;
            border: 0 !important;
            border-radius: 4px;
            margin-top: 15px !important;
            font-size: 16px;
            font-weight: 600;
            line-height: 18px;
        }
    }

    .modal-footer {
        border-top: none !important;
        background: none !important;
    }
}
