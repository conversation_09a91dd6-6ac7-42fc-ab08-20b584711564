.error {
    color: var(--sys-error-text);
}

.info {
    color: var(--sofa-color);
}

.warning {
    color: var(--sys-away-indicator-dark);
}

.ok {
    color: var(--sys-online-indicator);
}

.scoreGraph {
    display: flex;
    width: 112px;
    height: 112px;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.WorkspaceOptimizationDashboard {

    button[class*=' StyledChip'],
    button[class^='StyledChip'] {
        width: 120px;
        height: 24px;
        justify-content: space-around;
        border: none;
        margin-bottom: 0;
        box-shadow: none;
        font-size: 12px;
        font-style: normal;
        font-weight: bold;
        line-height: 16px;
        text-align: center;
        &:dir(ltr) {
            margin-right: 8px;}
        &:dir(rtl) {
            margin-left: 8px;}

        &.error {
            @extend .error;

            background-color: rgba(var(--sys-error-text-color-rgb), 0.16);
        }

        &.info {
            @extend .info;

            background-color: rgba(rgb(0 152 126), 0.16);
        }

        &.warning {
            @extend .warning;

            background-color: rgba(var(--sys-away-indicator-rgb), 0.16);
        }

        &.ok {
            @extend .ok;

            background-color: rgba(var(--sys-online-indicator-rgb), 0.16);
        }
    }

    div.OverallScore {
        display: flex;
        width: 872px;
        box-sizing: border-box;
        align-items: center;
        padding: 24px;
        border: 1px solid rgba(var(--sys-denim-center-channel-text-rgb), 0.08) !important;
        border-radius: 4px;
        
        background: var(--sys-center-channel-bg);
        box-shadow: var(--elevation-1);
        &:dir(ltr) {
            margin-left: 4px;}
        &:dir(rtl) {
            margin-right: 4px;}
        &__scoreEllipseSvg {
            display: flex;
            width: 140px;
            height: 140px;
            align-items: center;

            .alertImageScore {
                @extend .scoreGraph;

                background-color: rgba(var(--sys-error-text-color-rgb), 0.3);
            }

            .successImageScore {
                @extend .scoreGraph;

                background-color: rgba(var(--sys-online-indicator-rgb), 0.3);
            }

            .CircularChart {
                .circular-chart {
                    display: block;
                    margin: 5px auto;

                    &.warning .circle {
                        stroke: var(--sys-away-indicator);
                    }

                    &.success .circle {
                        stroke: var(--sys-online-indicator);
                    }

                    &.info .circle {
                        stroke: var(--sys-sidebar-text-active-border);
                    }

                    &.error .circle {
                        stroke: var(--sys-error-text);
                    }

                    .circle {
                        animation: progress 1s ease-out forwards;
                        fill: none;
                        stroke-width: 3.55;
                    }

                    .percentageOrNumber {
                        font-family: Metropolis;
                        font-size: 0.65em;
                        font-weight: 550;

                        html:lang(ar) & {
                            font-family: 'GraphikArabic', sans-serif;
                        }
                    }
                }

                @keyframes progress {
                    0% {
                        stroke-dasharray: 0 100;
                    }
                }
            }
        }

        &__content {
            display: flex;
            flex-direction: column;
            &:dir(ltr) {
                margin-left: 24px;}
            &:dir(rtl) {
                margin-right: 24px;}

            &__title {
                margin-bottom: 8px;
                color: var(--sys-denim-center-channel-text);
                font-family: 'GraphikArabic', sans-serif;
                font-size: 20px;
                font-weight: 700;
                line-height: 28px;

                html:lang(ar) & {
                    font-family: 'GraphikArabic', sans-serif;
                }
            }

            &__description {
                margin-bottom: 24px;
                color: var(--sys-denim-center-channel-text);
                font-size: 14px;
                font-style: normal;
                font-weight: normal;
                line-height: 20px;
            }

            &__chips {
                display: flex;
                flex-direction: row;

                button[class*=' StyledChip'],
                button[class^='StyledChip'] {
                    width: 180px;
                    height: 32px;
                   
                    &:dir(ltr) {
                        margin-right: 16px;}
                    &:dir(rtl) {
                        margin-left: 16px;}
                }
            }
        }
    }

    ul.Accordion {
        width: 872px;
       
        &:dir(ltr) {
            margin-left: 4px;}
        &:dir(rtl) {
            margin-right: 4px;}
        li.accordion-card {
            overflow: hidden;
            box-sizing: border-box;
            border: var(--border-light);
            border-radius: var(--radius-s);
            margin-top: 16px;
            background-color: var(--sys-center-channel-bg);

            &:has(.accordion-card-header[role="button"]){
                &:hover {
                    border: var(--border-dark);
                    box-shadow: var(--elevation-2);
                }
            }

            .accordion-card-header {
                max-height: 92px;
                padding: 0;
                background-color: var(--sys-center-channel-bg);

                &__icon {
                    padding: 0;
                    margin: 24px;

                    div {
                        z-index: 1;
                        display: flex;
                        width: 44px;
                        height: 44px;
                        align-items: center;
                        justify-content: center;
                        border-radius: 50%;
                        background-color: rgba(var(--sys-denim-center-channel-text-rgb), 0.16);

                        &.success {
                            background-color: rgba(var(--sys-online-indicator-rgb), 0.16);

                            svg {
                                opacity: 1;
                            }
                        }

                        svg {
                            opacity: 0.73;
                        }
                    }
                }

                &__body {
                    padding: 0;
                    margin: 27px 0 26px 16px;

                    &__title {
                        margin-bottom: 5px;
                        color: var(--sofa-color);
                        font-size: 14px;
                        font-weight: 600;
                        line-height: 14px;
                    }

                    &__description {
                        color: #3d3c40;
                        font-size: 14px;
                        font-style: normal;
                        font-weight: normal;
                        line-height: 20px;
                    }
                }

                &__extraContent {
                    display: flex;
                    flex-direction: row;
                    align-items: flex-end;
                    margin-top: 34px;
                    margin-left: auto;

                    button[class*=' StyledChip'],
                    button[class^='StyledChip'] {
                       
                        &:dir(ltr) {
                            margin-right: 24px;}
                        &:dir(rtl) {
                            margin-left: 24px;}
                    }
                }

                &__chevron {
                    padding: 0;
                   
                    font-size: 24px;
                    &:dir(ltr) {
                        margin: 30px 30px 30px 0;}
                    &:dir(rtl) {
                        margin: 30px 0 30px 30px;}
                }
            }

            .accordion-card-container {
                &__content {
                    padding: 16px 24px;
                    border-top: 1px solid rgba(63, 67, 80, 0.06);
                    background-color: rgb(255, 255, 255);

                    div[class*=' AccordionItem'],
                    div[class^='AccordionItem'] {
                        padding: 16px 0;

                        h5 {
                            margin-top: 0;
                            margin-bottom: 4px;
                        }

                        i {
                            font-size: 20px;
                            &:dir(rtl) {
                                margin-right: -5px;
                            }
                            &:dir(ltr) {
                                margin-left: -5px;
                            }

                            &.warning {
                                color: var(--sys-away-indicator);
                            }
                        }
                    }
                }

                .ctaButtons {
                    display: flex;
                    justify-content: flex-start;
                    margin: 15px 0 0 0;

                    a,
                    button {
                        max-height: 32px;
                        padding: 5px 20px !important;
                        border-radius: 4px !important;
                        margin-top: 0;
                        margin-right: 10px;
                        font-weight: 600;
                    }
                }
            }
        }
    }
}
