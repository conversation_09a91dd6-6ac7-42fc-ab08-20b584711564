.AdminUserCard {
    padding: 0;
    border-radius: 2px;
    margin: 2em 0 1em;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.5);

    .noUserBody {
        display: flex;
        min-height: 20vh;
        align-items: center;
        justify-content: center;
    }
}

.AdminUserCard__header {
    display: flex;
    height: 92px;
    flex-direction: row;
    align-items: flex-start;
    padding: 0 20px 0 30px;
    background-color: var(--sofa-color);
}

.AdminUserCard__header .Avatar.Avatar-xxl {
    border: solid 2px #fff;
}

.AdminUserCard__body {
    padding: 12px 20px 20px 178px;
    background-color: #fff;
}

.AdminUserCard__footer {
    display: flex;
    flex-direction: row;
    padding: 20px;
    border-top: solid 1px rgba(0, 0, 0, 0.2);
    background-color: #fff;
}

.AdminUserCard__user-info {
    overflow: hidden;
    min-width: 0;
    align-self: flex-end;
    padding: 0;
    margin-left: 20px;
    color: #fff;
    font-size: 20px;
    font-weight: normal;
    text-overflow: ellipsis;
}

.AdminUserCard__user-nickname {
    opacity: 0.5;
}

.AdminUserCard__user-id {
    align-self: flex-end;
    margin-left: auto;
    color: #fff;
    font-size: 12px;
    opacity: 0.5;
}
