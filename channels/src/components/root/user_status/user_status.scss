$transition-speed: 0.2s;
$border-radius-sm: 4px;
$border-radius-lg: 6px;
$spacing-sm: 8px;
$spacing-md: 12px;
$spacing-lg: 20px;

.user-status-container {
  width: 100%;
  height: 100%;
  padding: 0 100px;
  display: flex;
  flex-direction: column;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1051;
  color: #fff;

  button {
    padding: 4px;
    display: flex;
    background-color: unset;
    border: 0;
    border-radius: $border-radius-sm;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }

  .user-status-header {
    padding-top: 30px;
    display: flex;
    align-items: center;
    position: relative;
    
    .user-status-container__status-close-btn {
      margin: 0 8px;
    }

    .progress-container {
      position: absolute;
      inset: 0 0 auto;
      display: flex;
      gap: 2px;
      padding: 0 $spacing-md;
      margin-top: $spacing-sm;

      .progress-segment {
        flex: 1;
        height: 2px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 1px;
        overflow: hidden;

        .progress-bar {
          float: right;
          height: 100%;
          background: var(--sofa-color);
          transition: width 0.03s linear;
        }

        &.completed .progress-bar {
          background: var(--sofa-color);
        }
      }
    }

    .user-status-info {
      width: 100%;

      .user-status-profile {
        width: 100%;
        display: flex;
        align-items: center;
        gap: $spacing-md;

        .user-profile-image {
          width: 50px;
          height: 50px;

          img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
          }
        }

        .user-info-text {
          display: flex;
          flex-direction: column;

          .user-name {
            font-size: 2rem;
          }
        }
      }

      .status-dropdown-menu {
        position: relative;

        .status-dropdown-content {
          position: absolute;
          bottom: -4px;
          left: 0;
          background-color: #2d2d2d;
          border-radius: $border-radius-sm;
          box-shadow: 0 2px $spacing-sm rgba(0, 0, 0, 0.3);
          z-index: 1000;
          min-width: 120px;
          transform: translateY(100%);

          .status-dropdown-item {
            display: flex;
            align-items: center;
            gap: $spacing-sm;
            width: 100%;
            padding: $spacing-sm $spacing-md;
            text-align: left;
            background: none;
            border: none;
            cursor: pointer;

            &:hover {
              background-color: rgba(255, 255, 255, 0.1);
            }

            &.delete-option {
              color: #ff4d4f;
            }
          }
        }
      }
    }
  }

  .user-status-body {
    position: relative;
    display: flex;
    align-items: center;
    flex: 1;

    .nav-button {
      position: absolute;
      background: rgba(255, 255, 255, 0.1);
      border: none;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      cursor: pointer;
      transition: background-color $transition-speed;
      z-index: 1;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }

      &.prev {
        left: $spacing-sm;
      }
      &.next {
        right: $spacing-sm;
      }
    }

    .user-status-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: $spacing-lg;
      max-width: 100%;
      height: 100%;

      img,
      video {
        max-width: 100%;
        max-height: 80vh;
        width: auto;
        height: auto;
        object-fit: contain;

        &.vertical {
          max-width: 400px;
          max-height: 80vh;
        }

        &.horizontal {
          max-height: 600px;
          max-width: 80vw;
        }
      }

      .status-text {
        width: 100%;
        max-height: 100%;
        font-size: 1.9rem;
        text-align: center;
        white-space: pre-wrap;
        position: absolute;

        &:not(&.status-text--floating) {
          padding: 20px 0;
          top: 50%;
          transform: translateY(-50%);
        }
        
        &.status-text--floating {
          font-size: 1.4rem;
          width: 100%;
          text-align: center;
          bottom: 60px;
          background: rgba(0, 0, 0, 0.2);
        }
      }

      .status-text__read-more-btn {
        padding-right: 5px;
        font-weight: 600;
        font-style: italic;
        cursor: pointer;
      }
    }
  }

  .user-status-footer {
    padding-bottom: 35px;

    .user-status-reply {
      display: flex;
      align-items: center;
      gap: $spacing-sm;

      input,
      button {
        padding: 5px 10px;
        border-radius: $border-radius-lg;
        border: 1px solid #24A1DE1a;
        background-color: #001410;
      }

      input {
        flex-grow: 1;
      }

      button {
        svg:dir(rtl) {
          transform: rotate(180deg);
        }
      }
    }

    .user-status-views {
      position: absolute;
      width: 100%;
      left: 0;
      bottom: 12px;
      display: flex;
      flex-direction: column;
      align-items: center;

      .user-status-views__toggle-views {
        width: 60px;
        height: 60px;
        padding: 4px;
        margin-bottom: 4px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 2px;
        background: #24A1DE1A;
        color: var(--sofa-color);
        border-radius: 50%;
        cursor: pointer;
        
        span {
          font-weight: 500;
        }
      }

      .user-status-viewers {
        width: 190px;
        max-height: 0;
        background: #292C33;
        color: #fff;
        border-radius: 8px;
        overflow: auto;
        transition: .3s;
        
        &.open {
          max-height: 250px;
          padding: 8px;
        }

        .user-status-viewers__viewer {
          margin-bottom: 10px;
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .user-status-viewers__viewer-text {
          display: flex;
          flex-direction: column;
          gap: 2px;
          
          .user-name {
            font-size: 1.4rem;
          }

          time {
            font-size: 1.1rem;
          }
        }
      }
    }
  }
}
