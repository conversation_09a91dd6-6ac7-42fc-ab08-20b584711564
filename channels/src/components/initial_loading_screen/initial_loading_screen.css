body {
    margin: 0;
}

.LoadingScreen.disabled {
    opacity: 0;
}

.LoadingScreen {
    --background-color: #f4f4f6;
    --background-color-highlight: #ffffff;
    --stipple-color: #1e325c;
    --stipple-opacity: 0.08;
    position: absolute;
    z-index: 100;
    top: 0px;
    bottom: 0px;
    display: flex;
    overflow: hidden;
    width: 100vw;
    height: 100vh;
    align-items: center;
    justify-content: center;
    opacity: 1;
    transition: opacity 150ms 0ms ease-out, visibility 150ms 0ms step-start;
    vertical-align: middle;
    visibility: visible;
}

.LoadingScreen--darkMode {
    --background-color-highlight: #24A1DE;
    --background-color: #043561;
    --stipple-color: #14213e;
    --stipple-opacity: 0.8;
}

.LoadingScreen__background,
.LoadingScreen__background > svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
.LoadingScreen__background {
    background: var(--background-color);
}

.LoadingScreen .Pill__stipple {
    fill: var(--stipple-color);
    fill-opacity: var(--stipple-opacity);
}

.LoadingScreen .Pill__gradient {
    stop-color: var(--background-color);
}

.LoadingScreen .Pill__gradientHighlight {
    stop-color: var(--background-color-highlight);
}

.LoadingScreen--loaded {
    opacity: 0;
    transition: opacity 150ms 0ms ease-in, visibility 150ms 0ms step-end;
    visibility: hidden;
}

.LoadingAnimation {
    --fade-duration: 150ms;
    --colour: #1E325C;
    --animation-initial-delay: 750ms;
    --animation-start-duration: 750ms;
    --animation-end-duration: 600ms;
    --animation-spinner-speed: 500ms;
    --animation-spinner-mask-stroke-length: 169.6;
    --ease-in-cubic: cubic-bezier(0.32, 0, 0.67, 0);
    --ease-in: var(--ease-in-cubic);
    --ease-in-out-cubic: cubic-bezier(0.65, 0, 0.35, 1);
    --ease-in-out: var(--ease-in-out-cubic);
    --ease-in-out-compass-shrink: cubic-bezier(0.1, 0.25, 0.3, 1);

    opacity: 1;
    transform: scale3d(1, 1, 1);
}

.LoadingAnimation--darkMode {
    --colour: white;
}

.LoadingAnimation g,
.LoadingAnimation rect,
.LoadingAnimation path,
.LoadingAnimation circle {
    transform-origin: center center;
}
.LoadingAnimation svg {
    color: var(--colour);
}
.LoadingAnimation .LoadingAnimation__spinner-gradient-color {
    stop-color: var(--colour);
}
.LoadingAnimation .LoadingAnimation__spinner-container {
    opacity: 0;
    transform: scale3D(2.08, 2.08, 1) rotate3d(0, 0, 1, -10deg);
}
.LoadingAnimation .LoadingAnimation__spinner-mask-container {
    transform: rotate3d(0, 0, 1, -86deg);
}
.LoadingAnimation .LoadingAnimation__spinner-mask {
    stroke-dasharray: var(--animation-spinner-mask-stroke-length);
    stroke-dashoffset: var(--animation-spinner-mask-stroke-length);
    transform: scale3d(1.03, 1.03, 1);
}
.LoadingAnimation .LoadingAnimation__compass {
    opacity: 1;
    transform: scale3d(1, 1, 1);
}
.LoadingAnimation .LoadingAnimation__compass-needle-container {
    transform: scale3d(1, 1, 1);
}
.LoadingAnimation .LoadingAnimation__compass-needle,
.LoadingAnimation .LoadingAnimation__compass-needle-front-mask,
.LoadingAnimation .LoadingAnimation__compass-needle-behind-mask {
    transform: rotate3d(0, 0, 1, 0deg);
    transform-origin: 54px 46px;
}
.LoadingAnimation .LoadingAnimation__compass-base-mask-container {
    transform: rotate3d(0, 0, 1, -86deg);
}
.LoadingAnimation .LoadingAnimation__compass-base-mask {
    stroke-dasharray: var(--animation-spinner-mask-stroke-length);
    stroke-dashoffset: var(--animation-spinner-mask-stroke-length);
}

.LoadingAnimation--loading {
    --fade-in-duration: 150ms;
    --fade-in-delay: 0ms;

    transform: scale3d(1, 1, 1);
}

.LoadingAnimation.LoadingAnimation--loading .LoadingAnimation__spinner-container {
    --shrink-duration: calc(var(--animation-end-duration) * 0.5);
    --shrink-delay: calc( ( var(--animation-start-duration) + var(--animation-end-duration) ) * 0.91 + var(--animation-initial-delay));
    --fade-in-duration: calc(var(--animation-end-duration) * 0.25);
    --fade-in-delay: calc( var(--animation-start-duration) + var(--animation-end-duration) * 0.24 + var(--animation-initial-delay));

    animation: LoadingAnimation__spinner-shrink var(--shrink-duration) var(--shrink-delay) var(--ease-in-out-compass-shrink) forwards, LoadingAnimation__fade-in var(--fade-in-duration) var(--fade-in-delay) var(--ease-in) forwards;
}
.LoadingAnimation.LoadingAnimation--loading .LoadingAnimation__spinner-mask {
    --reveal-duration: var(--animation-end-duration);
    --reveal-delay: calc(var(--animation-start-duration) + var(--animation-initial-delay));

    animation: LoadingAnimation__spinner-reveal var(--reveal-duration) var(--reveal-delay) var(--ease-in) forwards;
}
.LoadingAnimation.LoadingAnimation--loading .LoadingAnimation__compass {
    --shrink-duration: calc(var(--animation-end-duration) * 0.5);
    --shrink-delay: calc( ( var(--animation-start-duration) + var(--animation-end-duration) ) * 0.91 + var(--animation-initial-delay));

    animation: LoadingAnimation__compass-shrink var(--shrink-duration) var(--shrink-delay) var(--ease-in-out-compass-shrink) forwards;
}
.LoadingAnimation.LoadingAnimation--loading .LoadingAnimation__compass-needle-container {
    --shrink-duration: calc(var(--animation-end-duration) * 0.25);
    --shrink-delay: calc( var(--animation-start-duration) + var(--animation-end-duration) - var(--animation-end-duration) * 0.25 + var(--animation-initial-delay));

    animation: LoadingAnimation__needle-shrink var(--shrink-duration) var(--shrink-delay) var(--ease-in) forwards;
}
.LoadingAnimation.LoadingAnimation--loading .LoadingAnimation__compass-needle {
    --spin-left-duration: var(--animation-start-duration);
    --spin-left-delay: var(--animation-initial-delay);
    --spin-right-duration: var(--animation-end-duration);
    --spin-right-delay: calc(var(--animation-start-duration) + var(--animation-initial-delay));
    --fade-out-duration: calc(var(--animation-end-duration) * 0.25);
    --fade-out-delay: calc( var(--animation-start-duration) + var(--animation-end-duration) - var(--animation-end-duration) * 0.25 + var(--animation-initial-delay));

    animation:
        LoadingAnimation__needle-spin-left var(--spin-left-duration) var(--spin-left-delay) var(--ease-in-out) forwards,
        LoadingAnimation__needle-spin-right var(--spin-right-duration) var(--spin-right-delay) var(--ease-in) forwards,
        LoadingAnimation__fade-out var(--fade-out-duration) var(--fade-out-delay) var(--ease-in) forwards;
}
.LoadingAnimation.LoadingAnimation--loading .LoadingAnimation__compass-needle-behind-mask {
    --spin-left-duration: var(--animation-start-duration);
    --spin-left-delay: var(--animation-initial-delay);
    --spin-right-duration: calc(var(--animation-end-duration) * 0.3666);
    --spin-right-delay: calc(var(--animation-start-duration) + var(--animation-initial-delay));

    animation: LoadingAnimation__needle-mask-spin-left var(--spin-left-duration) var(--spin-left-delay) var(--ease-in-out) forwards, LoadingAnimation__needle-mask-spin-right var(--spin-right-duration) var(--spin-right-delay) var(--ease-in) forwards;
}
.LoadingAnimation.LoadingAnimation--loading .LoadingAnimation__compass-needle-front-mask {
    --spin-left-duration: var(--animation-start-duration);
    --spin-left-delay:  var(--animation-initial-delay);
    --spin-right-duration: var(--animation-end-duration);
    --spin-right-delay: calc(var(--animation-start-duration) + var(--animation-initial-delay));

    animation: LoadingAnimation__needle-spin-left var(--spin-left-duration) var(--spin-left-delay) var(--ease-in-out) forwards, LoadingAnimation__needle-spin-right var(--spin-right-duration) var(--spin-right-delay) var(--ease-in) forwards;
}
.LoadingAnimation.LoadingAnimation--loading .LoadingAnimation__compass-base-mask {
    --conceal-duration: var(--animation-end-duration);
    --conceal-delay: calc(var(--animation-start-duration) + var(--animation-initial-delay));

    animation: LoadingAnimation__compass-base-conceal var(--conceal-duration) var(--conceal-delay) var(--ease-in) forwards;
}
.LoadingAnimation.LoadingAnimation--spinning .LoadingAnimation__spinner {
    --spin-duration: var(--animation-spinner-speed);
    --spin-delay: calc( ( var(--animation-start-duration) + var(--animation-end-duration) ) * 0.95 + var(--animation-initial-delay));

    animation: LoadingAnimation__spinner-spin var(--spin-duration) var(--spin-delay) linear infinite;
}

.LoadingAnimation--loaded {
    --duration: 150ms;
    --delay: 0ms;

    animation: LoadingAnimation__fade-out var(--duration) var(--delay) var(--ease-in) forwards, LoadingAnimation__shrink var(--duration) var(--delay) var(--ease-in) forwards;
}

.LoadingAnimation--loaded .LoadingAnimation__spinner-container {
    opacity: 1;
    transform: scale3D(1, 1, 1) rotate3d(0, 0, 1, -10deg);
}
.LoadingAnimation--loaded .LoadingAnimation__spinner-mask {
    stroke-dashoffset: 0;
}
.LoadingAnimation--loaded .LoadingAnimation__compass {
    transform: scale3D(0.4166666667, 0.4166666667, 1);
}
.LoadingAnimation--loaded .LoadingAnimation__compass-needle-container {
    transform: scale3d(0.35, 0.35, 1);
}
.LoadingAnimation--loaded .LoadingAnimation__compass-needle {
    opacity: 0;
    transform: rotate3d(0, 0, 1, 405deg);
}
.LoadingAnimation--loaded .LoadingAnimation__compass-needle-behind-mask {
    transform: rotate3d(0, 0, 1, 0deg);
}
.LoadingAnimation--loaded .LoadingAnimation__compass-needle-front-mask {
    transform: rotate3d(0, 0, 1, 405deg);
}
.LoadingAnimation--loaded .LoadingAnimation__compass-base-mask {
    stroke-dashoffset: 0;
}

@keyframes LoadingAnimation__fade-in {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

@keyframes LoadingAnimation__fade-out {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}

@keyframes LoadingAnimation__shrink {
    0% {
        transform: scale3d(1, 1, 1);
    }
    100% {
        transform: scale3d(0.35, 0.35, 1);
    }
}

@keyframes LoadingAnimation__spinner-shrink {
    0% {
        transform: scale3D(2.08, 2.08, 1) rotate3d(0, 0, 1, -10deg);
    }
    100% {
        transform: scale3D(1, 1, 1) rotate3d(0, 0, 1, -10deg);
    }
}

@keyframes LoadingAnimation__spinner-spin {
    from {
        transform: rotate3d(0, 0, 1, 0deg);
    }
    to {
        transform: rotate3d(0, 0, 1, 359deg);
    }
}

@keyframes LoadingAnimation__spinner-reveal {
    0%, 5% {
        stroke-dashoffset: var(--animation-spinner-mask-stroke-length);
    }
    95%, 100% {
        stroke-dashoffset: 0;
    }
}

@keyframes LoadingAnimation__needle-spin-left {
    0% {
        transform: rotate3d(0, 0, 1, 0deg);
    }
    100% {
        transform: rotate3d(0, 0, 1, -20deg);
    }
}

@keyframes LoadingAnimation__needle-spin-right {
    0% {
        transform: rotate3d(0, 0, 1, -20deg);
    }
    100% {
        transform: rotate3d(0, 0, 1, 405deg);
    }
}

@keyframes LoadingAnimation__needle-mask-spin-left {
    0% {
        transform: rotate3d(0, 0, 1, 0deg);
    }
    100% {
        transform: rotate3d(0, 0, 1, -20deg);
    }
}

@keyframes LoadingAnimation__needle-mask-spin-right {
    0% {
        transform: rotate3d(0, 0, 1, -20deg);
    }
    100% {
        transform: rotate3d(0, 0, 1, 0deg);
    }
}

@keyframes LoadingAnimation__needle-shrink {
    0% {
        transform: scale3d(1, 1, 1);
    }
    100% {
        transform: scale3d(0.35, 0.35, 1);
    }
}

@keyframes LoadingAnimation__compass-shrink {
    0% {
        transform: scale3D(1, 1, 1);
    }
    100% {
        transform: scale3D(0.4166666667, 0.4166666667, 1);
    }
}

@keyframes LoadingAnimation__compass-base-conceal {
    0%, 5% {
        stroke-dashoffset: var(--animation-spinner-mask-stroke-length);
    }
    95%, 100% {
        stroke-dashoffset: 0;
    }
}
