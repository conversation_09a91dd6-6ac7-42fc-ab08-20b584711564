.splitSendButton {
    display: flex;
    width: 48px;
    height: 32px;
    flex-direction: row;
    justify-content: center;
    border-radius: 4px;
    background: var(--button-bg);
    color: var(--button-color);

    &.scheduledPost {
        width: 56px;
    }

    &.disabled {
        background: rgba(var(--center-channel-color-rgb), 0.08);

        svg {
            fill: #24A1DE;
        }

        .button_send_post_options {
            border-color: rgba(var(--center-channel-color-rgb), 0.16);
        }
    }

    .SendMessageButton, .button_send_post_options {
        display: flex;
        flex-direction: column;
        justify-content: center;
        border: none;
        background: none;

        &.disabled {
            cursor: not-allowed;
        }
    }

    .SendMessageButton {
        flex: 1;
        border-radius: 4px 0 0 4px;
        cursor: pointer;
        padding-inline: 7px;
        place-content: center;
        place-items: center;
        transition: color 150ms;

        .android &,
        .ios & {
            display: flex;
        }

        &.singleAction {
            border-radius: 4px;
        }

        svg {
            &:dir(rtl) {
                transform: rotate(180deg);
            }
        }
    }

    .button_send_post_options {
        width: 21px;
        border-radius: 0 4px 4px 0;
        padding-inline: 2px;
        &:dir(rtl){
            border-right: 1px solid color-mix(in srgb, currentColor 20%, transparent);
        }
        &:dir(ltr){
            border-left: 1px solid color-mix(in srgb, currentColor 20%, transparent);
        }
    }
}
