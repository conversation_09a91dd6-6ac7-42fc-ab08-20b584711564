import React from 'react'

type Props = {
    statuses: any[];
    svgSize: number;
    circleRadius: number;
    left?: string;
    top?: string;
}

const UserStatusBorder = ({statuses, svgSize, circleRadius, top='0px', left='0px'}: Props) => {
    const gapSize = Math.max(3, Math.round(16 / statuses.length))
    const circumference = 2 * circleRadius * Math.PI;
    const sectionBorderLength = (circumference / statuses.length) - gapSize;

  return (
    <svg width={svgSize} height={svgSize} style={{position: 'absolute', top, left, transform: 'rotate(180deg)'}}>
        {statuses.map((status, index) => (
            <circle 
                r={circleRadius} 
                fill="none" 
                cx={svgSize / 2} 
                cy={svgSize / 2} 
                stroke={!status.viewed? '#24A1DE': '#8A8A8A'} 
                stroke-width="2"
                stroke-linecap="round" 
                stroke-dasharray={`${sectionBorderLength} ${statuses.length > 1? circumference - sectionBorderLength: 0}`} // No gap if the status length is 1
                stroke-dashoffset={(sectionBorderLength + gapSize) * index}
            />
        ))}
    </svg>
  )
}

export default UserStatusBorder