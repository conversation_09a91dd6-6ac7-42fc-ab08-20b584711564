// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import classNames from 'classnames';
import React from 'react';
import type {MouseEvent, KeyboardEvent} from 'react';
import {Draggable, Droppable} from 'react-beautiful-dnd';
import {OverlayTrigger, Tooltip} from 'react-bootstrap';
import {defineMessages, FormattedMessage} from 'react-intl';

import type {WebSocketMessage} from '@mattermost/client';
import type {PluginUserStatus} from '@mattermost/client/src/client4';
import {PulsatingDot} from '@mattermost/components';
import type {ChannelCategory} from '@mattermost/types/channel_categories';
import {CategorySorting} from '@mattermost/types/channel_categories';
import type {PreferenceType} from '@mattermost/types/preferences';

import {Client4} from 'mattermost-redux/client';
import {CategoryTypes} from 'mattermost-redux/constants/channel_categories';
import {localizeMessage} from 'mattermost-redux/utils/i18n_utils';

import {trackEvent} from 'actions/telemetry_actions';

// import KeyboardShortcutSequence, {
//     KEYBOARD_SHORTCUTS,
// } from 'components/keyboard_shortcuts/keyboard_shortcuts_sequence';
import WithTooltip from 'components/with_tooltip';

import Constants, {A11yCustomEventTypes, DraggingStateTypes, DraggingStates} from 'utils/constants';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {t} from 'utils/i18n';
import {isKeyPressed} from 'utils/keyboard';
import * as Utils from 'utils/utils';

import type {DraggingState} from 'types/store';

import SidebarCategoryMenu from './sidebar_category_menu';
import SidebarCategorySortingMenu from './sidebar_category_sorting_menu';

import AddChannelsCtaButton from '../add_channels_cta_button';
import InviteMembersButton from '../invite_members_button';
import {SidebarCategoryHeader} from '../sidebar_category_header';
import SidebarChannel from '../sidebar_channel';

import './sidebchannelt.scss';

// eslint-disable-next-line import/order
import WebSocketClient from 'client/web_websocket_client';

// eslint-disable-next-line import/order
import UsersStatusSidebar from './UserStatusSidebar';
// eslint-disable-next-line @typescript-eslint/consistent-type-imports

type Props = {
    category: ChannelCategory;
    categoryIndex: number;
    channelIds: string[];
    setChannelRef: (channelId: string, ref: HTMLLIElement) => void;
    handleOpenMoreDirectChannelsModal: (e: Event) => void;
    isNewCategory: boolean;
    draggingState: DraggingState;
    currentUserId: string;
    isAdmin: boolean;
    actions: {
        setCategoryCollapsed: (categoryId: string, collapsed: boolean) => void;
        setCategorySorting: (categoryId: string, sorting: CategorySorting) => void;
        savePreferences: (userId: string, preferences: PreferenceType[]) => void;
    };
};

type UserStatusTuple = [string, PluginUserStatus[]];

type State = {
    isMenuOpen: boolean;
    searchQuery: string; // تهيئة حالة البحث
    selectedStatusFilter: string | null;
    allUsersStatus: UserStatusTuple[];
    currentUserStatuses: UserStatusTuple[];
    seenStatuses: UserStatusTuple[];
    unSeenStatuses: UserStatusTuple[];
    currentTab: string;
    mutedStatus: UserStatusTuple[];
    plugins: any[];
};

export default class SidebarCategory1 extends React.PureComponent<Props, State> {
    categoryTitleRef: React.RefObject<HTMLButtonElement>;
    newDropBoxRef: React.RefObject<HTMLDivElement>;

    a11yKeyDownRegistered: boolean;

    constructor(props: Props) {
        super(props);

        this.categoryTitleRef = React.createRef();
        this.newDropBoxRef = React.createRef();

        this.state = {
            isMenuOpen: false,
            searchQuery: '', // تهيئة حالة البحث
            selectedStatusFilter: '',
            allUsersStatus: [],
            currentUserStatuses: [],
            seenStatuses: [],
            unSeenStatuses: [],
            currentTab: 'direct_messages',
            mutedStatus: [],
            plugins: [],
        };

        this.a11yKeyDownRegistered = false;
    }

    componentDidUpdate(prevProps: Props) {
        if (this.props.category.collapsed !== prevProps.category.collapsed && this.newDropBoxRef.current) {
            this.newDropBoxRef.current.classList.add('animating');
        }
    }

    componentDidMount() {
        this.categoryTitleRef.current?.addEventListener(A11yCustomEventTypes.ACTIVATE, this.handleA11yActivateEvent);
        this.categoryTitleRef.current?.addEventListener(A11yCustomEventTypes.DEACTIVATE, this.handleA11yDeactivateEvent);

        this.getAllUsersStatus();
        this.getPlugins();
        WebSocketClient.addMessageListener(this.handleWebSocketMessage); // Register for WebSocket events
    }

    componentWillUnmount() {
        this.categoryTitleRef.current?.removeEventListener(A11yCustomEventTypes.ACTIVATE, this.handleA11yActivateEvent);
        this.categoryTitleRef.current?.removeEventListener(A11yCustomEventTypes.DEACTIVATE, this.handleA11yDeactivateEvent);

        // Clean up WebSocket listener
        WebSocketClient.removeMessageListener(this.handleWebSocketMessage);

        if (this.a11yKeyDownRegistered) {
            this.handleA11yDeactivateEvent();
        }
    }

    getAllUsersStatus = async () => {
        try {
            const allStatuses = (await Client4.getAllUsersStatus()) || {};
            const allStatusesArr = Object.entries(allStatuses) as unknown as UserStatusTuple[];

            this.updateCurrentUserStatuses(allStatusesArr);
            this.updateSeenAndUnSeenStatuses(allStatusesArr);

            // Try to get muted statuses, but don't fail if the plugin is not available
            try {
                const mutedStatus = (await Client4.getMutedStatus()) || {};
                const mutedStatusArr = Object.entries(mutedStatus) as unknown as UserStatusTuple[];
                this.updateMutedStatuses(mutedStatusArr);
            } catch (mutedError) {
                // Plugin might not be installed, silently continue with empty muted statuses
                this.setState({mutedStatus: []});
            }

            this.setState({allUsersStatus: allStatusesArr});
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error('Failed to fetch user statuses:', error);
        }
    };

    updateCurrentUserStatuses = (allStatusesArr: UserStatusTuple[]) => {
        const currentUserStatuses = allStatusesArr.filter(([userID]) => userID === this.props.currentUserId);
        this.setState({currentUserStatuses});
    };

    updateSeenAndUnSeenStatuses = (allUsersStatus: UserStatusTuple[], shouldUpdateAllStatuses = true) => {
        const seenStatuses: UserStatusTuple[] = [];
        const unSeenStatuses: UserStatusTuple[] = [];

        for (const [userID, userStatuses] of allUsersStatus) {
            const firstUnSeenStatusIndex = userStatuses.findIndex((status) => !status.viewed);

            if (firstUnSeenStatusIndex < 0) {
                seenStatuses.push([userID, userStatuses]);
            } else {
                unSeenStatuses.push([userID, userStatuses]);
            }
        }

        const filteredUnSeenStatuses = unSeenStatuses.filter(([userID]) => userID !== this.props.currentUserId);
        if (shouldUpdateAllStatuses) {
            this.setState({seenStatuses, unSeenStatuses: filteredUnSeenStatuses, allUsersStatus});
        } else {
            this.setState({seenStatuses, unSeenStatuses: filteredUnSeenStatuses});
        }
    };

    updateMutedStatuses = async (mutedStatusArr: UserStatusTuple[]) => {
        const enrichedMutedStatusArr = await Promise.all(mutedStatusArr.map(async ([userID, statuses]) => {
            try {
                const user = await Client4.getUser(userID);
                const fullName = user ? `${user.first_name} ${user.last_name}`.trim() || user.username : '';
                const enrichedStatuses = statuses.map((status) => ({
                    ...status,
                    userName: fullName,
                }));
                return [userID, enrichedStatuses] as UserStatusTuple;
            } catch (error) {
                // eslint-disable-next-line no-console
                console.error(`Failed to fetch user ${userID} for muted status enrichment:`, error);
                return [userID, statuses] as UserStatusTuple;
            }
        }));

        this.setState({mutedStatus: enrichedMutedStatusArr.filter(Boolean) as UserStatusTuple[]});
    };

    updateStatuses = (statusesList: UserStatusTuple[], newStatus: PluginUserStatus, add = true): UserStatusTuple[] => {
        const updatedStatusesList: UserStatusTuple[] = [];
        let found = false;

        for (const [userID, statuses] of statusesList) {
            if (userID === newStatus.user_id) {
                found = true;
                const newStatuses = add ? [...statuses, newStatus] : statuses.filter((s) => s.id !== newStatus.id);
                if (newStatuses.length > 0) {
                    updatedStatusesList.push([userID, newStatuses]);
                }
            }
        }

        if (!found && add) {
            updatedStatusesList.push([newStatus.user_id, [newStatus]]);
        }

        return updatedStatusesList;
    };

    markStatusAsSeen = (statusID: string) => {
        const allUsersStatus = this.state.allUsersStatus;

        const updatedAllUsersStatus: UserStatusTuple[] = allUsersStatus.map(([userID, stack]) => {
            const updatedStack = stack.map((status) => {
                if (status.id === statusID && !status.viewed) {
                    return {...status, viewed: true};
                }
                return status;
            });

            return [userID, updatedStack] as UserStatusTuple;
        });

        this.updateSeenAndUnSeenStatuses(updatedAllUsersStatus, false);
    };

    handleA11yActivateEvent = () => {
        this.categoryTitleRef.current?.addEventListener('keydown', this.handleA11yKeyDown);
        this.a11yKeyDownRegistered = true;
    };

    handleA11yDeactivateEvent = () => {
        this.categoryTitleRef.current?.removeEventListener('keydown', this.handleA11yKeyDown);
        this.a11yKeyDownRegistered = false;
    };

    handleA11yKeyDown = (e: KeyboardEvent<HTMLButtonElement>['nativeEvent']) => {
        if (isKeyPressed(e, Constants.KeyCodes.ENTER)) {
            this.handleCollapse();
        }
    };

    handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        this.setState({searchQuery: event.target.value});
    };

    setSelectedStatusFilter = (status: string | null) => {
        this.setState({
            selectedStatusFilter: status,
        });
    };

    muteStatus = async (userID: string) => {
        try {
            const {seenStatuses, unSeenStatuses} = this.state;

            const seenAndUnSeenStatuses = seenStatuses.concat(unSeenStatuses);
            const matchedStatus = seenAndUnSeenStatuses.find(([id]) => id === userID);

            if (!matchedStatus) {
                return;
            }

            const [matchedUserID, statuses] = matchedStatus;
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const isAllViewed = statuses.every((status) => status.viewed);

            // Mute the user
            await Client4.muteUserStatus(userID);

            this.setState((prevState) => {
                const filterFn = ([id]: [string, any]) => id !== matchedUserID;
                const updatedSeenStatuses = prevState.seenStatuses.filter(filterFn);
                const updatedUnSeenStatuses = prevState.unSeenStatuses.filter(filterFn);

                return {
                    ...prevState,
                    seenStatuses: updatedSeenStatuses,
                    unSeenStatuses: updatedUnSeenStatuses,
                    mutedStatus: [...prevState.mutedStatus, matchedStatus],
                };
            });
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error(error);
        }
    };

    getPlugins = async () => {
        try {
            const plugins = await Client4.getPlugins();
            this.setState({plugins: plugins.active});
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error('Failed to fetch plugins:', error);
        }
    };

    unMuteStatus = async (userID: string) => {
        try {
            await Client4.unMuteUserStatus(userID);

            this.setState((prevState) => {
                const matchedStatus = prevState.mutedStatus.find(([id]) => id === userID);
                if (!matchedStatus) {
                    // eslint-disable-next-line no-console
                    console.warn('Muted status for user not found');
                    return null;
                }

                const [, statuses] = matchedStatus;
                const isAllViewed = statuses.every((status) => status.viewed);

                const updatedMutedStatus = prevState.mutedStatus.filter(([id]) => id !== userID);

                const targetListName = isAllViewed ? 'seenStatuses' : 'unSeenStatuses';
                const updatedTargetList = [...prevState[targetListName], matchedStatus];

                return {
                    ...prevState,
                    mutedStatus: updatedMutedStatus,
                    [targetListName]: updatedTargetList,
                };
            });
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error(error);
        }
    };

    renderChannel = (channelId: string, index: number) => {
        const {setChannelRef, category, draggingState} = this.props;
        const {searchQuery} = this.state;

        return (

            <SidebarChannel
                key={channelId}
                channelIndex={index}
                channelId={channelId}
                isDraggable={false}
                setChannelRef={setChannelRef}
                isCategoryCollapsed={category.collapsed}
                isCategoryDragged={draggingState.type === DraggingStateTypes.CATEGORY && draggingState.id === category.id}
                isAutoSortedCategory={category.sorting === CategorySorting.Alphabetical || category.sorting === CategorySorting.Recency}
                searchQuery={searchQuery} // Pass searchQuery here
                selectedStatusFilter={this.state.selectedStatusFilter}
                userStatuses={this.state.allUsersStatus}
            />
        );
    };

    handleCollapse = () => {
        const {category} = this.props;
        if (category.collapsed) {
            trackEvent('ui', 'ui_sidebar_expand_category');
        } else {
            trackEvent('ui', 'ui_sidebar_collapse_category');
        }

        this.props.actions.setCategoryCollapsed(category.id, !category.collapsed);
    };

    removeAnimation = () => {
        if (this.newDropBoxRef.current) {
            this.newDropBoxRef.current.classList.remove('animating');
        }
    };

    handleOpenDirectMessagesModal = (event: MouseEvent<HTMLLIElement | HTMLButtonElement> | KeyboardEvent<HTMLLIElement | HTMLButtonElement>) => {
        event.preventDefault();
        this.props.handleOpenMoreDirectChannelsModal(event.nativeEvent);
        trackEvent('ui', 'ui_sidebar_create_direct_message');
    };

    isDropDisabled = () => {
        const {draggingState, category} = this.props;
        if (category.type === CategoryTypes.DIRECT_MESSAGES) {
            return draggingState.type === DraggingStateTypes.CHANNEL;
        } else if (category.type === CategoryTypes.CHANNELS) {
            return draggingState.type === DraggingStateTypes.DM;
        }
        return false;
    };

    renderNewDropBox = (isDraggingOver: boolean) => {
        const {draggingState, category, isNewCategory, channelIds} = this.props;

        if (!isNewCategory || channelIds?.length) {
            return null;
        }

        return (
            <>
                <Draggable
                    draggableId={`NEW_CHANNEL_SPACER__${category.id}`}
                    isDragDisabled={true}
                    index={0}
                >
                    {(provided) => {
                        return (
                            <li
                                ref={provided.innerRef}
                                draggable='false'
                                className={'SidebarChannel noFloat newChannelSpacer '}
                                {...provided.draggableProps}
                                role='listitem'
                                tabIndex={-1}
                            />
                        );
                    }}
                </Draggable>
                <div className='SidebarCategory_newDropBox'>
                    <div
                        ref={this.newDropBoxRef}
                        className={classNames('SidebarCategory_newDropBox-content', {
                            collapsed: category.collapsed || (draggingState.type === DraggingStateTypes.CATEGORY && draggingState.id === category.id),
                            isDraggingOver,
                        })}
                        onTransitionEnd={this.removeAnimation}
                    >
                        <i className='icon-hand-right'/>
                        <span className='SidebarCategory_newDropBox-label'>
                            <FormattedMessage
                                id='sidebar_left.sidebar_category.newDropBoxLabel'
                                defaultMessage='Drag channels here...'
                            />
                        </span>
                    </div>
                </div>
            </>
        );
    };

    showPlaceholder = () => {
        const {channelIds, draggingState, category, isNewCategory} = this.props;
        if (category.sorting === CategorySorting.Alphabetical ||
            category.sorting === CategorySorting.Recency ||
            isNewCategory) {
            if (channelIds.find((id) => id === draggingState.id)) {
                return true;
            }
            return false;
        }
        return true;
    };
    handleClearSearch = () => {
        this.setState({searchQuery: ''}); // Clear the search input
    };

    handleWebSocketMessage = (msg: WebSocketMessage) => {
        if (!msg || !msg.data || !msg.event) {
            return;
        }

        const newStatus = msg.data;
        const isCurrentUser = this.props.currentUserId === newStatus.user_id;

        if (msg.event.includes('status_status_created')) {
            if (isCurrentUser) {
                const updatedCurrentUserStatuses = this.updateStatuses(this.state.currentUserStatuses, newStatus, true);
                this.setState({currentUserStatuses: updatedCurrentUserStatuses});
            } else {
                const updatedAllStatuses = this.updateStatuses(this.state.allUsersStatus, newStatus, true);
                this.setState({allUsersStatus: updatedAllStatuses});
                this.updateSeenAndUnSeenStatuses(updatedAllStatuses);
            }
        } else if (msg.event.includes('status_status_deleted')) {
            if (isCurrentUser) {
                const updatedCurrentUserStatuses = this.updateStatuses(this.state.currentUserStatuses, newStatus, false);
                this.setState({currentUserStatuses: updatedCurrentUserStatuses});
            } else {
                const updatedAllStatuses = this.updateStatuses(this.state.allUsersStatus, newStatus, false);
                this.setState({allUsersStatus: updatedAllStatuses});
                this.updateSeenAndUnSeenStatuses(updatedAllStatuses);
            }
        }
    };

    render() {
        const {
            category,
            categoryIndex,
            channelIds,
            isNewCategory,
        } = this.props;

        if (category.type !== CategoryTypes.DIRECT_MESSAGES) {
            return null;
        }

        if (!category || (category.type === CategoryTypes.FAVORITES && !channelIds?.length)) {
            return null;
        }

        const renderedChannels = channelIds.map(this.renderChannel);

        let categoryMenu: JSX.Element;
        let newLabel: JSX.Element;
        const directMessagesModalButton: JSX.Element = <></>; // عنصر فارغ
        const isCollapsible = true;
        const addHelpLabel = localizeMessage({id: 'sidebar.createDirectMessage', defaultMessage: 'Create new direct message'});
        const addTooltip = (
            <Tooltip
                id='new-group-tooltip'
                className='hidden-xs'
            >
                {addHelpLabel}
                {/* <KeyboardShortcutSequence
                    shortcut={KEYBOARD_SHORTCUTS.navDMMenu}
                    hideDescription={true}
                    isInsideTooltip={true}
                /> */}
            </Tooltip>
        );

        categoryMenu = (
            <>
                <SidebarCategorySortingMenu
                    category={category}
                    handleOpenDirectMessagesModal={this.handleOpenDirectMessagesModal}
                    selectedStatusFilter={this.state.selectedStatusFilter}
                    setSelectedStatusFilter={this.setSelectedStatusFilter}
                />
                <OverlayTrigger
                    delayShow={500}
                    placement='bottom'
                    overlay={addTooltip}
                >
                    <button
                        className='SidebarChannelGroupHeader_addButton'
                        onClick={this.handleOpenDirectMessagesModal}
                        aria-label={addHelpLabel}
                    >
                        <i className='icon-plus'/>
                    </button>
                </OverlayTrigger>
            </>
        );

        const toggleDirectMessagesButton = (
            this.state.currentTab === 'direct_messages' ? (
                <div
                    className='d-flex cursor--pointer'
                    onClick={() => this.setState({currentTab: 'stories'})}
                    style={{position: 'relative'}}
                >
                    {this.state.unSeenStatuses.length > 0 && (
                        <div style={{position: 'absolute', top: '0', right: '0'}}>
                            <PulsatingDot coords={{x: '2', y: '-4'}}/>
                        </div>
                    )}

                    <svg
                        xmlns='http://www.w3.org/2000/svg'
                        viewBox='0 0 24 24'
                        width='18'
                        height='18'
                        fill='none'
                        stroke='currentColor'
                        strokeWidth='2'
                        strokeLinecap='round'
                        strokeLinejoin='round'
                    >

                        <circle
                            cx='12'
                            cy='12'
                            r='10'
                            strokeDasharray='3 3'
                        />

                        <circle
                            cx='12'
                            cy='12'
                            r='6'
                        />
                    </svg>
                </div>
            ) : (
                <div
                    className='d-flex cursor--pointer'
                    onClick={() => this.setState({currentTab: 'direct_messages'})}
                >
                    <svg
                        width='24'
                        height='24'
                        viewBox='0 0 24 24'
                        fill='none'
                        xmlns='http://www.w3.org/2000/svg'
                    >
                        <path
                            d='M5 8H15'
                            stroke='currentColor'
                            strokeWidth='1.5'
                            strokeLinecap='round'
                        />
                        <path
                            d='M5 12H11'
                            stroke='currentColor'
                            strokeWidth='1.5'
                            strokeLinecap='round'
                        />

                        <path
                            d='M7 15.5C7 14.9477 7.44772 14.5 8 14.5H18C18.5523 14.5 19 14.9477 19 15.5V17.5C19 18.0523 18.5523 18.5 18 18.5H9L7 20.5V15.5Z'
                            fill='currentColor'
                        />
                    </svg>
                </div>
            )
        );

        if (isNewCategory) {
            newLabel = (
                <div className='SidebarCategory_newLabel'>
                    <FormattedMessage
                        id='sidebar_left.sidebar_category.newLabel'
                        defaultMessage='new'
                    />
                </div>
            );
            categoryMenu = <SidebarCategoryMenu category={category}/>;
        } else {
            categoryMenu = <SidebarCategoryMenu category={category}/>;
        }

        let displayName = category.display_name;
        if (category.type !== CategoryTypes.CUSTOM) {
            const message = categoryNames[category.type as keyof typeof categoryNames];
            displayName = localizeMessage({id: message.id, defaultMessage: message.defaultMessage});

            if (this.state.currentTab === 'stories') {
                displayName = 'القصص';
            }
        }
        categoryMenu = (
            <>
                <SidebarCategorySortingMenu
                    category={category}
                    handleOpenDirectMessagesModal={this.handleOpenDirectMessagesModal}
                    selectedStatusFilter={this.state.selectedStatusFilter}
                    setSelectedStatusFilter={this.setSelectedStatusFilter}
                />
                <WithTooltip
                    title={addHelpLabel}
                >
                    <button
                        className='SidebarChannelGroupHeader_addButton'
                        onClick={this.handleOpenDirectMessagesModal}
                        aria-label={addHelpLabel}
                    >
                        <i
                            className='icon-plus'
                            style={{fontSize: '19px'}}
                        />
                    </button>
                </WithTooltip>
            </>
        );

        return (
            <Draggable
                draggableId={category.id}
                index={categoryIndex}
                disableInteractiveElementBlocking={true}
            >
                {(provided, snapshot) => {
                    let inviteMembersButton = null;
                    if (category.type === 'direct_messages' && !category.collapsed) {
                        inviteMembersButton = (
                            <InviteMembersButton
                                className='followingSibling'
                                isAdmin={this.props.isAdmin}
                            />
                        );
                    }

                    let addChannelsCtaButton = null;
                    if (category.type === 'channels' && !category.collapsed) {
                        // eslint-disable-next-line @typescript-eslint/no-unused-vars
                        addChannelsCtaButton = <AddChannelsCtaButton/>;
                    }

                    const appliedFilter = (
                        <div className='SidebarChannelGroupHeader_filter'>
                            <FormattedMessage
                                id='sidebar_left.sidebar_channel.filter'
                                defaultMessage='فلتر: '
                            />
                            {this.state.selectedStatusFilter}
                            <button
                                className='clear-status-filter'
                                onClick={() => {
                                    this.setState({
                                        selectedStatusFilter: null,
                                    });
                                }}
                            >
                                <i className='icon icon-close'/>
                            </button>
                        </div>
                    );

                    return (
                        <div
                            className={classNames('SidebarChannelGroup a11y__section', {
                                dropDisabled: this.isDropDisabled(),
                                menuIsOpen: this.state.isMenuOpen,
                                capture: this.props.draggingState.state === DraggingStates.CAPTURE,
                                isCollapsed: category.collapsed,
                            })}
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                        >
                            <Droppable
                                droppableId={category.id}
                                type='SIDEBAR_CHANNEL'
                                isDropDisabled={this.isDropDisabled()}
                            >
                                {(droppableProvided, droppableSnapshot) => (
                                    <div
                                        {...droppableProvided.droppableProps}
                                        ref={droppableProvided.innerRef}
                                        className={classNames({
                                            draggingOver: droppableSnapshot.isDraggingOver,
                                        })}
                                    >
                                        <div className='sidbaer-card1'>
                                            <SidebarCategoryHeader
                                                ref={this.categoryTitleRef}
                                                displayName={displayName}
                                                dragHandleProps={provided.dragHandleProps}
                                                isCollapsed={category.collapsed}
                                                isCollapsible={isCollapsible}
                                                isDragging={snapshot.isDragging}
                                                isDraggingOver={droppableSnapshot.isDraggingOver}
                                                muted={category.muted}
                                                onClick={this.handleCollapse}
                                            >
                                                {newLabel}
                                                {newLabel}
                                                {directMessagesModalButton}

                                                {this.state.plugins.some((p: any) => p.id === 'com.example.status') && toggleDirectMessagesButton}

                                                {categoryMenu}
                                            </SidebarCategoryHeader>

                                        </div>
                                        {this.state.currentTab === 'direct_messages' ? (
                                            <div className={classNames('SidebarChannelGroup_content')}>
                                                {!category.collapsed && (
                                                    <div className='search-input-container'>
                                                        {this.state.searchQuery && (
                                                            <i
                                                                className='icon icon-close-circle'
                                                                style={{
                                                                    fontSize: '19px',
                                                                    justifyContent: 'center',
                                                                    alignItems: 'center',
                                                                    display: 'flex',
                                                                    cursor: 'pointer',
                                                                    position: 'absolute',
                                                                    left: '0',
                                                                    color: '#f23d4a',
                                                                    zIndex: '11',

                                                                }}
                                                                onClick={this.handleClearSearch}
                                                            />
                                                        )}
                                                        <input
                                                            type='text'
                                                            placeholder={Utils.localizeMessage({id: 'search_bar.search', defaultMessage: 'Search'})}
                                                            value={this.state.searchQuery}
                                                            onChange={this.handleSearchChange}
                                                            className='search-input'
                                                        />
                                                        {!this.state.searchQuery && (

                                                            <i
                                                                className='search-icon icon icon-magnify'
                                                                style={{fontSize: '19px',
                                                                    justifyContent: 'center',
                                                                    alignItems: 'center',
                                                                    display: 'flex'}}
                                                            />
                                                        )}
                                                    </div>

                                                )}
                                                <div className='mt-2 mb-2 d-flex align-items-center'>
                                                    {inviteMembersButton}
                                                    {this.state.selectedStatusFilter && appliedFilter}
                                                </div>
                                                <ul
                                                    role='list'
                                                    className='NavGroupContent bolder dolders'
                                                >
                                                    {this.renderNewDropBox(droppableSnapshot.isDraggingOver)}
                                                    {renderedChannels}
                                                    {this.showPlaceholder() ? droppableProvided.placeholder : null}
                                                </ul>
                                            </div>
                                        ) : (
                                            <UsersStatusSidebar
                                                currentUserID={this.props.currentUserId}
                                                currentUserStatuses={this.state.currentUserStatuses}
                                                markStatusAsSeen={this.markStatusAsSeen}
                                                seenStatuses={this.state.seenStatuses}
                                                unSeenStatuses={this.state.unSeenStatuses}
                                                mutedStatus={this.state.mutedStatus}
                                                muteStatus={this.muteStatus}
                                                unMuteStatus={this.unMuteStatus}
                                            />
                                        )}
                                    </div>
                                )}
                            </Droppable>
                        </div>
                    );
                }}
            </Draggable>
        );
    }
}
const categoryNames = defineMessages({
    channels: {
        id: 'sidebar.types.channels',
        defaultMessage: 'CHANNELS',
    },
    direct_messages: {
        id: 'sidebar.types.direct_messages',
        defaultMessage: 'DIRECT MESSAGES',
    },
    favorites: {
        id: 'sidebar.types.favorites',
        defaultMessage: 'FAVORITES',
    },
});
