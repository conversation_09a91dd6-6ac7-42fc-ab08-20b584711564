import React from 'react'
import './custom-user-status-color-picker.scss'

type props = {
    colorPalette: string[];
    activeColor: string;
    setActiveColor: (activeColor: string) => void;
}

function CustomUserStatusColorPicker({colorPalette, activeColor, setActiveColor}: props) {
  if (colorPalette && colorPalette.length === 0) return null;
    
  return (
    <div className='color-palette pb-2 mb-4'>
        {colorPalette.map((color, index) => (
            <div 
                key={index}
                className='color-palette__color'
                style={{background: color, borderColor: color === activeColor? '#24A1DE': '#fff'}}
                onClick={() => setActiveColor(color)}
            />
        ))}
    </div>
  )
}

export default CustomUserStatusColorPicker