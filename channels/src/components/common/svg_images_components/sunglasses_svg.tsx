// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React from 'react';

type SvgProps = {
    width?: number;
    height?: number;
}

const SvgComponent = (props: SvgProps) => (
    <svg
    xmlns="http://www.w3.org/2000/svg"
    width={props.width ? props.width.toString() : '376'}
    height={props.height ? props.height.toString() : '376'}
    viewBox="0 0 117 117"
    fill="none">
        <g clipPath="url(#clip0_1946_4269)">
            <path d="M58.5 117C90.8087 117 117 90.8087 117 58.5C117 26.1913 90.8087 0 58.5 0C26.1913 0 0 26.1913 0 58.5C0 90.8087 26.1913 117 58.5 117Z" fill="#24A1DE"/>
            <path d="M117 58.5C117 90.8075 90.8075 117 58.5 117V0C90.8075 0 117 26.1925 117 58.5Z" fill="#24A1DE"/>
            <path d="M58.5 0C26.1925 0 0 26.1925 0 58.5C0 90.8075 26.1925 117 58.5 117C90.8075 117 117 90.8075 117 58.5C117 26.1925 90.8075 0 58.5 0ZM58.5 112.903C28.5028 112.903 4.09729 88.4972 4.09729 58.5C4.09729 28.5028 28.5028 4.09729 58.5 4.09729C88.4972 4.09729 112.903 28.5028 112.903 58.5C112.903 88.4972 88.4972 112.903 58.5 112.903Z" fill="#2B5B4F"/>
            <path d="M117 58.5C117 90.8075 90.8075 117 58.5 117V112.903C88.4972 112.903 112.903 88.4972 112.903 58.5C112.903 28.5028 88.4972 4.09729 58.5 4.09729V0C90.8075 0 117 26.1925 117 58.5Z" fill="#2B5B4F"/>
            <path d="M58.5 96.1457C79.2912 96.1457 96.1457 79.2912 96.1457 58.5C96.1457 37.7089 79.2912 20.8544 58.5 20.8544C37.7089 20.8544 20.8544 37.7089 20.8544 58.5C20.8544 79.2912 37.7089 96.1457 58.5 96.1457Z" fill="#FFDA6B"/>
            <path d="M79.8274 61.9003C80.1239 61.8996 80.4173 61.961 80.6888 62.0804C80.9602 62.1998 81.2037 62.3746 81.4036 62.5937C81.6035 62.8127 81.7554 63.0711 81.8495 63.3523C81.9437 63.6335 81.978 63.9313 81.9503 64.2266C81.375 70.0466 78.6561 75.4449 74.322 79.3717C69.9879 83.2985 64.3484 85.4734 58.5 85.4734C52.6515 85.4734 47.0121 83.2985 42.678 79.3717C38.3439 75.4449 35.6249 70.0466 35.0497 64.2266C35.022 63.9313 35.0563 63.6335 35.1505 63.3523C35.2446 63.0711 35.3965 62.8127 35.5964 62.5937C35.7963 62.3746 36.0398 62.1998 36.3112 62.0804C36.5827 61.961 36.8761 61.8996 37.1726 61.9003H79.8274Z" fill="#473535"/>
            <path d="M78.2255 64.6951C78.3329 64.695 78.4389 64.7186 78.5362 64.764C78.6335 64.8095 78.7196 64.8757 78.7885 64.9581C78.8574 65.0405 78.9073 65.137 78.9348 65.2408C78.9623 65.3446 78.9667 65.4532 78.9476 65.5589C78.5793 67.617 77.9005 69.6072 76.9344 71.4614C76.5812 72.1333 76.0506 72.6955 75.4004 73.0871C74.7502 73.4786 74.0051 73.6846 73.2461 73.6826H43.7539C42.9949 73.6846 42.2498 73.4786 41.5996 73.0871C40.9494 72.6955 40.4189 72.1333 40.0657 71.4614C39.0995 69.6072 38.4207 67.617 38.0524 65.5589C38.0334 65.4532 38.0377 65.3446 38.0652 65.2408C38.0927 65.137 38.1427 65.0405 38.2115 64.9581C38.2804 64.8757 38.3665 64.8095 38.4638 64.764C38.5611 64.7186 38.6672 64.695 38.7745 64.6951H78.2255Z" fill="white"/>
            <path d="M55.8218 41.6173V44.3321C55.8176 48.728 54.0696 52.9426 50.9615 56.0512C47.8533 59.1598 43.6389 60.9083 39.243 60.9132C34.3673 60.9077 29.6929 58.9683 26.2455 55.5204C22.7981 52.0726 20.8592 47.3979 20.8544 42.5222V41.6082C20.8568 39.0123 21.8889 36.5235 23.7242 34.6877C25.5595 32.852 28.0481 31.8193 30.644 31.8163H46.0299C48.6276 31.8193 51.1178 32.8532 52.9537 34.6908C54.7897 36.5285 55.8212 39.0197 55.8218 41.6173Z" fill="#433068"/>
            <path d="M96.1456 41.6172V42.5313C96.1408 47.407 94.202 52.0816 90.7545 55.5295C87.3071 58.9773 82.6327 60.9168 77.757 60.9222C73.3593 60.9174 69.1433 59.1676 66.0345 56.0571C62.9257 52.9467 61.1783 48.7297 61.1759 44.332V41.6172C61.1789 39.0212 62.2115 36.5323 64.0472 34.6966C65.8829 32.861 68.3718 31.8283 70.9678 31.8253H86.3538C88.9498 31.8283 91.4386 32.861 93.2743 34.6966C95.11 36.5323 96.1426 39.0212 96.1456 41.6172Z" fill="#433068"/>
            <path d="M86.3538 31.8253H70.9678C69.32 31.8266 67.6991 32.2439 66.2555 33.0384C64.8118 33.8329 63.5921 34.979 62.7093 36.3705H54.2908C53.4078 34.9787 52.1877 33.8324 50.7436 33.0379C49.2995 32.2433 47.6782 31.8262 46.0299 31.8253H30.6463C28.0493 31.8253 25.5587 32.857 23.7224 34.6933C21.886 36.5296 20.8544 39.0202 20.8544 41.6172V42.5313C20.8619 47.3765 22.7791 52.0234 26.1902 55.4644C29.6013 58.9054 34.2313 60.8632 39.0762 60.9131C48.2808 60.9885 55.8127 53.3721 55.8127 44.172V44.1035C55.809 43.5883 55.946 43.0819 56.209 42.639C56.4719 42.196 56.8509 41.8332 57.3049 41.5898C57.6456 41.3915 58.0289 41.278 58.4227 41.2589C58.8164 41.2399 59.2089 41.3157 59.5672 41.4801C60.0512 41.7102 60.4591 42.0743 60.7426 42.5291C61.0261 42.9839 61.1733 43.5104 61.1668 44.0463V44.1789C61.1668 53.3858 68.7078 60.9976 77.9056 60.9199C82.7535 60.8718 87.3869 58.9137 90.8 55.4706C94.2132 52.0274 96.1308 47.3772 96.1366 42.529V41.6149C96.136 39.0199 95.1053 36.5313 93.271 34.6957C91.4367 32.8601 88.9488 31.8277 86.3538 31.8253ZM52.8694 44.332C52.8652 47.9446 51.4282 51.4081 48.8737 53.9626C46.3191 56.5171 42.8557 57.9541 39.243 57.9584C35.1504 57.9541 31.2266 56.3265 28.3326 53.4325C25.4387 50.5386 23.811 46.6148 23.8068 42.5221V41.6081C23.8116 39.7958 24.5338 38.0592 25.8155 36.778C27.0972 35.4967 28.834 34.7751 30.6463 34.7709H46.0299C47.8422 34.7751 49.579 35.4967 50.8607 36.778C52.1424 38.0592 52.8646 39.7958 52.8694 41.6081V44.332ZM93.191 42.5221C93.1868 46.6144 91.5594 50.5378 88.666 53.4317C85.7725 56.3256 81.8493 57.9535 77.757 57.9584C74.1444 57.9541 70.6809 56.5171 68.1264 53.9626C65.5719 51.4081 64.1349 47.9446 64.1307 44.332V41.6172C64.1355 39.8054 64.8574 38.0691 66.1386 36.7879C67.4197 35.5067 69.156 34.7848 70.9678 34.78H86.3538C88.1657 34.7848 89.9019 35.5067 91.1831 36.7879C92.4643 38.0691 93.1862 39.8054 93.191 41.6172V42.5221Z" fill="#5D519D"/>
        </g>
        <defs>
            <clipPath id="clip0_1946_4269">
                <rect width="117" height="117" fill="white"/>
            </clipPath>
        </defs>
    </svg>
);

export default SvgComponent;
