// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React from 'react';

type SvgProps = {
    width?: number;
    height?: number;
}

const SvgComponent = (props: SvgProps) => (
    <svg
    xmlns="http://www.w3.org/2000/svg"
    width={props.width ? props.width.toString() : '376'}
    height={props.height ? props.height.toString() : '376'}
    viewBox="0 0 117 117"
    fill="none">
        <g clipPath="url(#clip0_1946_4287)">
            <path d="M58.5 27.6504C41.4621 27.6504 27.6504 41.4621 27.6504 58.5C27.6504 75.5379 41.4621 89.3496 58.5 89.3496C75.5379 89.3496 89.3496 75.5379 89.3496 58.5C89.3496 41.4621 75.5379 27.6504 58.5 27.6504ZM58.5 68.7832C52.8296 68.7832 48.2168 64.1704 48.2168 58.5C48.2168 52.8296 52.8296 48.2168 58.5 48.2168C64.1704 48.2168 68.7832 52.8296 68.7832 58.5C68.7832 64.1704 64.1704 68.7832 58.5 68.7832Z" fill="#2B5B4F"/>
            <path d="M68.7832 58.5C68.7832 64.1704 64.1704 68.7832 58.5 68.7832V89.3496C75.5379 89.3496 89.3496 75.5379 89.3496 58.5C89.3496 41.4621 75.5379 27.6504 58.5 27.6504V48.2168C64.1704 48.2168 68.7832 52.8296 68.7832 58.5Z" fill="#2B5B4F"/>
            <path d="M114.121 48.2854L101.278 46.1602C100.387 42.9379 99.0844 39.9215 97.5076 36.9736L104.912 26.5531C105.871 25.1822 105.734 23.3312 104.569 22.1658L94.834 12.431C93.6688 11.2658 91.8176 11.1285 90.4465 12.0882L80.0262 19.4924C77.0783 17.9154 74.0619 16.6133 70.8396 15.7219L68.7144 2.87907C68.4404 1.23398 67.0008 0 65.3555 0H51.6445C49.9992 0 48.5596 1.23398 48.2854 2.87907L46.1602 15.7219C42.9379 16.6131 39.9215 17.9152 36.9736 19.4924L26.5535 12.0882C25.1824 11.1285 23.3312 11.2656 22.166 12.431L12.4315 22.166C11.2658 23.3312 11.1289 25.1824 12.0885 26.5533L19.4924 36.9738C17.9159 39.9217 16.6133 42.9381 15.7219 46.1604L2.87907 48.2856C1.23398 48.5596 0 49.9992 0 51.6445V65.3555C0 67.0008 1.23398 68.4404 2.87907 68.7147L15.7219 70.8398C16.6131 74.0617 17.9156 77.0781 19.4924 80.0264L12.0882 90.4465C11.1287 91.8176 11.2656 93.6688 12.4313 94.834L22.166 104.569C23.3312 105.734 25.1824 105.871 26.5535 104.911L36.9738 97.5076C39.9217 99.0841 42.9381 100.387 46.1604 101.278L48.2856 114.12C48.5596 115.766 49.9992 117 51.6445 117H65.3555C67.0008 117 68.4404 115.766 68.7147 114.12L70.8398 101.278C74.0621 100.387 77.0785 99.0844 80.0264 97.5076L90.4465 104.911C91.8176 105.871 93.6688 105.734 94.834 104.569L104.569 94.834C105.734 93.6688 105.871 91.8176 104.912 90.4465L97.5076 80.0262C99.0841 77.0779 100.387 74.0615 101.278 70.8396L114.121 68.7144C115.766 68.4404 117 67.0008 117 65.3555V51.6445C117 49.9992 115.766 48.5596 114.121 48.2854ZM58.5 82.4941C45.2689 82.4941 34.5059 71.7311 34.5059 58.5C34.5059 45.2689 45.2689 34.5059 58.5 34.5059C71.7311 34.5059 82.4941 45.2689 82.4941 58.5C82.4941 71.7311 71.7311 82.4941 58.5 82.4941Z" fill="#24A1DE"/>
            <path d="M117 51.6445V65.3555C117 67.0008 115.766 68.4404 114.121 68.7147L101.278 70.8398C100.387 74.0617 99.0844 77.0781 97.5076 80.0264L104.912 90.4465C105.871 91.8176 105.734 93.6688 104.569 94.834L94.834 104.569C93.6688 105.734 91.8176 105.871 90.4465 104.911L80.0262 97.5076C77.0783 99.0841 74.0619 100.387 70.8396 101.278L68.7144 114.12C68.4404 115.766 67.0008 117 65.3555 117H58.5V82.4941C71.7311 82.4941 82.4941 71.7311 82.4941 58.5C82.4941 45.2689 71.7311 34.5059 58.5 34.5059V0H65.3555C67.0008 0 68.4404 1.23398 68.7147 2.87907L70.8398 15.7219C74.0621 16.6131 77.0785 17.9152 80.0264 19.4924L90.4465 12.0882C91.8176 11.1285 93.6688 11.2656 94.834 12.431L104.569 22.166C105.734 23.3312 105.871 25.1824 104.912 26.5533L97.5076 36.9738C99.0841 39.9217 100.387 42.9381 101.278 46.1604L114.121 48.2856C115.766 48.5596 117 49.9992 117 51.6445Z" fill="#24A1DE"/>
        </g>
        <defs>
            <clipPath id="clip0_1946_4287">
                <rect width="117" height="117" fill="white"/>
            </clipPath>
        </defs>
    </svg>
);

export default SvgComponent;
