// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React from 'react';

type SvgProps = {
    width?: number;
    height?: number;
}

const SvgComponent = (props: SvgProps) => (
    <svg
        xmlns='http://www.w3.org/2000/svg'
        width={props.width ? props.width.toString() : '376'}
        height={props.height ? props.height.toString() : '376'}
        viewBox='0 0 117 117'
        fill='none'
    >
        <path
            d='M91.4577 14.1248V95.8129L77.3329 102.875V7.06226L91.4577 14.1248Z'
            fill='#2B5B4F'
        />
        <path
            d='M84.3954 7.06226V102.875L25.5424 95.8129V14.1248L84.3954 7.06226Z'
            fill='#24A1DE'
        />
        <path
            d='M84.3954 95.813L77.3329 117H84.3952C88.2795 117 91.4574 113.822 91.4574 109.938V95.813H84.3954Z'
            fill='#9F9F9F'
        />
        <path
            d='M84.3954 0H77.3331L84.3954 14.1248H91.4577V7.06228C91.4577 3.17797 88.2797 0 84.3954 0Z'
            fill='#9F9F9F'
        />
        <path
            d='M84.3954 95.813V109.938C84.3954 113.822 81.2175 117 77.3331 117H32.6046C28.7203 117 25.5424 113.822 25.5424 109.938V95.813H84.3954Z'
            fill='#BFBFBF'
        />
        <path
            d='M84.3954 7.06228V14.1246H25.5424V7.06228C25.5424 3.17797 28.7203 0 32.6046 0H77.3329C81.2172 0 84.3954 3.17797 84.3954 7.06228Z'
            fill='#BFBFBF'
        />
        <path
            d='M65.5623 8.82802H58.5C57.5249 8.82802 56.7345 8.03758 56.7345 7.06251C56.7345 6.08743 57.5249 5.297 58.5 5.297H65.5623C66.5374 5.297 67.3278 6.08743 67.3278 7.06251C67.3278 8.03758 66.5374 8.82802 65.5623 8.82802Z'
            fill='#808080'
        />
        <path
            d='M58.5 109.938C60.4503 109.938 62.0313 108.357 62.0313 106.406C62.0313 104.456 60.4503 102.875 58.5 102.875C56.5497 102.875 54.9688 104.456 54.9688 106.406C54.9688 108.357 56.5497 109.938 58.5 109.938Z'
            fill='white'
        />
        <path
            d='M51.4378 8.82778C52.4128 8.82778 53.2033 8.03733 53.2033 7.06226C53.2033 6.0872 52.4128 5.29675 51.4378 5.29675C50.4627 5.29675 49.6722 6.0872 49.6722 7.06226C49.6722 8.03733 50.4627 8.82778 51.4378 8.82778Z'
            fill='#808080'
        />
    </svg>
);

export default SvgComponent;
