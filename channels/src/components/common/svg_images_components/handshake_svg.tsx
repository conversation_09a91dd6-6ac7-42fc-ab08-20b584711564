// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React from 'react';

type SvgProps = {
    width?: number;
    height?: number;
}

const SvgComponent = (props: SvgProps) => (
    <svg xmlns="http://www.w3.org/2000/svg"
    width={props.width ? props.width.toString() : '376'}
    height={props.height ? props.height.toString() : '376'}
    viewBox="0 0 117 117"
    fill="none">
        <path d="M95.5781 56.9237L92.606 32.7804L87.0367 33.466L87.039 33.4621L76.2417 27.3856C75.0899 26.7376 73.7682 26.4556 72.4522 26.5772L55.4036 28.1539C53.3079 28.3477 51.4352 29.5435 50.3779 31.3634L38.3617 52.0458C37.4744 53.573 38.1709 55.5316 39.8233 56.1556L40.3148 56.3412C41.2604 56.6984 42.2323 56.9052 43.2037 56.9861L41.6811 58.3121L38.635 60.9661L29.9713 68.3563C28.4291 69.6997 28.2689 72.0374 29.6123 73.5797L30.4912 74.5863C32.2165 76.5648 35.2194 76.7702 37.2015 75.0444L40.5127 78.8424C41.5892 80.0786 43.1664 80.6255 44.6817 80.439C45.5923 80.327 46.4808 79.9495 47.2228 79.3005L50.5303 83.0987C51.6107 84.3381 53.1877 84.8813 54.703 84.6948C55.6136 84.5826 56.5023 84.2088 57.2445 83.56L59.042 85.1135C60.7678 87.0954 63.7741 87.3004 65.7563 85.5746L68.1998 83.4428L88.7423 67.444L88.7645 67.4237L96.8694 67.4157L95.5781 56.9237Z" fill="#F29F5C"/>
        <path d="M86.7741 61.2038L86.7519 61.2241L66.2094 77.2229L63.7659 79.3547C61.784 81.0805 58.7774 80.8755 57.0517 78.8936L55.2542 77.3401C54.5119 77.9889 53.623 78.363 52.7126 78.4749C51.1973 78.6614 49.6206 78.1182 48.5399 76.8788L45.2324 73.0806C44.4902 73.7294 43.6017 74.1069 42.6913 74.2191C41.1761 74.4056 39.5988 73.8589 38.5223 72.6225L35.2111 68.8245C33.5142 70.3021 31.0709 70.3597 29.3198 69.1049C28.4217 70.4591 28.4935 72.2945 29.613 73.5797L30.4919 74.5863C32.2171 76.5648 35.2201 76.7702 37.2022 75.0445L40.5134 78.8424C41.5899 80.0787 43.1671 80.6255 44.6824 80.439C45.593 80.3271 46.4813 79.9495 47.2235 79.3006L50.531 83.0989C51.6114 84.3384 53.1884 84.8816 54.7037 84.6951C55.6143 84.5829 56.503 84.2091 57.2452 83.5603L59.0427 85.1137C60.7685 87.0957 63.7748 87.3006 65.7569 85.5749L68.2004 83.4431L88.743 67.4443L88.7652 67.424L96.8701 67.416L95.9734 60.1318L86.7741 61.2038Z" fill="#D37D42"/>
        <path d="M90.5149 71.6469L79.5084 62.1567L76.4477 59.5196L74.2576 57.6334L64.8412 49.5166L65.9287 40.2595C66.2966 37.1286 64.3257 34.1974 61.2874 33.3567L40.761 27.677C38.5892 27.076 36.2938 28.0837 35.2664 30.0894L32.6851 35.1282L21.6049 34.3761L18.7667 58.5356L17.5332 69.0347L28.3186 69.752L28.341 69.7724L50.873 89.1945L53.3284 91.3128C55.3199 93.0276 58.3251 92.806 60.0399 90.8145L63.3259 87.0015C64.0716 87.6461 64.9626 88.0152 65.8739 88.1223C67.3901 88.3006 68.9641 87.7485 70.0377 86.5031L73.3241 82.6866C74.0698 83.3313 74.9603 83.704 75.8716 83.8109C77.3878 83.9892 78.9621 83.4337 80.0317 82.1912L83.3217 78.375C85.3132 80.0898 88.3149 79.8677 90.0293 77.8796L90.9026 76.868C92.2376 75.3187 92.0644 72.9819 90.5149 71.6469Z" fill="#F9C295"/>
        <path d="M90.515 71.6469L87.1624 68.7562C88.252 70.2011 88.2296 72.2566 86.9999 73.6839L86.0728 74.7577C84.253 76.868 81.0666 77.1039 78.9523 75.2835L75.4597 79.3346C74.3242 80.6534 72.6531 81.243 71.0434 81.054C70.0761 80.9404 69.1308 80.5448 68.3392 79.8604L64.8502 83.912C63.7106 85.234 62.04 85.8199 60.4303 85.6309C59.463 85.5173 58.5172 85.1254 57.7256 84.441L54.2371 88.4889C53.5799 89.2524 52.7428 89.7668 51.8436 90.0319L53.3287 91.3131C55.3203 93.0279 58.3254 92.8063 60.0402 90.8148L63.3263 87.0018C64.0719 87.6464 64.9629 88.0154 65.8742 88.1226C67.3904 88.3006 68.9644 87.7488 70.038 86.5034L73.3245 82.6869C74.0701 83.3316 74.9606 83.7043 75.872 83.8112C77.3881 83.9895 78.9624 83.434 80.0321 82.1915L83.322 78.3753C85.3135 80.0901 88.3153 79.868 90.0296 77.8799L90.903 76.8683C92.2377 75.3187 92.0645 72.9819 90.515 71.6469Z" fill="#E59F6A"/>
        <path d="M84.4362 77.1045L78.4052 71.9044C77.6888 71.2865 76.6064 71.3665 75.988 72.0833C75.3699 72.8002 75.4501 73.8824 76.1669 74.5006L82.1872 79.6915L83.322 78.3753C83.7749 78.7653 84.2815 79.0498 84.8119 79.2416C85.2033 78.5448 85.0646 77.6463 84.4362 77.1045Z" fill="#E59F6A"/>
        <path d="M74.7936 83.5511C75.2065 82.8507 75.0742 81.9346 74.4366 81.3848L69.019 76.7137C68.3022 76.0958 67.2201 76.1758 66.6018 76.8927C65.9836 77.6095 66.0639 78.6918 66.7807 79.3099L72.1983 83.981C72.2006 83.9828 72.2029 83.9844 72.2052 83.9862L73.3245 82.6864C73.7707 83.0724 74.2698 83.3587 74.7936 83.5511Z" fill="#E59F6A"/>
        <path d="M28.8809 69.7278L27.5754 71.4517C26.1538 73.329 26.5231 76.0033 28.4003 77.4251C30.2776 78.8467 32.9519 78.4774 34.3737 76.6002L35.6792 74.8763C37.1008 72.999 36.7315 70.3247 34.8542 68.9029C32.977 67.4813 30.3025 67.8506 28.8809 69.7278Z" fill="#F29F5C"/>
        <path d="M36.8323 73.3537L34.3737 76.6002C32.9521 78.4774 33.3214 81.1517 35.1986 82.5736C37.0759 83.9951 39.7502 83.6259 41.172 81.7486L43.6306 78.5021C45.0522 76.6249 44.6829 73.9506 42.8057 72.5287C40.9282 71.1071 38.2539 71.4764 36.8323 73.3537Z" fill="#F29F5C"/>
        <path d="M44.5225 77.3241L41.1718 81.7486C39.7502 83.6259 40.1195 86.3002 41.9967 87.722C43.874 89.1436 46.5483 88.7743 47.9701 86.897L51.3208 82.4725C52.7424 80.5953 52.3731 77.921 50.4959 76.4992C48.6186 75.0774 45.9443 75.4469 44.5225 77.3241Z" fill="#F29F5C"/>
        <path d="M51.5153 82.2157L47.9701 86.8969C46.5486 88.7741 46.9178 91.4484 48.7951 92.8702C50.6723 94.2918 53.3466 93.9225 54.7685 92.0453L58.3136 87.3642C59.7352 85.4869 59.3659 82.8126 57.4887 81.3908C55.6112 79.9692 52.9369 80.3385 51.5153 82.2157Z" fill="#F29F5C"/>
        <path d="M87.039 33.4621L76.2417 27.3856C75.0899 26.7376 73.7682 26.4556 72.4522 26.5772L55.4036 28.1539C53.3079 28.3477 51.4352 29.5435 50.3779 31.3634L38.3617 52.0458C37.4744 53.5732 38.1709 55.5316 39.8233 56.1556L40.3148 56.3412C44.7194 58.0048 49.6937 56.5891 52.5623 52.8559L57.1662 46.8642C57.1662 46.8642 68.2329 59.6777 80.4173 44.7139" fill="#F29F5C"/>
        <path d="M116.987 70.6442L111.34 24.7697C111.284 24.3186 111.052 23.9079 110.693 23.6282C110.335 23.3485 109.881 23.2229 109.429 23.2782L96.2239 24.9038C92.7941 25.3261 90.3474 28.46 90.7697 31.8895L95.3043 68.7261C95.6946 71.8972 98.4028 74.2278 101.52 74.2278C101.774 74.2278 102.032 74.2123 102.29 74.1805L115.495 72.5548C116.435 72.439 117.103 71.5837 116.987 70.6442Z" fill="#24A1DE"/>
        <path d="M116.987 70.6442L116.367 65.6086C116.086 66.0211 115.637 66.3159 115.103 66.3817L101.26 68.0857C100.989 68.1191 100.719 68.1353 100.453 68.1353C98.1219 68.1353 96.0102 66.8917 94.842 64.9727L95.3041 68.7258C95.6944 71.8969 98.4025 74.2275 101.52 74.2275C101.774 74.2275 102.031 74.212 102.29 74.1802L115.495 72.5546C116.435 72.4389 117.103 71.5836 116.987 70.6442Z" fill="#2B5B4F"/>
        <path d="M20.7761 24.9035L7.57068 23.2779C7.1189 23.2223 6.66485 23.3482 6.30653 23.6279C5.94822 23.9076 5.71582 24.3183 5.6603 24.7694L0.0130106 70.6442C-0.102618 71.5836 0.565331 72.4389 1.50453 72.5545L14.7099 74.1802C14.9686 74.212 15.2257 74.2275 15.48 74.2275C18.597 74.2275 21.3053 71.8967 21.6956 68.7258L26.2303 31.8892C26.6526 28.4597 24.2059 25.3258 20.7761 24.9035Z" fill="#24A1DE"/>
        <path d="M0.0130142 70.6442L0.632975 65.6086C0.914048 66.0211 1.36285 66.3159 1.89712 66.3817L15.7399 68.0857C16.0109 68.1191 16.2808 68.1353 16.5472 68.1353C18.8781 68.1353 20.9898 66.8917 22.1579 64.9727L21.6959 68.7258C21.3056 71.8969 18.5974 74.2275 15.4803 74.2275C15.2262 74.2275 14.9686 74.212 14.7102 74.1802L1.50476 72.5546C0.565106 72.4389 -0.102614 71.5836 0.0130142 70.6442Z" fill="#2B5B4F"/>
        <path d="M106.031 66.6119C107.86 66.6119 109.344 65.1286 109.344 63.2989C109.344 61.4691 107.86 59.9858 106.031 59.9858C104.201 59.9858 102.718 61.4691 102.718 63.2989C102.718 65.1286 104.201 66.6119 106.031 66.6119Z" fill="#EBEEF2"/>
        <path d="M10.7836 66.6119C12.6133 66.6119 14.0966 65.1286 14.0966 63.2989C14.0966 61.4691 12.6133 59.9858 10.7836 59.9858C8.95386 59.9858 7.47058 61.4691 7.47058 63.2989C7.47058 65.1286 8.95386 66.6119 10.7836 66.6119Z" fill="#EBEEF2"/>
</svg>
);

export default SvgComponent;
