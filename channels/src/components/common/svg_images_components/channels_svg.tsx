// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React from 'react';

type SvgProps = {
    width?: number;
    height?: number;
}

export default (props: SvgProps) => (
    <svg xmlns="http://www.w3.org/2000/svg" width={props.width ? props.width.toString() : '104'}
    height={props.height ? props.height.toString() : '104'}
    viewBox="0 0 117 117"
    fill="none">
        <g clipPath="url(#clip0_1946_3793)">
            <g opacity="0.4">
                <path d="M102.375 44.1282H95.0625V62.4094C95.0625 72.5043 86.8762 80.6907 76.7812 80.6907H43.875V88.0032C43.875 96.0798 50.4233 102.628 58.5 102.628H80.4375L96.3788 115.9C97.929 117.191 100.236 116.983 101.527 115.432C102.075 114.774 102.375 113.944 102.375 113.085V102.628C110.452 102.628 117 96.0798 117 88.0032V58.7532C117 50.6765 110.452 44.1282 102.375 44.1282Z" fill="#3EB798"/>
            </g>
            <path d="M73.125 0.253174H14.625C6.54834 0.253174 0 6.80152 0 14.8782V58.7532C0 66.8298 6.54834 73.3782 14.625 73.3782V87.3816C14.625 89.3999 16.263 91.0379 18.2812 91.0379C19.1514 91.0379 19.996 90.7271 20.6578 90.1604L40.2188 73.3782H73.125C81.2017 73.3782 87.75 66.8298 87.75 58.7532V14.8782C87.75 6.80152 81.2017 0.253174 73.125 0.253174ZM27.0928 41.971C26.7711 42.3256 26.4018 42.6328 25.9959 42.8851C25.6011 43.152 25.1879 43.386 24.7528 43.5797C24.2994 43.7479 23.8351 43.8832 23.3634 43.9819C22.8954 44.0843 22.4165 44.1318 21.9375 44.1282C17.8973 44.1282 14.625 40.8558 14.625 36.8157C14.5482 32.7792 17.7584 29.441 21.7949 29.3642C22.3214 29.3533 22.8479 29.4008 23.3634 29.5032C23.8351 29.6019 24.2994 29.7372 24.7528 29.9054C25.1952 30.0882 25.612 30.3185 25.9959 30.6001C26.3945 30.8523 26.7601 31.1448 27.0928 31.4776C28.5114 32.8889 29.2902 34.8157 29.25 36.8157C29.2427 38.7535 28.4676 40.6072 27.0928 41.971ZM49.0303 41.971C47.6665 43.3457 45.8128 44.1209 43.875 44.1282C43.3997 44.183 42.9244 44.183 42.4491 44.1282C41.9774 44.0295 41.5131 43.8942 41.0597 43.726C40.6246 43.5322 40.2114 43.2982 39.8166 43.0313C39.4071 42.7681 39.0268 42.4646 38.6831 42.1172C37.2864 40.7096 36.5223 38.7974 36.5625 36.8157C36.5077 36.3404 36.5077 35.865 36.5625 35.3897C36.6612 34.9181 36.7965 34.4537 36.9647 34.0004C37.1475 33.558 37.3778 33.1411 37.6594 32.7572C37.919 32.3551 38.2151 31.9748 38.5369 31.6238C38.8879 31.3021 39.2681 31.0059 39.6703 30.7463C40.0542 30.4648 40.471 30.2344 40.9134 30.0516C41.4107 29.8249 41.9226 29.6421 42.4491 29.5032C46.4088 28.7134 50.2588 31.2874 51.0486 35.2471C51.1509 35.7627 51.1985 36.2892 51.1875 36.8157C51.1875 38.7462 50.427 40.5999 49.0669 41.971H49.0303ZM70.9678 41.971C70.6461 42.3256 70.2768 42.6328 69.8709 42.8851C69.4761 43.152 69.0629 43.386 68.6278 43.5797C68.1744 43.7479 67.7101 43.8832 67.2384 43.9819C66.7704 44.0843 66.2915 44.1318 65.8125 44.1282C61.7723 44.1282 58.5 40.8558 58.5 36.8157C58.4232 32.7792 61.6334 29.441 65.6699 29.3642C66.1964 29.3533 66.7229 29.4008 67.2384 29.5032C67.7101 29.6019 68.1744 29.7372 68.6278 29.9054C69.0702 30.0882 69.487 30.3185 69.8709 30.6001C70.2695 30.8523 70.6351 31.1448 70.9678 31.4776C72.3864 32.8889 73.1652 34.8157 73.125 36.8157C73.1177 38.7535 72.3426 40.6072 70.9678 41.971Z" fill="#24A1DE"/>
        </g>
        <defs>
            <clipPath id="clip0_1946_3793">
                <rect width="117" height="117" fill="white"/>
            </clipPath>
        </defs>
    </svg>
);
