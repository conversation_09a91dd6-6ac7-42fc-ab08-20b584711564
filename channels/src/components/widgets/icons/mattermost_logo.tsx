// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React from 'react';
import type {CSSProperties} from 'react';
import {useIntl} from 'react-intl';

export default function MattermostLogo(props: React.HTMLAttributes<HTMLSpanElement>) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const {formatMessage} = useIntl();
    return (
        <span {...props}>

            <svg
                width='32'
                height='32'
                viewBox='0 0 96 96'
                fill='none'
                xmlns='http://www.w3.org/2000/svg'
            >
                <path
                    d='M27.9752 0.0782858C27.1151 0.216212 23.7098 1.6721 8.66649 8.36916C5.47177 9.7944 2.54035 11.189 2.13662 11.4955C1.66268 11.848 1.15363 12.461 0.732352 13.2272L0.0653225 14.3919L0.0126623 47.4175C-0.0224445 71.5698 0.0126623 80.7036 0.15309 81.3779C0.416391 82.5886 1.08342 83.6307 2.10152 84.4123C2.68078 84.8874 6.1388 86.4812 14.74 90.2818C21.2523 93.1629 26.9922 95.6302 27.5188 95.7682C28.7125 96.0747 29.4673 96.0747 30.7662 95.7835C32.8726 95.3084 34.628 93.8372 35.2774 92.0135C35.5934 91.1093 35.611 89.7454 35.611 47.9845C35.611 9.10477 35.5759 4.79841 35.3301 4.06281C34.3998 1.33494 31.1875 -0.396792 27.9752 0.0782858Z'
                    fill='#24A1DE'
                />
                <path
                    d='M65.7687 0.0956987C63.7676 0.402201 61.8718 1.70483 60.9415 3.43657L60.4675 4.3101L60.4149 47.5882C60.3798 95.5557 60.292 91.939 61.5207 93.5021C62.9952 95.3718 65.6809 96.3373 68.1033 95.8929C68.7176 95.7703 73.2991 93.8393 81.2157 90.3452C92.8185 85.2113 93.4153 84.9354 94.2403 84.1385C94.7318 83.6788 95.2935 82.9278 95.5217 82.4221L95.9254 81.5486L95.9781 48.4464C96.0307 13.6891 96.0483 14.394 95.2584 13.0454C94.3807 11.5282 93.9594 11.2983 81.3561 5.75066C74.8964 2.88487 69.2793 0.463501 68.8932 0.3409C67.9453 0.0343983 66.7868 -0.0575523 65.7687 0.0956987Z'
                    fill='#24A1DE'
                />
            </svg>
        </span>
    );
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const style: CSSProperties = {
    fillRule: 'evenodd',
    clipRule: 'evenodd',
};
