@use 'utils/mixins';
@use 'utils/variables';

.TeamIcon__content {
    display: flex;
    overflow: hidden;
    height: 100%;
    flex-direction: column;
    justify-content: center;
    border-radius: 8px;
    transition: all 0.25s ease;

    &.no-hover {
        transition: none;
    }
}

.TeamIcon__initials {
    font-family: 'GraphikArabic', sans-serif;
    letter-spacing: 0.02em;
    user-select: none;

    html:lang(ar) & {
        font-family: 'GraphikArabic', sans-serif;
    }

    &.TeamIcon__initials__lg {
        font-size: 3em;
    }

    &.TeamIcon__initials__sm {
        font-size: 14px;
    }
}

.TeamIcon {
    position: relative;
    width: 34px;
    height: 34px;
    box-sizing: content-box;
    border: 3px solid #24A1DE;
    border-radius: 8px;
    background-color: var(--sidebar-header-bg);
    font-weight: 600;
    text-align: center;
    text-transform: uppercase;
    transform-origin: center center;
    transition: box-shadow 0.3s, background-color 0.3s, border-color 0.3s;
    will-change: box-shadow, background-color, border-color;

    &:not(.no-hover):hover {
        border-color: var(--sidebar-teambar-bg);
        background-color: rgba(var(--sidebar-text-rgb), 0.3);
        box-shadow: 0 0 0 3px #24A1DE;
    }

    &:active {
        border-color: var(--sidebar-teambar-bg);
        background-color: rgba(var(--sidebar-text-rgb), 0.3);
        box-shadow: 0 0 0 3px #24A1DE;
    }

    &.active {
        border-color: var(--sidebar-teambar-bg);
        background-color: rgba(var(--sidebar-text-rgb), 0.3);
        box-shadow: 0 0 0 3px #24A1DE;
    }

    &.TeamIcon__lg {
        width: 94px;
        height: 94px;
    }
    &.TeamIcon__mms{
        width: auto;
        height: auto;
        max-width: 86px;
        min-height: 34px;
        .TeamIcon__initials__sm{
          display: flex;
          justify-content: center;
          padding: 3px 2px 0px 2px;
        }
    
    }
    &.TeamIcon__sm {
        width: 34px;
        height: 34px;
    }

    &.withImage {
        border: none;

        &.TeamIcon__lg {
            width: 100px;
            height: 100px;
        }

        &.TeamIcon__sm {
            width: 40px;
            height: 40px;
        }

        .TeamIcon__image {
            border-radius: 8px;
            transition: box-shadow 200ms;
        }

        &:not(.no-hover):hover {
            box-shadow: 0 0 0 3px #effedd;

            .TeamIcon__image {
                box-shadow: 0 0 0 3px #effedd;
            }
        }
    }

    &.withImage.active {
        box-shadow: 0 0 0 3px #effedd;

        .TeamIcon__image {
            box-shadow: 0 0 0 3px #effedd;
        }
    }

    .TeamIcon__image {
        @include mixins.clearfix;
        box-shadow: 0 0 0 3px #effedd;
        width: 100%;
        height: 100%;
        background-color: var(--sidebar-header-bg);
        background-repeat: no-repeat;
        background-size: 70% 70%;
        background-position: center;
        border: solid 2px #24A1DE;
    }
}
