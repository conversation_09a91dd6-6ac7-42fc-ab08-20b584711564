@use "sass:color";

.Avatar {
  -webkit-user-select: none; /* Chrome all / Safari all */
  -moz-user-select: none; /* Firefox all */
  -ms-user-select: none; /* IE 10+ */
  user-select: none;
  vertical-align: sub;

  &,
  &:focus,
  &.a11y--focused {
    border-radius: 50%;
  }

  &.Avatar-xxs {
    width: 16px;
    min-width: 16px;
    height: 16px;
    font-size: 8px;
  }

  &.Avatar-xs {
    width: 33px;
    min-width: 32px;
    height: 33px;
    font-size: 9.5px;
    background: #24A1DE;
  }

  &.Avatar-sm {
    width: 24px;
    min-width: 24px;
    height: 24px;
    font-size: 10px;
  }

  &.Avatar-md {
    width: 32px;
    min-width: 32px;
    height: 32px;
    font-size: 12px;
    background: #24A1DE;
  }

  &.Avatar-lg {
    width: 36px;
    min-width: 36px;
    height: 36px;
    font-size: 14px;
  }

  &.Avatar-xl {
    width: 50px;
    min-width: 50px;
    height: 50px;
    font-size: 18px;
  }

  &.Avatar-xxl {
    width: 128px;
    min-width: 128px;
    height: 128px;
    font-size: 44px;
  }

  &.Avatar-plain {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: white;

    &::before {
      position: absolute;
      display: inline-flex;
      width: 100%;
      height: 100%;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      color: rgba(var(--center-channel-color-rgb), 0.75);
      content: attr(data-content);
    }
  }
}
