.flip-card {
    width: 300px;
    height: 450px;
    perspective: 1000px;
    cursor: pointer;
    transition: transform 0.3s;
    transform-style: preserve-3d;
  
    &:hover {
      transform: scale(1.05);
    }
  
    &.flipped .card-inner {
      transform: rotateY(180deg);
    }
  }
  
  .card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.8s;
    transform-style: preserve-3d;
  }
  
  .card-side {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    background: white;
    border: 1px solid #24A1DE;
    border-radius: 5px 5px 0 0;
  }
  
  .card-front {
    display: flex;
    flex-direction: column;
    .image-container {
      width: 100%;
      height: 80%;
      border-radius: 10px 10px 0 0;
      display: flex;
      justify-content: center;
    }
    .card-image {
      width: 85%;
      height: 110%;
      background: rgb(36 161 222 / 22%);
      border-radius: 10px 10px 0 0;
      margin-top: -32px;
      padding-top: 11px;
      img {
        width: 96% !important;
        height: 90%;
        object-fit: contain;
        border-top-left-radius: 15px;
        border-top-right-radius: 15px;
        max-width: 100% !important;
      }
    }
  }
  
  .card-back {
      transform: rotateY(180deg);
      background: #fff;
      display: flex;
      flex-direction: column;
      padding:0 30px;
      color: white;
      position: relative;
      padding-top: 35px;
      overflow: hidden;
  
    &::before {
      content: "";
      position: absolute;
      top: -186px;
      left: 0;
      right: 1px;
      bottom: 0;
      background-image: url("../../../../images/ML/Patterny.png");
      background-size: 20px 20px;
      z-index: -1;
      border-radius: 15px;
      background-repeat: no-repeat;
      background-size: cover;
      opacity: 1;
      height: 154%;
      width: 173%;
    }
  
    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    
      z-index: -1;
      border-radius: 15px;
    }
    .back-title{
      color: #050922;
    }
  }
  .card-content {
    width: 100%;
    height: 20%;
    background: #24A1DE;
    border-radius: 20px 20px 0 0px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 16px;
    .card-title {
      color: #fff;
      font-size: 16px;
      font-weight: 600;
      line-height: 23px;
  
   
    }
  }
  
  .card-description1 {
    margin-top: 58px;
    display: flex;
    flex-direction: column;
    padding: 10px;
    height: 100%;
    width: 100%;
    justify-content: start;
    align-items: flex-start;
    p {
      color: #050922;
      font-size: 11px;
      text-align: right;
      line-height: 17px;
      font-weight: 600;
      font-family: 'GraphikArabic';
      &::before {
        content: "";
        position: absolute;
        right: 30px;
        height: 5px;
        width: 5px;
        background: #3d3d3d;
        margin-top: 7px;
        border-radius: 30%;
       
      }
    }
  }
  