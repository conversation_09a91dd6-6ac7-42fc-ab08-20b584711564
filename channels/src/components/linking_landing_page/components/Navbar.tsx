import React, { useState } from "react";
import "../styles/main.scss";
import { Link } from "react-router-dom";
import img from "../../../images/icon50x50.png";

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false); // State to manage menu visibility

  const toggleMenu = () => {
    setIsOpen(!isOpen); // Toggle the menu open/close state
  };

  return (
    <nav className="menu">
      <div className="nav-container">
        <div className="brand">
          <img src={img} width={33} height={33} alt="Logo" />
          <span> تيليجرام</span>
        </div>

        <div className={`nav-links ${isOpen ? "active" : ""}`}>
          <a href="#about" className="nav-link">
            حول المنصة
          </a>
          <a href="#whyUs" className="nav-link">
            لماذا تختار منصتنا
          </a>
          <a href="#ourTrainingPrograms" className="nav-link">
            برامجنا التدريبية
          </a>
          <a href="#prices" className="nav-link">
            الأسعار
          </a>
        </div>
      </div>
      <div className="login-section">
        <Link to={"/login"} className="nav-login">
          <button className="login-button">تسجيل الدخول</button>
        </Link>
      </div>
      <div className="menu-icon" onClick={toggleMenu}>
        {isOpen ? "✖" : "☰"} {/* Toggle between X and menu icon */}
      </div>
    </nav>
  );
};

export default Navbar;