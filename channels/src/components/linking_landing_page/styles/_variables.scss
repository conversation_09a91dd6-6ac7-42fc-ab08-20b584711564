// Colors
$primary-color: #24A1DE;
$text-color: #040921;
$white: #ffffff;
$primary-color-dark: #2563eb;
$color-yellow: #facc15;
$color-gray-50: #f9fafb;
$color-gray-100: #f3f4f6;
$color-gray-900: #111827;
$transition-duration: 0.5s;

// Transitions
$transition-default: all 0.3s ease;

// Breakpoints
$breakpoint-md: 768px;

// Shadows
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
  0 4px 6px -2px rgba(0, 0, 0, 0.05);
$shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
  0 10px 10px -5px rgba(0, 0, 0, 0.04);
// Breakpoints
$mobile: 640px;
$tablet: 768px;
$desktop: 1100px;
$wide: 1480px;

// Common mixins
@mixin containerHome {
  max-width: $wide !important;
  width: $wide !important;
  padding: 0 0.5rem;
}

@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin button-base {
  padding: 0.5rem 1.5rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
}
