.image_preview {
    &__image {
        max-height: calc(100vh - 168px);
        border-radius: 8px;
        cursor: default;

        @media screen and (max-width: 768px) {
            border-radius: 0;
        }
    }
}
.imgge-scroll {
    overflow: auto; /* السماح بالتمرير */
}

/* تخصيص شريط التمرير */
.imgge-scroll::-webkit-scrollbar {
    width: 10px; /* زيادة حجم الشريط العمودي */
    height: 10px; /* زيادة حجم الشريط الأفقي */
}

/* تخصيص المسار (track) */
.imgge-scroll::-webkit-scrollbar-track {
    background-color: #f1f1f1; /* اللون الفاتح لمسار التمرير */
}

/* تخصيص المقبض (thumb) */
.imgge-scroll::-webkit-scrollbar-thumb {
    background-color: #24A1DE ; /* اللون الأحمر للمقبض */
    border-radius: 10px; /* جعل المقابض مدورة */
}

/* تخصيص المقبض عند التمرير فوقه */
.imgge-scroll::-webkit-scrollbar-thumb:hover {
    background-color: #24A1DE; /* اللون الأحمر الداكن عند التمرير */
}
