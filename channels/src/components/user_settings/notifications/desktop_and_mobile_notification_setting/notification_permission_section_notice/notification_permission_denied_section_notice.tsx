// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React from 'react';
import {useIntl} from 'react-intl';

import SectionNotice from 'components/section_notice';

export default function NotificationPermissionDeniedSectionNotice() {
    const intl = useIntl();

    return (
        <div className='extraContentBeforeSettingList'>
            <SectionNotice
                type='danger'
                title={intl.formatMessage({
                    id: 'user.settings.notifications.desktopAndMobile.notificationSection.permissionDenied.title',
                    defaultMessage: 'تم رفض إذن الإشعارات من المتصفح',
                })}
                text={intl.formatMessage({
                    id: 'user.settings.notifications.desktopAndMobile.notificationSection.permissionDenied.message',
                    defaultMessage: 'تفوتك إشعارات الرسائل والمكالمات الهامة من Telegram. لبدء تلقي الإشعارات، يرجى تمكين الإشعارات لـ Telegram في إعدادات المتصفح الخاص بك.',
                })}
            />
        </div>

    );
}
