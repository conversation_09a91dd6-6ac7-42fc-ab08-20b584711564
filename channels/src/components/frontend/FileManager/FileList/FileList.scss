

.files {
  position: relative;
  display: flex;
  align-content: flex-start;
  flex-wrap: wrap;
  column-gap: 0.5em;
  row-gap: 5px;
  height: calc(100% - (35px + 16px));
  overflow: auto;
  padding: 8px;
  padding-right: 20px;


  .file-item-container {
    border-radius: 5px;
    margin: 5px 0;
    border: 1px solid #e2e8f0;
    width: 182px;
    height: 164px;
    display: flex;
    justify-content: center;
  }

  .file-item {
    position: relative;
    height: 150px;
    width: 138px;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: center;
    justify-content: space-around;
    padding-top: 13px;
    padding-bottom: 1px;
    border-radius: 5px;

    .selection-checkbox {
      position: absolute;
      left: 0px;
      top: 15px !important;
    }

    .hidden {
      visibility: hidden;
    }

    .visible {
      visibility: visible;
    }

    .rename-file-container {
      position: absolute;
      top: 65px;
      width: 100%;
      text-align: center;
    }

    .rename-file-container.list {
      top: 6px;
      left: 48px;
      text-align: left;

      .rename-file {
        min-width: 15%;
        text-align: left;
        border-radius: 3px;
        border: none;
        top: 5px;
        white-space: nowrap;
        overflow-x: hidden;
        max-width: calc(100% - 48px);
      }

      .folder-name-error.right {
        left: 0px;
        bottom: -72px;
      }
    }

    .file-name {
      border-top: 1px solid #e2e8f0;
      width: 182px;
      text-align: center;
      margin-bottom: -10px;
      padding-top: 10px;
      font-size: 14px;
      font-family: "GraphikArabic";
      font-weight: normal;
      padding-left: 2px;
      padding-right: 2px;
    }
  }

  .file-selected {
    box-shadow: 0px 1px 4px 1px #24A1DE;
    background-color: transparent;
    border: 1px solid ;
  }

  .file-moving {
    opacity: 0.5;
  }

  .file-context-menu-list {
    font-size: 1.1em;

    ul {
      list-style-type: none;
      padding-right: 0;
      margin: 0;
      direction: rtl;

      li {
        display: flex;
        gap: 6px;
        align-items: center;
        padding: 6px 22px 6px 14px;

        &:hover {
          cursor: pointer;
          background-color: rgb(0, 0, 0, 0.04);
        }
      }

      li.disable-paste {
        opacity: 0.5;

        &:hover {
          cursor: default;
          background-color: transparent;
        }
      }
    }
  }
}

.files.list {
  flex-direction: column;
  flex-wrap: nowrap;
  gap: 5px;
  padding-top: 0px;
  justify-content: flex-start;

  .files-header {
    font-size: 0.83em;
    font-weight: 600;
    display: flex;
    gap: 5px;
    border-bottom: 1px solid #dddddd;
    padding: 10px 10px;
    position: sticky;
    top: 0;
    background-color: #f5f5f5;
    z-index: 1;

    .file-name {
      width: calc(70% - 65px);
      padding-left: 65px;
    }

    .file-date {
      text-align: center;
      width: 20%;
    }

    .file-size {
      text-align: center;
      width: 10%;
    }
  }

  .file-item-container {
    display: flex;
    width: 99%;
    margin: 0;
    height: 70px;

    .file-name {
      border: none;
      padding-top: 0;
      margin-bottom: 0;
    }
  }

  .file-item {
    flex-direction: row;
    height: 50px;
    justify-content: unset;
    padding: 15px;
    padding-left: 0;
    margin: 0;
    width: calc(80% - 30px);

    &:hover {
      background-color: unset;
    }

    .selection-checkbox {
      top: 12px;
    }

    .file-name {
      max-width: 285px;
    }
  }

  .modified-date {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8em;
    width: calc(20%);
  }

  .size {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8em;
    width: calc(10%);
  }
}

.empty-folder {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}
