

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// img,
// svg {
//   vertical-align: middle;
// }

.fm-modal {
  border: 1px solid #c6c6c6;
  border-radius: 5px;
  width: fit-content;
  overflow-x: hidden;
  padding: 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.253);
}

.w-25 {
  width: 25%;
}

.file-explorer {
  height: 78vh;
  width: 100vh;
  direction: rtl;
  border: 1px solid #24A1DE !important;
  padding: 8px;
  border-radius: 8px !important;

  button {
  }


  position: relative;

  cursor: default;
  // Disable Text Selection on Double Click
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;

  .files-container {
    display: flex;
    height: calc(
      100% - 80px
    ); // Toolbar total height = baseHeight: 35px, padding top + bottom: 10px, border: 1px.

    .navigation-pane {
      width: fit-content;
      z-index: 1;
      padding-top: 8px;
      min-width: 22vh;
      position: relative;

      .sidebar-resize {
        position: absolute;
        left: 0px;
        top: 0px;
        bottom: 0px;
        width: 5px;
        cursor:all-scroll;
        z-index: 10;
        border-left: 1px solid;

        &:hover {
          border-left: 1px solid #1e3a8a;
        }
      }

      .sidebar-dragging {
        border-left: 1px solid #1e3a8a;
      }
    }

    .folders-preview {
      z-index: 2;
      padding-left: 0px;
      padding-left: 0px;
      border-bottom-left-radius: 8px;
    }
  }
}

.close-icon {
  padding: 5px;
  border-radius: 50%;

  &:hover {
    cursor: pointer;
    background-color: rgb(0, 0, 0, 0.07);
  }
}

.action-input {
  border: none;
  border-bottom: 1px solid grey;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  transition: box-shadow 0.3s;
  background-color: #f5f5f5;
  padding: 6px 9px;
  margin-right: 8px;
  width: calc(100% - 18px);

  &:focus {
    outline: none;
  }
}

.fm-rename-folder-container {
  padding: 8px 0;

  .fm-rename-folder-input {
    border-bottom: 1px solid #c6c6c6;
    padding: 8px 12px 12px;

    .fm-rename-warning {
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }

  .fm-rename-folder-action {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    padding: 8px 8px 0 0;
  }
}

.file-selcted {
  .select-files {
    width: 40%;
  }
}
