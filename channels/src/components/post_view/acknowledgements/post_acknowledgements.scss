@use 'utils/mixins';
@use 'utils/variables';

.AcknowledgementButton {
    @include mixins.button-xsmall;

    padding: 0;
    display: flex;
    min-width: 0;
    align-items: center;
    border: 0;
    border-radius: 4px;
    // margin: 4px 0;
    // background: rgba(var(--online-indicator-rgb), 0.08);
    // color: var(--online-indicator);
    font-weight: 600;
    transition: all 0.15s ease-out;

    > svg {
        // margin-right: 3px;
        // fill: #333333ba;
    }

    &--default {
        // padding-right: 9px;
        background-color: var(--sofa-color);
        color: #3db887;
    }

    &--disabled,
    &:disabled {
        background: transparent;
        cursor: default;
    }

    // &:hover:enabled {
    //     background: rgba(var(--online-indicator-rgb), 0.12);
    // }

    // &:active:enabled {
    //     background: rgba(var(--online-indicator-rgb), 0.16);
    // }

    &--acked {
        background: rgb(var(--button-bg-rgb));
        color: #3db887;

        > svg {
            margin-right: 3px;
            fill: #3db887;
        }

        &.AcknowledgementButton--disabled,
        &.AcknowledgementButton--disabled:hover,
        &:disabled {
            // background: rgb(var(--button-bg-rgb));
            color: #3db887;

            > svg {
                margin-right: 3px;
                fill: #3db887;
            }
        }

        &:active:enabled:not(.AcknowledgementButton--disabled),
        &:hover:enabled:not(.AcknowledgementButton--disabled) {
            background: rgb(var(--online-indicator-rgb));
        }
    }
}

.AcknowledgementButton__divider {
    width: 1px;
    height: 24px;
    margin: 0 4px;
    background: rgba(var(--center-channel-color-rgb), 0.16);
}
