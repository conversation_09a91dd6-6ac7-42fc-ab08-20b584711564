import partition from 'lodash/partition';
import React from 'react';
import type {ReactNode} from 'react';
import {useSelector} from 'react-redux';

import type {GlobalState} from '@mattermost/types/store';

import {getAppBarAppBindings} from 'mattermost-redux/selectors/entities/apps';
import {getAppBarPluginComponents, getChannelHeaderPluginComponents, shouldShowAppBar} from 'selectors/plugins';

import {useCurrentProduct, useCurrentProductId, inScope} from 'utils/products';

import AppBarPluginComponent, {isAppBarPluginComponent} from './app_bar_plugin_component';

import './app_bar.scss';

export default function Videoicon() {
    const appBarPluginComponents = useSelector(getAppBarPluginComponents);
    const currentProduct = useCurrentProduct();
    const currentProductId = useCurrentProductId();

   

    // **تصفية المكونات بحيث تبقى فقط Jitsi**
    const jitsiComponents = appBarPluginComponents.filter(({pluginId}) => pluginId === 'jitsi');
 // إن لم يكن هناك أي مكوّن Jitsi → لا تعرض شيء
    if (!jitsiComponents || jitsiComponents.length === 0) {
        return null;
    }
    const items: ReactNode[] = jitsiComponents.map((x) => {
        if (!x) {
            return null;
        }

        if (isAppBarPluginComponent(x)) {
            if (!inScope(x.supportedProductIds ?? null, currentProductId, currentProduct?.pluginId)) {
                return null;
            }
            return (
                <AppBarPluginComponent
                    key={x.id}
                    component={x}
                />
            );
        }
    });

    return (
        <div className={'noAnimation'}>
            <button
                className='VoicePluginComponent'
                style={{color: '#24A1DE', border: 'none', width: '0'}}
            >
                {items}
            </button>
        </div>
    );
}
