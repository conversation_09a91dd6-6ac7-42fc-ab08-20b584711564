.tooltipContainer {
    z-index: 1070;

    > .tooltipContentContainer {
        z-index: 1070;
        max-width: 220px;
        padding: 4px 8px;
        border-radius: 4px;
        background: var(--sofa-color);
        box-shadow: 0 6px 14px rgba(0, 0, 0, 0.12);
        line-height: 18px;
        pointer-events: none;
        text-align: center;
        word-break: break-word;

        > .tooltipContent {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-family: "GraphikArabic", sans-serif;

            html:lang(ar) & {
                font-family: 'GraphikArabic', sans-serif;
            }

            > .tooltipContentTitleContainer {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                gap: 6px;

                &.isEmojiLarge {
                    flex-direction: column;
                    gap: 2px;

                    > .tooltipContentEmoji {
                        padding-top: 1px;
                    }
                }

                > .tooltipContentEmoji {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                > .tooltipContentTitle {
                    color: #ffffff;
                    font-size: 12px;
                    font-weight: 600;
                    line-height: 15px;
                }
            }

            > .tooltipContentShortcut {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                padding: 4px 0;
                gap: 2px;
            }

            > .tooltipContentHint {
                color: rgba(255, 255, 255, 0.64);
                font-size: 11px;
                font-weight: 600;
                line-height: 16px;
            }
        }

        svg {
            fill: var(--sofa-color)
        }
    }
}
