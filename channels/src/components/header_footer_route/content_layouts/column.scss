// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

.content-team__container {
    position: relative;
    display: flex;
    overflow: hidden;
    max-width: fit-content;
    background-color: #f9fbfb;
    flex: 1;
    flex-direction: column;
    padding: 0px 3px 0 6px;
    margin: 0 auto;
    justify-content: center;
    border-radius: 24px;
    align-items: center;

    .content-layout-column-svg {
        align-self: center;
        padding-top: 20px;
    }

    .content-layout-column-title {
        color: var(--sofa-color);
        font-family: 'GraphikArabic';
        font-size: 40px;
        font-weight: 500;
        letter-spacing: -0.02em;
        line-height: 38px;
        text-align: center;
        padding: 0 10px 0 10px;
    }

    .content-layout-column-message {
        // color: rgba(var(--center-channel-color-rgb), 0.75);
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        text-align: center;
    }

    .layout-extraContent {
        padding: 12px;
        width: 100%;
    }

    .content-layout-column-extra-content {
        display: flex;
        flex: 1;
        margin-top: 30px;
        margin-bottom: 30px;
    }
}

@media screen and (max-width: 699px) {
    .content-team__container {
        width: 100%;
        padding: 0 40px;
    }
}