# [3.1.0](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v3.0.0...v3.1.0) (2025-08-18)


### Features

* **UI:** Arabize the toast message and adjust its color to match the theme CHAT-217 ([b164808](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/b164808ab7400658a2c0b54b6e4ec6897c0515b8))

# [3.0.0](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.18.0...v3.0.0) (2025-08-17)


* Merge branch 'build/release-3' into 'main' ([18e214f](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/18e214fc5ae68ff0941ed199720a50370f1524e2))
* Merge branch 'fix/pipline' into 'main' ([0bdcd99](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/0bdcd9909c23b8f34c15de04129ce3c720c429ad))


### Bug Fixes

* CHAT-197 hide post story dropdown when plugin is inactive ([369cc69](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/369cc697788f52f25dcca1b7ed55322ceccfcac1))
* handle missing username in statuses to prevent unwanted Cocostat activation ([c845fa9](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/c845fa9ea14945a3341fcd3f6a98a2de6c6eadfc))


### Features

* CHAT-111 mark stories as viewed in real-time without page reload ([b2b1763](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/b2b17633e191699d5105057f3b79518a648c9b59))
* CHAT-136 Add support for multi-line in the story text, fix text height, and handle overflow ([a2b8db2](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/a2b8db22e5cf6246223ad9d767b9e3a428c382c4))
* CHAT-167 Add reply functionality with preview support ([8cd9821](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/8cd98214d39e24ba60b80b70f0ade894cfa70014))
* CHAT-170 support jumping to unmounted posts ([ad79448](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/ad79448f07de36076a1ac3c6a15b212dbcac2e84))
* CHAT-172 unify highlight effect for all posts, including linked replies ([d87f3ed](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/d87f3ed5566906dec1fe158977661021d9c447ff))
* CHAT-173 Add media preview display when clicking on file preview ([2dfa20c](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/2dfa20c93de160352a2879a238100266036cbc0b))
* CHAT-180 Add forwarded post indicator ([74842c3](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/74842c31a0499df9761b75ead85669529c2323bc))
* CHAT-73 Implement video editing functionality with video timeline, clipping functionality, and frame extraction ([b637fe6](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/b637fe6fe18bd450b29385bab2e18c174bebe698))
* CHAT-73 Implement video editing functionality with video timeline, clipping functionality, and frame extraction ([8ad1e87](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/8ad1e877a767775f0762107fcfb5f66169db809a))
* **ui:** translate default channel names  CHAT-202 ([5540b3e](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/5540b3e8d5986d9eb3d8e832cf5c4b56d37c09e4))


### BREAKING CHANGES

* move to 3

See merge request sofachat/web/sofachat_frontend_v10.1.6!151
* make marwan learn

See merge request sofachat/web/sofachat_frontend_v10.1.6!150

# [2.18.0](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.17.0...v2.18.0) (2025-07-02)


### Bug Fixes

* solve translation file conflict ([f537a9c](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/f537a9c017cc45c77ca494dfefadd679068fdf07))


### Features

* add Arabic translations for media editor image header and buttons ([c7592e9](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/c7592e9d5b833181e1c90a8282cec1cf6bbba411))
* add initial user status media editor mode; fix the exit button color for the user status view; change the syntax for displaying the full name of the status publisher in the user status view ([26c9520](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/26c95203b11610a5b78654b8d6ac8bbba727c76b))
* enhance media editor with save and cancel functionality; update styles and translations for image editing ([0e3ef72](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/0e3ef721c80893ef38d151f08b9a9d5c9661dc6b))

# [2.17.0](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.16.0...v2.17.0) (2025-07-02)


### Features

* Add support for forwarding posts CHAT-54 ([23d4cdf](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/23d4cdf18ba3e104dcf46b6b2d9068b60faf82e2))
* preserve consecutive blank lines in messages CHAT-55 ([63e8e5e](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/63e8e5e0d8607511719f397f676b2f944d4be558))

# [2.16.0](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.15.0...v2.16.0) (2025-06-24)


### Features

* add team-based visibility control for user status ([7506874](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/75068744183bab3eb6118e31433361fca1efaa32))

# [2.15.0](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.14.0...v2.15.0) (2025-06-23)


### Features

* implement user status preview functionality; update styles and component structure for replied status display ([1472681](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/14726814de989dd210640b11900ecf7eacfdf7bf))

# [2.14.0](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.13.0...v2.14.0) (2025-06-23)


### Features

* add user status muting functionality; enhance sidebar with muted status display; extract the user status border into its own component ([73b9b15](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/73b9b1527cdef75c374d7b3b62470808ae44ef9f))

# [2.13.0](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.12.0...v2.13.0) (2025-05-20)


### Features

* return replied status preview component and enhance the layout ([ea80e7c](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/ea80e7c175579e724083af6e8e021a2bc7177bc7))

# [2.12.0](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.11.0...v2.12.0) (2025-05-19)


### Features

* add user text background color. fix realtime status uploadig. add status text length counter ([68a5bd4](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/68a5bd4faba6b0564841841b7d19fc635337b632))

# [2.11.0](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.10.0...v2.11.0) (2025-05-10)


### Features

* add trigger_parent_pipeline job to build docker img ([30c2ec1](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/30c2ec11e42b8746e4d64b6f5b12e664864a7ed0))

## [2.10.2](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.10.1...v2.10.2) (2025-05-10)


### Bug Fixes

* Edit .releaserc change lable ([9c829a8](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/9c829a872ea66c487b769fcad8e7aeacdbf4ee79))

## [2.10.1](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.10.0...v2.10.1) (2025-05-10)


### Bug Fixes

* dont set lable in .releaserc ([72d7f9f](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/72d7f9f236d7ead704d34e5812c151da94c6172a))
* Edit .releaserc ([846d2aa](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/846d2aa619a19f2a813e33ca178e69b24e06cc28))
* Edit .releaserc add generic ([bb9421a](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/bb9421a23019ac934cdb5c13eac1e57e0d3eb9bc))
* Edit .releaserc to add generic_package ([b5dda0b](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/b5dda0b82fe24c8643984b51ad98a6b8c19f9c93))

## [2.10.1](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.10.0...v2.10.1) (2025-05-10)


### Bug Fixes

* dont set lable in .releaserc ([72d7f9f](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/72d7f9f236d7ead704d34e5812c151da94c6172a))
* Edit .releaserc ([846d2aa](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/846d2aa619a19f2a813e33ca178e69b24e06cc28))
* Edit .releaserc add generic ([bb9421a](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/bb9421a23019ac934cdb5c13eac1e57e0d3eb9bc))
* Edit .releaserc to add generic_package ([b5dda0b](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/b5dda0b82fe24c8643984b51ad98a6b8c19f9c93))

## [2.10.1](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.10.0...v2.10.1) (2025-05-10)


### Bug Fixes

* dont set lable in .releaserc ([72d7f9f](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/72d7f9f236d7ead704d34e5812c151da94c6172a))
* Edit .releaserc ([846d2aa](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/846d2aa619a19f2a813e33ca178e69b24e06cc28))
* Edit .releaserc add generic ([bb9421a](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/bb9421a23019ac934cdb5c13eac1e57e0d3eb9bc))
* Edit .releaserc to add generic_package ([b5dda0b](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/b5dda0b82fe24c8643984b51ad98a6b8c19f9c93))

## [2.10.1](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.10.0...v2.10.1) (2025-05-08)


### Bug Fixes

* Edit .releaserc ([846d2aa](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/846d2aa619a19f2a813e33ca178e69b24e06cc28))
* Edit .releaserc add generic ([bb9421a](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/bb9421a23019ac934cdb5c13eac1e57e0d3eb9bc))
* Edit .releaserc to add generic_package ([b5dda0b](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/b5dda0b82fe24c8643984b51ad98a6b8c19f9c93))

## [2.10.2](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.10.1...v2.10.2) (2025-05-08)


### Bug Fixes

* Edit .releaserc add generic ([bb9421a](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/bb9421a23019ac934cdb5c13eac1e57e0d3eb9bc))

## [2.10.1](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.10.0...v2.10.1) (2025-05-08)


### Bug Fixes

* Edit .releaserc to add generic_package ([b5dda0b](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/b5dda0b82fe24c8643984b51ad98a6b8c19f9c93))

# [2.10.0](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.9.0...v2.10.0) (2025-05-07)


### Features

* add user status viewers functionality ([914c04f](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/914c04fd07d0274da68fc66cf5bfb02c76a05f1b))

# [2.9.0](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.8.0...v2.9.0) (2025-05-07)


### Features

* implement user status viewers functionality ([25de0b7](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/25de0b76670169417e65a1dd3448b98185b877be))

# [2.8.0](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.7.0...v2.8.0) (2025-05-06)


### Features

* integrate WebSocket for real-time user status updates in sidebar components ([0503135](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/0503135306cf489ad5711b8d3e58807c6d0a0680))

# [2.7.0](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.6.0...v2.7.0) (2025-05-05)


### Features

* implement user status viewers functionality ([a6f3eeb](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/a6f3eeb701affa9475b331ef19bd16b91ecb56d4))

# [2.6.0](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.5.0...v2.6.0) (2025-05-04)


### Features

* add replied status preview component ([bb658a7](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/bb658a719c5c1b64b5fd7510dc4bce1b87682246))

# [2.5.0](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.4.3...v2.5.0) (2025-05-04)


### Features

* combine all users status in one modal ([701fa47](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/701fa47a3c43c0810ca77f570575fc6b6e95532a))

## [2.4.3](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.4.2...v2.4.3) (2025-04-17)


### Bug Fixes

* Allow Git to trust the CI project directory ([4a66b4e](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/4a66b4e5fbccfb96b5b145ca2cf09836ab49e09a))
* change apt to apk ([54adeb5](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/54adeb5526c2ba12c5fb541d09175339d14a850e))
* change cert path ([15eb954](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/15eb954dd0c4578c913a3dfe5ddfd347b84c36d3))
* make builds in one stage ([260c704](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/260c704ed336ae47caf4c5b718297eca84a59fb7))
* path in cache ([aa9cd2f](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/aa9cd2f5249c7ea384908edbe78fcf455716ced6))
* reorder stages ([c1ba8ac](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/c1ba8ac7da57483aa41350cdba2f7420a3725047))
* stop ruls doe sonarqube ([f6b5255](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/f6b525571684c3de6004b5c2d0496ef1872e6369))
* test sonarqube-vulnerability-report ([a7fd7a6](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/a7fd7a6d26a3450513c9db6df1ce7adf441a273c))
* The update-ca-certificates step is skipped entirely ([51c772f](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/51c772fcca7ecb03fcf3293894538b1fdb661c29))
* try run sonar jobs in root ([511d7cb](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/511d7cb97116174d0f36100c98961a100ca2a737))
* try run sonar jobs in root ([bbd43ac](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/bbd43ac95ee192db1726a2572da9ba8f82b9b06a))

## [2.4.2](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.4.1...v2.4.2) (2025-03-24)


### Bug Fixes

* disable versions check from the internet, remove the "Learn More" button ([c152d05](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/c152d054a3160f5ce61a8173db035be070a215bd))

## [2.4.1](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.4.0...v2.4.1) (2025-03-23)


### Bug Fixes

* ensure current time is always synced on audio load ([acb1dff](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/acb1dff9f2ae3b554ffdd6501351ad1e0d1d0b0a))

# [2.4.0](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.3.1...v2.4.0) (2025-03-23)


### Features

* enhance audio player functionality and UI controls ([dec6955](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/dec6955576defc072844df68b8136417515fba49))

## [2.3.1](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.3.0...v2.3.1) (2025-03-23)


### Bug Fixes

* git fetch to be forced ([37c5e72](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/37c5e727b7c5728156c812e1a04dd091ce3b975c))

# [2.3.0](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.2.0...v2.3.0) (2025-03-20)


### Bug Fixes

* add installation semantic-release ([6baca2d](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/6baca2d674cc7cd768014316f63994632324c48c))
* disable rule ([9275098](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/9275098bbf625cbb3c5ef55a2021de5627c7c566))
* job utl in the release ([27c01c2](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/27c01c2da8850a10970c46f7cad664fed503785c))
* mute audio player by default to avoide the echo ([74e3067](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/74e3067bfbf0dcb523269fc12c7a782730c7eebb))
* test 1 ([36d2c91](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/36d2c91dbc212fb93d7af6a20add5fdcd2761316))


### Features

* add bump-version to .gitlab-ci.yml file ([f2c9b0f](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/f2c9b0f5a07c1914d61ccd98ae82e4f2064a18c6))
* add bump-version to .gitlab-ci.yml file ([04000d9](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/04000d9b0a5802f5b9ddd13963391d75e5d28160))
* add bump-version to .gitlab-ci.yml file ([b5b2917](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/b5b2917fc0352ad9870e04561a486bbef8f3210c))
* add bump-version to .gitlab-ci.yml file ([574fa4e](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/574fa4e2c986c5ce0d5c12a566d59e05d2959017))
* add bump-version to .gitlab-ci.yml file ([7194100](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/71941005aa3197e7ba7170c4da9858698ec40bb8))
* add bump-version to .gitlab-ci.yml file ([75bce7d](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/75bce7d784fdf67837cd808a3abf35f23429bc00))
* add pipeline ([2530b1e](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/2530b1ebbf82489e9e617ea89b577e00dcccb8fb))
* Edit .releaserc ([5037a6f](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/5037a6f53e62ab9e170a36f18e996739db81c063))
* Edit .releaserc ([b595b36](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/b595b3641e9a7b72bf4da3d502cd60940e28009c))
* mod .gitlab-ci.yml file ([b9ed339](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/b9ed33999a95c7fac6ff536439843b8e51f0ca02))
* test release-ci ([a8a6825](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/a8a6825fefc73d39f5e1444f5b346bf730787232))

# [2.3.0](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.2.0...v2.3.0) (2025-03-20)


### Bug Fixes

* add installation semantic-release ([6baca2d](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/6baca2d674cc7cd768014316f63994632324c48c))
* disable rule ([9275098](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/9275098bbf625cbb3c5ef55a2021de5627c7c566))
* job utl in the release ([27c01c2](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/27c01c2da8850a10970c46f7cad664fed503785c))
* mute audio player by default to avoide the echo ([74e3067](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/74e3067bfbf0dcb523269fc12c7a782730c7eebb))
* test 1 ([36d2c91](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/36d2c91dbc212fb93d7af6a20add5fdcd2761316))


### Features

* add bump-version to .gitlab-ci.yml file ([f2c9b0f](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/f2c9b0f5a07c1914d61ccd98ae82e4f2064a18c6))
* add bump-version to .gitlab-ci.yml file ([04000d9](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/04000d9b0a5802f5b9ddd13963391d75e5d28160))
* add bump-version to .gitlab-ci.yml file ([b5b2917](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/b5b2917fc0352ad9870e04561a486bbef8f3210c))
* add bump-version to .gitlab-ci.yml file ([574fa4e](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/574fa4e2c986c5ce0d5c12a566d59e05d2959017))
* add bump-version to .gitlab-ci.yml file ([7194100](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/71941005aa3197e7ba7170c4da9858698ec40bb8))
* add bump-version to .gitlab-ci.yml file ([75bce7d](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/75bce7d784fdf67837cd808a3abf35f23429bc00))
* add pipeline ([2530b1e](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/2530b1ebbf82489e9e617ea89b577e00dcccb8fb))
* Edit .releaserc ([5037a6f](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/5037a6f53e62ab9e170a36f18e996739db81c063))
* Edit .releaserc ([b595b36](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/b595b3641e9a7b72bf4da3d502cd60940e28009c))
* test release-ci ([a8a6825](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/a8a6825fefc73d39f5e1444f5b346bf730787232))

# [2.3.0](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.2.0...v2.3.0) (2025-03-20)


### Bug Fixes

* add installation semantic-release ([6baca2d](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/6baca2d674cc7cd768014316f63994632324c48c))
* disable rule ([9275098](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/9275098bbf625cbb3c5ef55a2021de5627c7c566))
* job utl in the release ([27c01c2](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/27c01c2da8850a10970c46f7cad664fed503785c))
* mute audio player by default to avoide the echo ([74e3067](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/74e3067bfbf0dcb523269fc12c7a782730c7eebb))
* test 1 ([36d2c91](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/36d2c91dbc212fb93d7af6a20add5fdcd2761316))


### Features

* add bump-version to .gitlab-ci.yml file ([f2c9b0f](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/f2c9b0f5a07c1914d61ccd98ae82e4f2064a18c6))
* add bump-version to .gitlab-ci.yml file ([04000d9](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/04000d9b0a5802f5b9ddd13963391d75e5d28160))
* add bump-version to .gitlab-ci.yml file ([b5b2917](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/b5b2917fc0352ad9870e04561a486bbef8f3210c))
* add bump-version to .gitlab-ci.yml file ([574fa4e](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/574fa4e2c986c5ce0d5c12a566d59e05d2959017))
* add bump-version to .gitlab-ci.yml file ([7194100](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/71941005aa3197e7ba7170c4da9858698ec40bb8))
* add bump-version to .gitlab-ci.yml file ([75bce7d](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/75bce7d784fdf67837cd808a3abf35f23429bc00))
* add pipeline ([2530b1e](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/2530b1ebbf82489e9e617ea89b577e00dcccb8fb))
* Edit .releaserc ([b595b36](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/b595b3641e9a7b72bf4da3d502cd60940e28009c))
* test release-ci ([a8a6825](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/a8a6825fefc73d39f5e1444f5b346bf730787232))

# [2.3.0](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.2.0...v2.3.0) (2025-03-20)


### Bug Fixes

* add installation semantic-release ([6baca2d](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/6baca2d674cc7cd768014316f63994632324c48c))
* disable rule ([9275098](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/9275098bbf625cbb3c5ef55a2021de5627c7c566))
* job utl in the release ([27c01c2](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/27c01c2da8850a10970c46f7cad664fed503785c))
* mute audio player by default to avoide the echo ([74e3067](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/74e3067bfbf0dcb523269fc12c7a782730c7eebb))
* test 1 ([36d2c91](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/36d2c91dbc212fb93d7af6a20add5fdcd2761316))


### Features

* add bump-version to .gitlab-ci.yml file ([04000d9](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/04000d9b0a5802f5b9ddd13963391d75e5d28160))
* add bump-version to .gitlab-ci.yml file ([b5b2917](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/b5b2917fc0352ad9870e04561a486bbef8f3210c))
* add bump-version to .gitlab-ci.yml file ([574fa4e](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/574fa4e2c986c5ce0d5c12a566d59e05d2959017))
* add bump-version to .gitlab-ci.yml file ([7194100](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/71941005aa3197e7ba7170c4da9858698ec40bb8))
* add bump-version to .gitlab-ci.yml file ([75bce7d](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/75bce7d784fdf67837cd808a3abf35f23429bc00))
* add pipeline ([2530b1e](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/2530b1ebbf82489e9e617ea89b577e00dcccb8fb))
* test release-ci ([a8a6825](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/a8a6825fefc73d39f5e1444f5b346bf730787232))

# [2.3.0](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/compare/v2.2.0...v2.3.0) (2025-03-18)


### Bug Fixes

* add installation semantic-release ([6baca2d](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/6baca2d674cc7cd768014316f63994632324c48c))
* mute audio player by default to avoide the echo ([74e3067](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/74e3067bfbf0dcb523269fc12c7a782730c7eebb))


### Features

* add pipeline ([2530b1e](https://git.sofa.io/sofachat/web/sofachat_frontend_v10.1.6/commit/2530b1ebbf82489e9e617ea89b577e00dcccb8fb))
