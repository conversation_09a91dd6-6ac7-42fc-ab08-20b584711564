default:
  image: node:22-bullseye-slim
  services:
    - name: node:22-bullseye-slim
  before_script:
    # Install CA certificate and configure Git SSL
    - mkdir -p /usr/local/share/ca-certificates
    - apt-get update && apt-get install -y --no-install-recommends make git-core ca-certificates
    - echo "$CA_CERTIFICATE" > /usr/local/share/ca-certificates/git.sofa.io.crt
    - update-ca-certificates
    - git config --global http.sslCAInfo /etc/ssl/certs/ca-certificates.crt
    - git config --global credential.helper store
    - git fetch --prune --tags --force
    - echo "https://gitlab-ci-token:${GITLAB_TOKEN}@git.sofa.io" > ~/.git-credentials


# CI/CD variables are: CA_CERTIFICATE, CONTAINER_REGISTRY_USER, GITLAB_TOKEN 
variables:
    DOCKER_TLS_CERTDIR: "/etc/certs"
    CONTAINER_REGISTRY: git.sofa.io:4567
    IMAGE_PATH: sofaerp/web/sofaerp-fullstack
    DOCKER_IMAGE: ${CONTAINER_REGISTRY}/${IMAGE_PATH}
    DOCKERFILE: Dockerfile.python
    NODE_EXTRA_CA_CERTS: /usr/local/share/ca-certificates/git.sofa.io.crt
    

stages:
  - build
  - sonarqube-vulnerability-report
  - release



build-package:
  stage: build
  tags:
    - docker-build
  script:
    - echo "TEST_BUILD_JOB_ID=$CI_JOB_ID" >> variables.env
    - npm i
    - make package
  artifacts:
    paths:
      - Telegram-webapp.tar.gz
    reports:
      dotenv: variables.env
    expire_in: 2 hours



# build-sonar:
#   stage: build
#   image:
#     name: sonarsource/sonar-scanner-cli:11
#     entrypoint: ["/bin/sh", "-c"]
#   variables:
#     SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"
#     GIT_DEPTH: "0"
#     CERT_DIR: "${CI_PROJECT_DIR}/certs"  # Project-specific cert directory
#   before_script:
#     # Write custom CA certificate to project directory
#     - mkdir -p "${CERT_DIR}"
#     - echo "$CA_CERTIFICATE" > "${CERT_DIR}/git.sofa.io.crt"
#     # Allow Git to trust the CI project directory
#     - git config --global --add safe.directory "${CI_PROJECT_DIR}"
#     # Configure Git to trust the custom CA
#     - git config --global http.sslCAInfo "${CERT_DIR}/git.sofa.io.crt"
#     - git config --global credential.helper store
#     # Fetch code with SSL verification
#     - git fetch --prune --tags --force
#     - echo "https://gitlab-ci-token:${GITLAB_TOKEN}@git.sofa.io" > ~/.git-credentials
#   script:
#     - sonar-scanner -Dsonar.host.url="${SONAR_HOST_URL}"
#   cache:
#     policy: pull-push
#     key: "sonar-cache-$CI_COMMIT_REF_SLUG"
#     paths:
#       - "${SONAR_USER_HOME}/cache"
#       - sonar-scanner/
#   allow_failure: true
#   rules:
#     - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
#     - if: $CI_COMMIT_BRANCH == 'master'
#     - if: $CI_COMMIT_BRANCH == 'main'
#     - if: $CI_COMMIT_BRANCH == 'develop'



# sonarqube-vulnerability-report:
#   stage: sonarqube-vulnerability-report
#   image: 
#     name: sonarsource/sonar-scanner-cli:11
#     entrypoint: ["/bin/sh", "-c"]
#   variables:
#     SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
#     GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
#     CERT_DIR: "${CI_PROJECT_DIR}/certs"
#   before_script:
#     # Write custom CA certificate to project directory
#     - mkdir -p "${CERT_DIR}"
#     - echo "$CA_CERTIFICATE" > "${CERT_DIR}/git.sofa.io.crt"
#     # Allow Git to trust the CI project directory
#     - git config --global --add safe.directory "${CI_PROJECT_DIR}"
#     # Configure Git to trust the custom CA
#     - git config --global http.sslCAInfo "${CERT_DIR}/git.sofa.io.crt"
#     - git config --global credential.helper store
#     # Fetch code with SSL verification
#     - git fetch --prune --tags --force
#     - echo "https://gitlab-ci-token:${GITLAB_TOKEN}@git.sofa.io" > ~/.git-credentials
#   script:
#     - 'curl -u "${SONAR_TOKEN}:" "${SONAR_HOST_URL}/api/issues/gitlab_sast_export?projectKey=sofachat_web_sofachat_frontend_v10.1.6_9c680309-f514-49a1-a58f-534f51054dd9&branch=${CI_COMMIT_BRANCH}&pullRequest=${CI_MERGE_REQUEST_IID}" -o gl-sast-sonar-report.json'
#   allow_failure: true
#   rules:
#     - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
#     - if: $CI_COMMIT_BRANCH == 'master'
#     - if: $CI_COMMIT_BRANCH == 'main'
#     - if: $CI_COMMIT_BRANCH == 'develop'
#   artifacts:
#     expire_in: 1 day
#     reports:
#       sast: gl-sast-sonar-report.json



bump-version:
  stage: release
  image: node:22-bullseye-slim
  needs: 
    - build-package
  services:
    - name: node:22-bullseye-slim
  before_script:
    
    # Install CA certificate and configure Git SSL
    - mkdir -p /usr/local/share/ca-certificates
    - apt-get update && apt-get install -y --no-install-recommends git-core ca-certificates
    - echo "$CA_CERTIFICATE" > /usr/local/share/ca-certificates/git.sofa.io.crt
    - update-ca-certificates
    - git config --global http.sslCAInfo /etc/ssl/certs/ca-certificates.crt
    - git config --global credential.helper store
    - git fetch --prune --tags
    - echo "https://gitlab-ci-token:${GITLAB_TOKEN}@git.sofa.io" > ~/.git-credentials
    
    # install semantic-release
    - npm install -g semantic-release @semantic-release/gitlab @semantic-release/changelog @semantic-release/git
  tags:
    - docker-build
  only:
    - main
  variables:
    GIT_DEPTH: 0
  script:
    - OLD_VERSION=$(git describe --tags --abbrev=0 | sed 's/^v//')
    - semantic-release
    # Capture the new version created by semantic-release, stripping the 'v' prefix
    - NEW_VERSION=$(git describe --tags --abbrev=0 | sed 's/^v//')
    - echo "NEW_VERSION=$NEW_VERSION" > version.env
    - |
      if [ "$OLD_VERSION" == "$NEW_VERSION" ]; then
        echo "OLD_VERSION -> $OLD_VERSION"
        echo "NEW_VERSION -> $NEW_VERSION"
        echo "Versions are the same. Exiting with status 1."
        exit 1
      fi



trigger_parent_pipeline:
  stage: .post  # Run after all stages
  image: alpine:latest
  # rules:
  #   - if: $CI_COMMIT_TAG  # Trigger only after a tagged release
  only:
    - main
  before_script:
    - mkdir -p /usr/local/share/ca-certificates
    - echo "$CA_CERTIFICATE" > /usr/local/share/ca-certificates/git.sofa.io.crt
    - apk update && apk add --no-cache git curl ca-certificates jq && update-ca-certificates

  script:
    - |
      # Use space-separated branches (ash-compatible)
      BRANCHES="main ci/add-ci"
      for branch in $BRANCHES; do
        curl --request POST \
          --form "token=$PARENT_TRIGGER_TOKEN" \
          --form "ref=$branch" \
          --form "variables[SUB_MODULE_RELEASE_TAG]=$CI_COMMIT_TAG" \
          "https://git.sofa.io/api/v4/projects/$PARENT_PROJECT_ID/trigger/pipeline"
      done



