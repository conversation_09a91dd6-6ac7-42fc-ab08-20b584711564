{"branches": ["ci/add-pipeline", "main"], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", ["@semantic-release/git", {"assets": ["CHANGELOG.md"], "message": "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}"}], ["@semantic-release/gitlab", {"gitlabUrl": "https://git.sofa.io", "assets": [{"path": "Sofachat-webapp.tar.gz", "label": "Sofachat-webapp.tar.gz", "target": "generic_package", "type": "package"}]}]]}